Metadata-Version: 2.4
Name: aliyun-python-sdk-core
Version: 2.16.0
Summary: The core module of Aliyun Python SDK.
Home-page: https://github.com/aliyun/aliyun-openapi-python-sdk
Author: Alibaba Cloud
Author-email: <EMAIL>
License: Apache License 2.0
Keywords: aliyun,sdk,core
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development
Requires-Python: >=3.7
License-File: LICENSE
Requires-Dist: jmespath<1.0.0,>=0.9.3
Requires-Dist: cryptography>=3.0.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

======================
aliyun-python-sdk-core
======================


This is the core module of Aliyun Python SDK.

Aliyun Python SDK is the official software development kit. It makes things easy to integrate your Python application,
library, or script with Aliyun services.

This module works on Python versions:

   * 2.7 and greater


Documentation:

Please visit http://develop.aliyun.com/sdk/python
