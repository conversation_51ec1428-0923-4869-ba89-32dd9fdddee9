Metadata-Version: 2.1
Name: aliyun-python-sdk-kms
Version: 2.16.5
Summary: The kms module of Aliyun Python sdk.
Home-page: http://develop.aliyun.com/sdk/python
Author: <PERSON>yun
Author-email: <EMAIL>
License: Apache
Keywords: aliyun,sdk,kms
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Software Development
Requires-Dist: aliyun-python-sdk-core >=2.11.5

=============================================================
aliyun-python-sdk-kms
=============================================================

.. This is the kms module of Aliyun Python SDK.

Aliyun Python SDK is the official software development kit. It makes things easy to integrate your Python application, library, or script with Aliyun services.

This module works on Python versions:

2.6.5 and greater

**Documentation:**

Please visit `http://develop.aliyun.com/sdk/python <http://develop.aliyun.com/sdk/python>`_


