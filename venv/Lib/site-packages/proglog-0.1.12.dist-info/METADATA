Metadata-Version: 2.4
Name: proglog
Version: 0.1.12
Summary: Log and progress bar manager for console, notebooks, web...
Author: Zulko
License-Expression: MIT
Project-URL: Homepage, https://github.com/Edinburgh-Genome-Foundry/proglog
Keywords: logger,log,progress,bar
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: tqdm
Dynamic: license-file

Proglog
=======

Proglog is a progress logging system for Python. It allows to build complex
libraries while giving the user control on the management of logs, callbacks and progress bars.


Infos
-----

**PIP installation:**

.. code:: bash

  pip install proglog

**Github Page:** `<https://github.com/Edinburgh-Genome-Foundry/Proglog>`_

**License:** MIT

Copyright 2017 Edinburgh Genome Foundry, University of Edinburgh
