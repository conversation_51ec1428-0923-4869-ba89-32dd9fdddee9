/*! For license information please see 6950.70fe55c2.chunk.js.LICENSE.txt */
(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[6950],{97943:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))}));l.displayName="Add"},57463:(e,t,n)=>{"use strict";n.d(t,{H:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M16 9v10H8V9h8m-1.5-6h-5l-1 1H5v2h14V4h-3.5l-1-1zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7z"}))}));l.displayName="Delete"},41342:(e,t,n)=>{"use strict";n.d(t,{k:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("rect",{width:24,height:24,fill:"none"}),i.createElement("path",{d:"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4l-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z"}))}));l.displayName="FileDownload"},34367:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"}))}));l.displayName="Fullscreen"},31011:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}))}));l.displayName="FullscreenExit"},17875:(e,t,n)=>{"use strict";n.d(t,{o:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))}));l.displayName="Search"},82218:(e,t,n)=>{"use strict";n.d(t,{d:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},e,{ref:t}),i.createElement("path",{d:"M0 0v4l1.5-1.5L3 4l1-1-1.5-1.5L4 0H0zm5 4L4 5l1.5 1.5L4 8h4V4L6.5 5.5 5 4z"}))}));l.displayName="FullscreenEnter"},97781:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var o=n(25773),i=n(66845),r=n(69),l=i.forwardRef((function(e,t){return i.createElement(r.D,(0,o.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},e,{ref:t}),i.createElement("path",{d:"M1 0L0 1l1.5 1.5L0 4h4V0L2.5 1.5 1 0zm3 4v4l1.5-1.5L7 8l1-1-1.5-1.5L8 4H4z"}))}));l.displayName="FullscreenExit"},74559:(e,t,n)=>{"use strict";n.d(t,{z:()=>a});var o=n(60484),i=n(66845),r=function(){const e=Array.prototype.slice.call(arguments).filter(Boolean),t={},n=[];e.forEach((e=>{(e?e.split(" "):[]).forEach((e=>{if(e.startsWith("atm_")){const[,n]=e.split("_");t[n]=e}else n.push(e)}))}));const o=[];for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&o.push(t[i]);return o.push(...n),o.join(" ")},l=(e,t)=>{const n={};return Object.keys(e).filter((e=>t=>-1===e.indexOf(t))(t)).forEach((t=>{n[t]=e[t]})),n};var a=function(e){let t="";return n=>{const a=(a,s)=>{const{as:c=e,class:u=t}=a;var d;const h=function(e,t,n){const i=l(t,n);if(!e){const e="function"===typeof o.Z?{default:o.Z}:o.Z;Object.keys(i).forEach((t=>{e.default(t)||delete i[t]}))}return i}(void 0===n.propsAsIs?!("string"===typeof c&&-1===c.indexOf("-")&&(d=c[0],d.toUpperCase()!==d)):n.propsAsIs,a,["as","class"]);h.ref=s,h.className=n.atomic?r(n.class,h.className||u):r(h.className||u,n.class);const{vars:f}=n;if(f){const e={};for(const i in f){const t=f[i],o=t[0],r=t[1]||"",l="function"===typeof o?o(a):o;n.name,e["--".concat(i)]="".concat(l).concat(r)}const t=h.style||{},o=Object.keys(t);o.length>0&&o.forEach((n=>{e[n]=t[n]})),h.style=e}return e.__linaria&&e!==c?(h.as=c,i.createElement(e,h)):i.createElement(c,h)},s=i.forwardRef?i.forwardRef(a):e=>{const t=l(e,["innerRef"]);return a(t,e.innerRef)};return s.displayName=n.name,s.__linaria={className:n.class||t,extends:e},s}}},29916:e=>{e.exports=function(e,t,n,o){for(var i=-1,r=null==e?0:e.length;++i<r;){var l=e[i];t(o,l,n(l),e)}return o}},70199:(e,t,n)=>{var o=n(64811);e.exports=function(e,t,n,i){return o(e,(function(e,o,r){t(i,e,n(e),r)})),i}},18471:e=>{e.exports=function(e,t,n){return e===e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}},48003:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e,n){return null!=e&&t.call(e,n)}},34768:(e,t,n)=>{var o=n(89632),i=n(10636),r=n(19774),l=n(7086),a=n(58938),s=n(21278);e.exports=function(e,t,n){var c=-1,u=i,d=e.length,h=!0,f=[],p=f;if(n)h=!1,u=r;else if(d>=200){var v=t?null:a(e);if(v)return s(v);h=!1,u=l,p=new o}else p=t?[]:f;e:for(;++c<d;){var g=e[c],m=t?t(g):g;if(g=n||0!==g?g:0,h&&m===m){for(var w=p.length;w--;)if(p[w]===m)continue e;t&&p.push(m),f.push(g)}else u(p,m,n)||(p!==f&&p.push(m),f.push(g))}return f}},98718:(e,t,n)=>{var o=n(29916),i=n(70199),r=n(48603),l=n(12697);e.exports=function(e,t){return function(n,a){var s=l(n)?o:i,c=t?t():{};return s(n,e,r(a,2),c)}}},58938:(e,t,n)=>{var o=n(64626),i=n(55304),r=n(21278),l=o&&1/r(new o([,-0]))[1]==1/0?function(e){return new o(e)}:i;e.exports=l},51586:(e,t,n)=>{var o=n(18471),i=n(90773);e.exports=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=i(n))===n?n:0),void 0!==t&&(t=(t=i(t))===t?t:0),o(i(e),t,n)}},56797:(e,t,n)=>{var o=n(60506),i=n(3338),r=n(90773),l=Math.max,a=Math.min;e.exports=function(e,t,n){var s,c,u,d,h,f,p=0,v=!1,g=!1,m=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function w(t){var n=s,o=c;return s=c=void 0,p=t,d=e.apply(o,n)}function b(e){var n=e-f;return void 0===f||n>=t||n<0||g&&e-p>=u}function y(){var e=i();if(b(e))return x(e);h=setTimeout(y,function(e){var n=t-(e-f);return g?a(n,u-(e-p)):n}(e))}function x(e){return h=void 0,m&&s?w(e):(s=c=void 0,d)}function k(){var e=i(),n=b(e);if(s=arguments,c=this,f=e,n){if(void 0===h)return function(e){return p=e,h=setTimeout(y,t),v?w(e):d}(f);if(g)return clearTimeout(h),h=setTimeout(y,t),w(f)}return void 0===h&&(h=setTimeout(y,t)),d}return t=r(t)||0,o(n)&&(v=!!n.leading,u=(g="maxWait"in n)?l(r(n.maxWait)||0,t):u,m="trailing"in n?!!n.trailing:m),k.cancel=function(){void 0!==h&&clearTimeout(h),p=0,s=f=c=h=void 0},k.flush=function(){return void 0===h?d:x(i())},k}},76236:(e,t,n)=>{var o=n(45742),i=n(98718),r=Object.prototype.hasOwnProperty,l=i((function(e,t,n){r.call(e,n)?e[n].push(t):o(e,n,[t])}));e.exports=l},82781:(e,t,n)=>{var o=n(48003),i=n(98869);e.exports=function(e,t){return null!=e&&i(e,t,o)}},55304:e=>{e.exports=function(){}},3338:(e,t,n)=>{var o=n(19661);e.exports=function(){return o.Date.now()}},19266:(e,t,n)=>{var o=n(56797),i=n(60506);e.exports=function(e,t,n){var r=!0,l=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return i(n)&&(r="leading"in n?!!n.leading:r,l="trailing"in n?!!n.trailing:l),o(e,t,{leading:r,maxWait:t,trailing:l})}},17015:(e,t,n)=>{var o=n(34768);e.exports=function(e){return e&&e.length?o(e):[]}},87717:function(e,t,n){var o,i,r,l,a;l=this,a=function(e){var t=!1,n=!1,o=!1,i=!1,r="escape years months weeks days hours minutes seconds milliseconds general".split(" "),l=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function a(e,t){return!(t.length>e.length)&&-1!==e.indexOf(t)}function s(e){for(var t="";e;)t+="0",e-=1;return t}function c(e,t){var n=e+"+"+m(S(t).sort(),(function(e){return e+":"+t[e]})).join(",");return c.cache[n]||(c.cache[n]=Intl.NumberFormat(e,t)),c.cache[n]}function u(e,t,r){var l,a,d,h=t.useToLocaleString,f=t.useGrouping,p=f&&t.grouping.slice(),v=t.maximumSignificantDigits,g=t.minimumIntegerDigits||1,m=t.fractionDigits||0,w=t.groupingSeparator,b=t.decimalSeparator;if(h&&r){var y,x={minimumIntegerDigits:g,useGrouping:f};return m&&(x.maximumFractionDigits=m,x.minimumFractionDigits=m),v&&e>0&&(x.maximumSignificantDigits=v),o?(i||((y=C({},t)).useGrouping=!1,y.decimalSeparator=".",e=parseFloat(u(e,y),10)),c(r,x).format(e)):(n||((y=C({},t)).useGrouping=!1,y.decimalSeparator=".",e=parseFloat(u(e,y),10)),e.toLocaleString(r,x))}var k=(v?e.toPrecision(v+1):e.toFixed(m+1)).split("e");d=k[1]||"",a=(k=k[0].split("."))[1]||"";var S=(l=k[0]||"").length,M=a.length,R=S+M,E=l+a;(v&&R===v+1||!v&&M===m+1)&&((E=function(e){for(var t=e.split("").reverse(),n=0,o=!0;o&&n<t.length;)n?"9"===t[n]?t[n]="0":(t[n]=(parseInt(t[n],10)+1).toString(),o=!1):(parseInt(t[n],10)<5&&(o=!1),t[n]="0"),n+=1;return o&&t.push("1"),t.reverse().join("")}(E)).length===R+1&&(S+=1),M&&(E=E.slice(0,-1)),l=E.slice(0,S),a=E.slice(S)),v&&(a=a.replace(/0*$/,""));var I=parseInt(d,10);I>0?a.length<=I?(l+=a+=s(I-a.length),a=""):(l+=a.slice(0,I),a=a.slice(I)):I<0&&(a=s(Math.abs(I)-l.length)+l+a,l="0"),v||((a=a.slice(0,m)).length<m&&(a+=s(m-a.length)),l.length<g&&(l=s(g-l.length)+l));var T,O="";if(f)for(k=l;k.length;)p.length&&(T=p.shift()),O&&(O=w+O),O=k.slice(-T)+O,k=k.slice(0,-T);else O=l;return a&&(O=O+b+a),O}function d(e,t){return e.label.length>t.label.length?-1:e.label.length<t.label.length?1:0}c.cache={};var h={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:function(e,t,n){return 1===t&&null===n?e:e+e}};function f(e){return"[object Array]"===Object.prototype.toString.call(e)}function p(e){return"[object Object]"===Object.prototype.toString.call(e)}function v(e,t){var n,o=0,i=e&&e.length||0;for("function"!==typeof t&&(n=t,t=function(e){return e===n});o<i;){if(t(e[o]))return e[o];o+=1}}function g(e,t){var n=0,o=e.length;if(e&&o)for(;n<o;){if(!1===t(e[n],n))return;n+=1}}function m(e,t){var n=0,o=e.length,i=[];if(!e||!o)return i;for(;n<o;)i[n]=t(e[n],n),n+=1;return i}function w(e,t){return m(e,(function(e){return e[t]}))}function b(e){var t=[];return g(e,(function(e){e&&t.push(e)})),t}function y(e){var t=[];return g(e,(function(e){v(t,e)||t.push(e)})),t}function x(e,t){var n=[];return g(e,(function(e){g(t,(function(t){e===t&&n.push(e)}))})),y(n)}function k(e,t){var n=[];return g(e,(function(o,i){if(!t(o))return n=e.slice(i),!1})),n}function C(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function S(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}function M(e,t){var n=0,o=e.length;if(!e||!o)return!1;for(;n<o;){if(!0===t(e[n],n))return!0;n+=1}return!1}function R(e){return"3.6"===e(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})}function E(e){var t=!0;return!!(t=(t=(t=t&&"1"===e(1,"en",{minimumIntegerDigits:1}))&&"01"===e(1,"en",{minimumIntegerDigits:2}))&&"001"===e(1,"en",{minimumIntegerDigits:3}))&&!!(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0}))&&"100.0"===e(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1}))&&"99.99"===e(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2}))&&"99.990"===e(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3}))&&!!(t=(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumSignificantDigits:1}))&&"100"===e(99.99,"en",{maximumSignificantDigits:2}))&&"100"===e(99.99,"en",{maximumSignificantDigits:3}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:4}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:5}))&&!!(t=(t=t&&"1,000"===e(1e3,"en",{useGrouping:!0}))&&"1000"===e(1e3,"en",{useGrouping:!1}))}function I(){var e,t=[].slice.call(arguments),n={};if(g(t,(function(t,o){if(!o){if(!f(t))throw"Expected array as the first argument to durationsFormat.";e=t}"string"!==typeof t&&"function"!==typeof t?"number"!==typeof t?p(t)&&C(n,t):n.precision=t:n.template=t})),!e||!e.length)return[];n.returnMomentTypes=!0;var o=m(e,(function(e){return e.format(n)})),i=x(r,y(w(function(e){var t=[];return g(e,(function(e){t=t.concat(e)})),t}(o),"type"))),l=n.largest;return l&&(i=i.slice(0,l)),n.returnMomentTypes=!1,n.outputTypes=i,m(e,(function(e){return e.format(n)}))}function T(){var n=[].slice.call(arguments),i=C({},this.format.defaults),s=this.asMilliseconds(),c=this.asMonths();"function"===typeof this.isValid&&!1===this.isValid()&&(s=0,c=0);var R=s<0,E=e.duration(Math.abs(s),"milliseconds"),I=e.duration(Math.abs(c),"months");g(n,(function(e){"string"!==typeof e&&"function"!==typeof e?"number"!==typeof e?p(e)&&C(i,e):i.precision=e:i.template=e}));var T={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},O={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};i.types=r;var P=function(e){return v(r,(function(t){return O[t].test(e)}))},D=new RegExp(m(r,(function(e){return O[e].source})).join("|"),"g");i.duration=this;var H="function"===typeof i.template?i.template.apply(i):i.template,z=i.outputTypes,L=i.returnMomentTypes,F=i.largest,A=[];z||(f(i.stopTrim)&&(i.stopTrim=i.stopTrim.join("")),i.stopTrim&&g(i.stopTrim.match(D),(function(e){var t=P(e);"escape"!==t&&"general"!==t&&A.push(t)})));var V=e.localeData();V||(V={}),g(S(h),(function(e){"function"!==typeof h[e]?V["_"+e]||(V["_"+e]=h[e]):V[e]||(V[e]=h[e])})),g(S(V._durationTimeTemplates),(function(e){H=H.replace("_"+e+"_",V._durationTimeTemplates[e])}));var _=i.userLocale||e.locale(),N=i.useLeftUnits,B=i.usePlural,Z=i.precision,W=i.forceLength,j=i.useGrouping,U=i.trunc,X=i.useSignificantDigits&&Z>0,G=X?i.precision:0,Y=G,K=i.minValue,$=!1,q=i.maxValue,Q=!1,J=i.useToLocaleString,ee=i.groupingSeparator,te=i.decimalSeparator,ne=i.grouping;J=J&&(t||o);var oe=i.trim;f(oe)&&(oe=oe.join(" ")),null===oe&&(F||q||X)&&(oe="all"),null!==oe&&!0!==oe&&"left"!==oe&&"right"!==oe||(oe="large"),!1===oe&&(oe="");var ie=function(e){return e.test(oe)},re=/both/,le=/^all|[^sm]all/,ae=F>0||M([/large/,re,le],ie),se=M([/small/,re,le],ie),ce=M([/mid/,le],ie),ue=M([/final/,le],ie),de=m(H.match(D),(function(e,t){var n=P(e);return"*"===e.slice(0,1)&&(e=e.slice(1),"escape"!==n&&"general"!==n&&A.push(n)),{index:t,length:e.length,text:"",token:"escape"===n?e.replace(O.escape,"$1"):e,type:"escape"===n||"general"===n?null:n}})),he={index:0,length:0,token:"",text:"",type:null},fe=[];N&&de.reverse(),g(de,(function(e){if(e.type)return(he.type||he.text)&&fe.push(he),void(he=e);N?he.text=e.token+he.text:he.text+=e.token})),(he.type||he.text)&&fe.push(he),N&&fe.reverse();var pe=x(r,y(b(w(fe,"type"))));if(!pe.length)return w(fe,"text").join("");pe=m(pe,(function(e,t){var n,o=t+1===pe.length,r=!t;n="years"===e||"months"===e?I.as(e):E.as(e);var l=Math.floor(n),a=n-l,s=v(fe,(function(t){return e===t.type}));return r&&q&&n>q&&(Q=!0),o&&K&&Math.abs(i.duration.as(e))<K&&($=!0),r&&null===W&&s.length>1&&(W=!0),E.subtract(l,e),I.subtract(l,e),{rawValue:n,wholeValue:l,decimalValue:o?a:0,isSmallest:o,isLargest:r,type:e,tokenLength:s.length}}));var ve,ge=U?Math.floor:Math.round,me=function(e,t){var n=Math.pow(10,t);return ge(e*n)/n},we=!1,be=!1,ye=function(e,t){var n={useGrouping:j,groupingSeparator:ee,decimalSeparator:te,grouping:ne,useToLocaleString:J};return X&&(G<=0?(e.rawValue=0,e.wholeValue=0,e.decimalValue=0):(n.maximumSignificantDigits=G,e.significantDigits=G)),Q&&!be&&(e.isLargest?(e.wholeValue=q,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),$&&!be&&(e.isSmallest?(e.wholeValue=K,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),e.isSmallest||e.significantDigits&&e.significantDigits-e.wholeValue.toString().length<=0?Z<0?e.value=me(e.wholeValue,Z):0===Z?e.value=ge(e.wholeValue+e.decimalValue):X?(e.value=U?me(e.rawValue,G-e.wholeValue.toString().length):e.rawValue,e.wholeValue&&(G-=e.wholeValue.toString().length)):(n.fractionDigits=Z,e.value=U?e.wholeValue+me(e.decimalValue,Z):e.wholeValue+e.decimalValue):X&&e.wholeValue?(e.value=Math.round(me(e.wholeValue,e.significantDigits-e.wholeValue.toString().length)),G-=e.wholeValue.toString().length):e.value=e.wholeValue,e.tokenLength>1&&(W||we)&&(n.minimumIntegerDigits=e.tokenLength,be&&n.maximumSignificantDigits<e.tokenLength&&delete n.maximumSignificantDigits),!we&&(e.value>0||""===oe||v(A,e.type)||v(z,e.type))&&(we=!0),e.formattedValue=u(e.value,n,_),n.useGrouping=!1,n.decimalSeparator=".",e.formattedValueEn=u(e.value,n,"en"),2===e.tokenLength&&"milliseconds"===e.type&&(e.formattedValueMS=u(e.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),e};if((pe=b(pe=m(pe,ye))).length>1){var xe=function(e){return v(pe,(function(t){return t.type===e}))};g(l,(function(e){var t=xe(e.type);t&&g(e.targets,(function(e){var n=xe(e.type);n&&parseInt(t.formattedValueEn,10)===e.value&&(t.rawValue=0,t.wholeValue=0,t.decimalValue=0,n.rawValue+=1,n.wholeValue+=1,n.decimalValue=0,n.formattedValueEn=n.wholeValue.toString(),be=!0)}))}))}return be&&(we=!1,G=Y,pe=b(pe=m(pe,ye))),!z||Q&&!i.trim?(ae&&(pe=k(pe,(function(e){return!e.isSmallest&&!e.wholeValue&&!v(A,e.type)}))),F&&pe.length&&(pe=pe.slice(0,F)),se&&pe.length>1&&(ve=function(e){return!e.wholeValue&&!v(A,e.type)&&!e.isLargest},pe=k(pe.slice().reverse(),ve).reverse()),ce&&(pe=b(pe=m(pe,(function(e,t){return t>0&&t<pe.length-1&&!e.wholeValue?null:e})))),!ue||1!==pe.length||pe[0].wholeValue||!U&&pe[0].isSmallest&&pe[0].rawValue<K||(pe=[])):pe=b(pe=m(pe,(function(e){return v(z,(function(t){return e.type===t}))?e:null}))),L?pe:(g(fe,(function(e){var t=T[e.type],n=v(pe,(function(t){return t.type===e.type}));if(t&&n){var o=n.formattedValueEn.split(".");o[0]=parseInt(o[0],10),o[1]?o[1]=parseFloat("0."+o[1],10):o[1]=null;var i=V.durationPluralKey(t,o[0],o[1]),r=function(e,t){var n=[];return g(S(t),(function(o){if("_durationLabels"===o.slice(0,15)){var i=o.slice(15).toLowerCase();g(S(t[o]),(function(r){r.slice(0,1)===e&&n.push({type:i,key:r,label:t[o][r]})}))}})),n}(t,V),l=!1,s={};g(V._durationLabelTypes,(function(t){var n=v(r,(function(e){return e.type===t.type&&e.key===i}));n&&(s[n.type]=n.label,a(e.text,t.string)&&(e.text=e.text.replace(t.string,n.label),l=!0))})),B&&!l&&(r.sort(d),g(r,(function(t){return s[t.type]===t.label?!a(e.text,t.label)&&void 0:a(e.text,t.label)?(e.text=e.text.replace(t.label,s[t.type]),!1):void 0})))}})),(fe=m(fe,(function(e){if(!e.type)return e.text;var t=v(pe,(function(t){return t.type===e.type}));if(!t)return"";var n="";return N&&(n+=e.text),(R&&Q||!R&&$)&&(n+="< ",Q=!1,$=!1),(R&&$||!R&&Q)&&(n+="> ",Q=!1,$=!1),R&&(t.value>0||""===oe||v(A,t.type)||v(z,t.type))&&(n+="-",R=!1),"milliseconds"===e.type&&t.formattedValueMS?n+=t.formattedValueMS:n+=t.formattedValue,N||(n+=e.text),n}))).join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function O(){var e=this.duration,t=function(t){return e._data[t]},n=v(this.types,t),o=function(e,t){for(var n=e.length;n-=1;)if(t(e[n]))return e[n]}(this.types,t);switch(n){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(n===o)return"d __";case"weeks":return n===o?"w __":(null===this.trim&&(this.trim="both"),"w __, d __, h __");case"months":if(n===o)return"M __";case"years":return n===o?"y __":(null===this.trim&&(this.trim="both"),"y __, M __, d __");default:return null===this.trim&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function P(e){if(!e)throw"Moment Duration Format init cannot find moment instance.";e.duration.format=I,e.duration.fn.format=T,e.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:O,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},e.updateLocale("en",h)}var D=function(e,t,n){return e.toLocaleString(t,n)};t=function(){try{(0).toLocaleString("i")}catch(e){return"RangeError"===e.name}return!1}()&&E(D),n=t&&R(D);var H=function(e,t,n){if("undefined"!==typeof window&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(t,n).format(e)};return o=E(H),i=o&&R(H),P(e),P},i=[n(53608)],void 0===(r="function"===typeof(o=a)?o.apply(t,i):o)||(e.exports=r),l&&(l.momentDurationFormatSetup=l.moment?a(l.moment):a)},15650:function(e,t,n){var o,i,r;i=[t,n(73074)],o=function(e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=o(t);function o(e){return e&&e.__esModule?e:{default:e}}e.default=n.default},void 0===(r="function"===typeof o?o.apply(t,i):o)||(e.exports=r)},73074:function(e,t,n){var o,i,r;i=[t,n(66845),n(8984)],o=function(e,t,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHasSupportToCaptureOption=f;var o=r(t),i=r(n);function r(e){return e&&e.__esModule?e:{default:e}}var l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e};function a(e,t){var n={};for(var o in e)t.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var c=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function d(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var h=!1;function f(e){h=e}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){f(!0)}}))}catch(m){}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{capture:!0};return h?e:e.capture}function v(e){if("touches"in e){var t=e.touches[0];return{x:t.pageX,y:t.pageY}}return{x:e.screenX,y:e.screenY}}var g=function(e){function t(){var e;s(this,t);for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var r=u(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(o)));return r._handleSwipeStart=r._handleSwipeStart.bind(r),r._handleSwipeMove=r._handleSwipeMove.bind(r),r._handleSwipeEnd=r._handleSwipeEnd.bind(r),r._onMouseDown=r._onMouseDown.bind(r),r._onMouseMove=r._onMouseMove.bind(r),r._onMouseUp=r._onMouseUp.bind(r),r._setSwiperRef=r._setSwiperRef.bind(r),r}return d(t,e),c(t,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(e){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(e))}},{key:"_onMouseMove",value:function(e){this.mouseDown&&this._handleSwipeMove(e)}},{key:"_onMouseUp",value:function(e){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(e)}},{key:"_handleSwipeStart",value:function(e){var t=v(e),n=t.x,o=t.y;this.moveStart={x:n,y:o},this.props.onSwipeStart(e)}},{key:"_handleSwipeMove",value:function(e){if(this.moveStart){var t=v(e),n=t.x,o=t.y,i=n-this.moveStart.x,r=o-this.moveStart.y;this.moving=!0,this.props.onSwipeMove({x:i,y:r},e)&&e.cancelable&&e.preventDefault(),this.movePosition={deltaX:i,deltaY:r}}}},{key:"_handleSwipeEnd",value:function(e){this.props.onSwipeEnd(e);var t=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-t?this.props.onSwipeLeft(1,e):this.movePosition.deltaX>t&&this.props.onSwipeRight(1,e),this.movePosition.deltaY<-t?this.props.onSwipeUp(1,e):this.movePosition.deltaY>t&&this.props.onSwipeDown(1,e)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(e){this.swiper=e,this.props.innerRef(e)}},{key:"render",value:function(){var e=this.props,t=(e.tagName,e.className),n=e.style,i=e.children,r=(e.allowMouseEvents,e.onSwipeUp,e.onSwipeDown,e.onSwipeLeft,e.onSwipeRight,e.onSwipeStart,e.onSwipeMove,e.onSwipeEnd,e.innerRef,e.tolerance,a(e,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]));return o.default.createElement(this.props.tagName,l({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:t,style:n},r),i)}}]),t}(t.Component);g.displayName="ReactSwipe",g.propTypes={tagName:i.default.string,className:i.default.string,style:i.default.object,children:i.default.node,allowMouseEvents:i.default.bool,onSwipeUp:i.default.func,onSwipeDown:i.default.func,onSwipeLeft:i.default.func,onSwipeRight:i.default.func,onSwipeStart:i.default.func,onSwipeMove:i.default.func,onSwipeEnd:i.default.func,innerRef:i.default.func,tolerance:i.default.number.isRequired},g.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},e.default=g},void 0===(r="function"===typeof o?o.apply(t,i):o)||(e.exports=r)},30480:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(e,t,n){var o=0===e?e:e+t;return"translate3d"+("("+("horizontal"===n?[o,0,0]:[0,o,0]).join(",")+")")}},98806:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fadeAnimationHandler=t.slideStopSwipingHandler=t.slideSwipeAnimationHandler=t.slideAnimationHandler=void 0;var o,i=n(66845),r=(o=n(30480))&&o.__esModule?o:{default:o},l=n(34705);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}t.slideAnimationHandler=function(e,t){var n={},o=t.selectedItem,a=o,c=i.Children.count(e.children)-1;if(e.infiniteLoop&&(o<0||o>c))return a<0?e.centerMode&&e.centerSlidePercentage&&"horizontal"===e.axis?n.itemListStyle=(0,l.setPosition)(-(c+2)*e.centerSlidePercentage-(100-e.centerSlidePercentage)/2,e.axis):n.itemListStyle=(0,l.setPosition)(100*-(c+2),e.axis):a>c&&(n.itemListStyle=(0,l.setPosition)(0,e.axis)),n;var u=(0,l.getPosition)(o,e),d=(0,r.default)(u,"%",e.axis),h=e.transitionTime+"ms";return n.itemListStyle={WebkitTransform:d,msTransform:d,OTransform:d,transform:d},t.swiping||(n.itemListStyle=s(s({},n.itemListStyle),{},{WebkitTransitionDuration:h,MozTransitionDuration:h,OTransitionDuration:h,transitionDuration:h,msTransitionDuration:h})),n};t.slideSwipeAnimationHandler=function(e,t,n,o){var r={},a="horizontal"===t.axis,s=i.Children.count(t.children),c=(0,l.getPosition)(n.selectedItem,t),u=t.infiniteLoop?(0,l.getPosition)(s-1,t)-100:(0,l.getPosition)(s-1,t),d=a?e.x:e.y,h=d;0===c&&d>0&&(h=0),c===u&&d<0&&(h=0);var f=c+100/(n.itemSize/h),p=Math.abs(d)>t.swipeScrollTolerance;return t.infiniteLoop&&p&&(0===n.selectedItem&&f>-100?f-=100*s:n.selectedItem===s-1&&f<100*-s&&(f+=100*s)),(!t.preventMovementUntilSwipeScrollTolerance||p||n.swipeMovementStarted)&&(n.swipeMovementStarted||o({swipeMovementStarted:!0}),r.itemListStyle=(0,l.setPosition)(f,t.axis)),p&&!n.cancelClick&&o({cancelClick:!0}),r};t.slideStopSwipingHandler=function(e,t){var n=(0,l.getPosition)(t.selectedItem,e);return{itemListStyle:(0,l.setPosition)(n,e.axis)}};t.fadeAnimationHandler=function(e,t){var n=e.transitionTime+"ms",o="ease-in-out",i={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:o,msTransitionTimingFunction:o,MozTransitionTimingFunction:o,WebkitTransitionTimingFunction:o,OTransitionTimingFunction:o};return t.swiping||(i=s(s({},i),{},{WebkitTransitionDuration:n,MozTransitionDuration:n,OTransitionDuration:n,transitionDuration:n,msTransitionDuration:n})),{slideStyle:i,selectedStyle:s(s({},i),{},{opacity:1,position:"relative"}),prevStyle:s({},i)}}},42852:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==f(e)&&"function"!==typeof e)return{default:e};var t=h();if(t&&t.has(e))return t.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var r=o?Object.getOwnPropertyDescriptor(e,i):null;r&&(r.get||r.set)?Object.defineProperty(n,i,r):n[i]=e[i]}n.default=e,t&&t.set(e,n);return n}(n(66845)),i=d(n(15650)),r=d(n(75385)),l=d(n(91935)),a=d(n(39896)),s=d(n(22124)),c=n(34705),u=n(98806);function d(e){return e&&e.__esModule?e:{default:e}}function h(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return h=function(){return e},e}function f(e){return f="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(){return p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},p.apply(this,arguments)}function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function w(e,t){return w=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},w(e,t)}function b(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=x(e);if(t){var i=x(this).constructor;n=Reflect.construct(o,arguments,i)}else n=o.apply(this,arguments);return function(e,t){if(t&&("object"===f(t)||"function"===typeof t))return t;return y(e)}(this,n)}}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var C=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&w(e,t)}(f,e);var t,n,d,h=b(f);function f(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),k(y(t=h.call(this,e)),"thumbsRef",void 0),k(y(t),"carouselWrapperRef",void 0),k(y(t),"listRef",void 0),k(y(t),"itemsRef",void 0),k(y(t),"timer",void 0),k(y(t),"animationHandler",void 0),k(y(t),"setThumbsRef",(function(e){t.thumbsRef=e})),k(y(t),"setCarouselWrapperRef",(function(e){t.carouselWrapperRef=e})),k(y(t),"setListRef",(function(e){t.listRef=e})),k(y(t),"setItemsRef",(function(e,n){t.itemsRef||(t.itemsRef=[]),t.itemsRef[n]=e})),k(y(t),"autoPlay",(function(){o.Children.count(t.props.children)<=1||(t.clearAutoPlay(),t.props.autoPlay&&(t.timer=setTimeout((function(){t.increment()}),t.props.interval)))})),k(y(t),"clearAutoPlay",(function(){t.timer&&clearTimeout(t.timer)})),k(y(t),"resetAutoPlay",(function(){t.clearAutoPlay(),t.autoPlay()})),k(y(t),"stopOnHover",(function(){t.setState({isMouseEntered:!0},t.clearAutoPlay)})),k(y(t),"startOnLeave",(function(){t.setState({isMouseEntered:!1},t.autoPlay)})),k(y(t),"isFocusWithinTheCarousel",(function(){return!!t.carouselWrapperRef&&!((0,a.default)().activeElement!==t.carouselWrapperRef&&!t.carouselWrapperRef.contains((0,a.default)().activeElement))})),k(y(t),"navigateWithKeyboard",(function(e){if(t.isFocusWithinTheCarousel()){var n="horizontal"===t.props.axis,o=n?37:38;(n?39:40)===e.keyCode?t.increment():o===e.keyCode&&t.decrement()}})),k(y(t),"updateSizes",(function(){if(t.state.initialized&&t.itemsRef&&0!==t.itemsRef.length){var e="horizontal"===t.props.axis,n=t.itemsRef[0];if(n){var o=e?n.clientWidth:n.clientHeight;t.setState({itemSize:o}),t.thumbsRef&&t.thumbsRef.updateSizes()}}})),k(y(t),"setMountState",(function(){t.setState({hasMount:!0}),t.updateSizes()})),k(y(t),"handleClickItem",(function(e,n){0!==o.Children.count(t.props.children)&&(t.state.cancelClick?t.setState({cancelClick:!1}):(t.props.onClickItem(e,n),e!==t.state.selectedItem&&t.setState({selectedItem:e})))})),k(y(t),"handleOnChange",(function(e,n){o.Children.count(t.props.children)<=1||t.props.onChange(e,n)})),k(y(t),"handleClickThumb",(function(e,n){t.props.onClickThumb(e,n),t.moveTo(e)})),k(y(t),"onSwipeStart",(function(e){t.setState({swiping:!0}),t.props.onSwipeStart(e)})),k(y(t),"onSwipeEnd",(function(e){t.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),t.props.onSwipeEnd(e),t.clearAutoPlay(),t.state.autoPlay&&t.autoPlay()})),k(y(t),"onSwipeMove",(function(e,n){t.props.onSwipeMove(n);var o=t.props.swipeAnimationHandler(e,t.props,t.state,t.setState.bind(y(t)));return t.setState(g({},o)),!!Object.keys(o).length})),k(y(t),"decrement",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem-("number"===typeof e?e:1))})),k(y(t),"increment",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem+("number"===typeof e?e:1))})),k(y(t),"moveTo",(function(e){if("number"===typeof e){var n=o.Children.count(t.props.children)-1;e<0&&(e=t.props.infiniteLoop?n:0),e>n&&(e=t.props.infiniteLoop?0:n),t.selectItem({selectedItem:e}),t.state.autoPlay&&!1===t.state.isMouseEntered&&t.resetAutoPlay()}})),k(y(t),"onClickNext",(function(){t.increment(1)})),k(y(t),"onClickPrev",(function(){t.decrement(1)})),k(y(t),"onSwipeForward",(function(){t.increment(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(y(t),"onSwipeBackwards",(function(){t.decrement(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(y(t),"changeItem",(function(e){return function(n){(0,c.isKeyboardEvent)(n)&&"Enter"!==n.key||t.moveTo(e)}})),k(y(t),"selectItem",(function(e){t.setState(g({previousItem:t.state.selectedItem},e),(function(){t.setState(t.animationHandler(t.props,t.state))})),t.handleOnChange(e.selectedItem,o.Children.toArray(t.props.children)[e.selectedItem])})),k(y(t),"getInitialImage",(function(){var e=t.props.selectedItem,n=t.itemsRef&&t.itemsRef[e];return(n&&n.getElementsByTagName("img")||[])[0]})),k(y(t),"getVariableItemHeight",(function(e){var n=t.itemsRef&&t.itemsRef[e];if(t.state.hasMount&&n&&n.children.length){var o=n.children[0].getElementsByTagName("img")||[];if(o.length>0){var i=o[0];if(!i.complete){i.addEventListener("load",(function e(){t.forceUpdate(),i.removeEventListener("load",e)}))}}var r=(o[0]||n.children[0]).clientHeight;return r>0?r:null}return null}));var n={initialized:!1,previousItem:e.selectedItem,selectedItem:e.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:e.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return t.animationHandler="function"===typeof e.animationHandler&&e.animationHandler||"fade"===e.animationHandler&&u.fadeAnimationHandler||u.slideAnimationHandler,t.state=g(g({},n),t.animationHandler(e,n)),t}return t=f,(n=[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(e,t){e.children||!this.props.children||this.state.initialized||this.setupCarousel(),!e.autoFocus&&this.props.autoFocus&&this.forceFocus(),t.swiping&&!this.state.swiping&&this.setState(g({},this.props.stopSwipingHandler(this.props,this.state))),e.selectedItem===this.props.selectedItem&&e.centerMode===this.props.centerMode||(this.updateSizes(),this.moveTo(this.props.selectedItem)),e.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var e=this;this.bindEvents(),this.state.autoPlay&&o.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},(function(){var t=e.getInitialImage();t&&!t.complete?t.addEventListener("load",e.setMountState):e.setMountState()}))}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.addEventListener("mouseenter",this.stopOnHover),e.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.removeEventListener("mouseenter",this.stopOnHover),e.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,s.default)().addEventListener("resize",this.updateSizes),(0,s.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,a.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,s.default)().removeEventListener("resize",this.updateSizes),(0,s.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var e=this.getInitialImage();e&&e.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,a.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var e;null===(e=this.carouselWrapperRef)||void 0===e||e.focus()}},{key:"renderItems",value:function(e){var t=this;return this.props.children?o.Children.map(this.props.children,(function(n,i){var l=i===t.state.selectedItem,a=i===t.state.previousItem,s=l&&t.state.selectedStyle||a&&t.state.prevStyle||t.state.slideStyle||{};t.props.centerMode&&"horizontal"===t.props.axis&&(s=g(g({},s),{},{minWidth:t.props.centerSlidePercentage+"%"})),t.state.swiping&&t.state.swipeMovementStarted&&(s=g(g({},s),{},{pointerEvents:"none"}));var c={ref:function(e){return t.setItemsRef(e,i)},key:"itemKey"+i+(e?"clone":""),className:r.default.ITEM(!0,i===t.state.selectedItem,i===t.state.previousItem),onClick:t.handleClickItem.bind(t,i,n),style:s};return o.default.createElement("li",c,t.props.renderItem(n,{isSelected:i===t.state.selectedItem,isPrevious:i===t.state.previousItem}))})):[]}},{key:"renderControls",value:function(){var e=this,t=this.props,n=t.showIndicators,i=t.labels,r=t.renderIndicator,l=t.children;return n?o.default.createElement("ul",{className:"control-dots"},o.Children.map(l,(function(t,n){return r&&r(e.changeItem(n),n===e.state.selectedItem,n,i.item)}))):null}},{key:"renderStatus",value:function(){return this.props.showStatus?o.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,o.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return this.props.showThumbs&&this.props.children&&0!==o.Children.count(this.props.children)?o.default.createElement(l.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children)):null}},{key:"render",value:function(){var e=this;if(!this.props.children||0===o.Children.count(this.props.children))return null;var t=this.props.swipeable&&o.Children.count(this.props.children)>1,n="horizontal"===this.props.axis,l=this.props.showArrows&&o.Children.count(this.props.children)>1,a=l&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,s=l&&(this.state.selectedItem<o.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,c=this.renderItems(!0),u=c.shift(),d=c.pop(),h={className:r.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},f={};if(n){if(h.onSwipeLeft=this.onSwipeForward,h.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var v=this.getVariableItemHeight(this.state.selectedItem);f.height=v||"auto"}}else h.onSwipeUp="natural"===this.props.verticalSwipe?this.onSwipeBackwards:this.onSwipeForward,h.onSwipeDown="natural"===this.props.verticalSwipe?this.onSwipeForward:this.onSwipeBackwards,h.style=g(g({},h.style),{},{height:this.state.itemSize}),f.height=this.state.itemSize;return o.default.createElement("div",{"aria-label":this.props.ariaLabel,className:r.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},o.default.createElement("div",{className:r.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,a,this.props.labels.leftArrow),o.default.createElement("div",{className:r.default.WRAPPER(!0,this.props.axis),style:f},t?o.default.createElement(i.default,p({tagName:"ul",innerRef:this.setListRef},h,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&u):o.default.createElement("ul",{className:r.default.SLIDER(!0,this.state.swiping),ref:function(t){return e.setListRef(t)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&u)),this.props.renderArrowNext(this.onClickNext,s,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}])&&m(t.prototype,n),d&&m(t,d),f}(o.default.Component);t.default=C,k(C,"displayName","Carousel"),k(C,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:c.noop,onClickThumb:c.noop,onChange:c.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(e,t,n){return o.default.createElement("button",{type:"button","aria-label":n,className:r.default.ARROW_PREV(!t),onClick:e})},renderArrowNext:function(e,t,n){return o.default.createElement("button",{type:"button","aria-label":n,className:r.default.ARROW_NEXT(!t),onClick:e})},renderIndicator:function(e,t,n,i){return o.default.createElement("li",{className:r.default.DOT(t),onClick:e,onKeyDown:e,value:n,key:n,role:"button",tabIndex:0,"aria-label":"".concat(i," ").concat(n+1)})},renderItem:function(e){return e},renderThumbs:function(e){var t=o.Children.map(e,(function(e){var t=e;if("img"!==e.type&&(t=o.Children.toArray(e.props.children).find((function(e){return"img"===e.type}))),t)return t}));return 0===t.filter((function(e){return e})).length?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):t},statusFormatter:c.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:u.slideSwipeAnimationHandler,stopSwipingHandler:u.slideStopSwipingHandler})},96239:()=>{},34705:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setPosition=t.getPosition=t.isKeyboardEvent=t.defaultStatusFormatter=t.noop=void 0;var o,i=n(66845),r=(o=n(30480))&&o.__esModule?o:{default:o};t.noop=function(){};t.defaultStatusFormatter=function(e,t){return"".concat(e," of ").concat(t)};t.isKeyboardEvent=function(e){return!!e&&e.hasOwnProperty("key")};t.getPosition=function(e,t){if(t.infiniteLoop&&++e,0===e)return 0;var n=i.Children.count(t.children);if(t.centerMode&&"horizontal"===t.axis){var o=-e*t.centerSlidePercentage,r=n-1;return e&&(e!==r||t.infiniteLoop)?o+=(100-t.centerSlidePercentage)/2:e===r&&(o+=100-t.centerSlidePercentage),o}return 100*-e};t.setPosition=function(e,t){var n={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(o){n[o]=(0,r.default)(e,"%",t)})),n}},91935:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==d(e)&&"function"!==typeof e)return{default:e};var t=u();if(t&&t.has(e))return t.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var r=o?Object.getOwnPropertyDescriptor(e,i):null;r&&(r.get||r.set)?Object.defineProperty(n,i,r):n[i]=e[i]}n.default=e,t&&t.set(e,n);return n}(n(66845)),i=c(n(75385)),r=n(24202),l=c(n(30480)),a=c(n(15650)),s=c(n(22124));function c(e){return e&&e.__esModule?e:{default:e}}function u(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return u=function(){return e},e}function d(e){return d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(){return h=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},h.apply(this,arguments)}function f(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function p(e,t){return p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},p(e,t)}function v(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=m(e);if(t){var i=m(this).constructor;n=Reflect.construct(o,arguments,i)}else n=o.apply(this,arguments);return function(e,t){if(t&&("object"===d(t)||"function"===typeof t))return t;return g(e)}(this,n)}}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}(d,e);var t,n,c,u=v(d);function d(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),w(g(t=u.call(this,e)),"itemsWrapperRef",void 0),w(g(t),"itemsListRef",void 0),w(g(t),"thumbsRef",void 0),w(g(t),"setItemsWrapperRef",(function(e){t.itemsWrapperRef=e})),w(g(t),"setItemsListRef",(function(e){t.itemsListRef=e})),w(g(t),"setThumbsRef",(function(e,n){t.thumbsRef||(t.thumbsRef=[]),t.thumbsRef[n]=e})),w(g(t),"updateSizes",(function(){if(t.props.children&&t.itemsWrapperRef&&t.thumbsRef){var e=o.Children.count(t.props.children),n=t.itemsWrapperRef.clientWidth,i=t.props.thumbWidth?t.props.thumbWidth:(0,r.outerWidth)(t.thumbsRef[0]),l=Math.floor(n/i),a=l<e,s=a?e-l:0;t.setState((function(e,n){return{itemSize:i,visibleItems:l,firstItem:a?t.getFirstItem(n.selectedItem):0,lastPosition:s,showArrows:a}}))}})),w(g(t),"handleClickItem",(function(e,n,o){if(!function(e){return e.hasOwnProperty("key")}(o)||"Enter"===o.key){var i=t.props.onSelectItem;"function"===typeof i&&i(e,n)}})),w(g(t),"onSwipeStart",(function(){t.setState({swiping:!0})})),w(g(t),"onSwipeEnd",(function(){t.setState({swiping:!1})})),w(g(t),"onSwipeMove",(function(e){var n=e.x;if(!t.state.itemSize||!t.itemsWrapperRef||!t.state.visibleItems)return!1;var i=o.Children.count(t.props.children),r=-100*t.state.firstItem/t.state.visibleItems;0===r&&n>0&&(n=0),r===100*-Math.max(i-t.state.visibleItems,0)/t.state.visibleItems&&n<0&&(n=0);var a=r+100/(t.itemsWrapperRef.clientWidth/n);return t.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(e){t.itemsListRef.style[e]=(0,l.default)(a,"%",t.props.axis)})),!0})),w(g(t),"slideRight",(function(e){t.moveTo(t.state.firstItem-("number"===typeof e?e:1))})),w(g(t),"slideLeft",(function(e){t.moveTo(t.state.firstItem+("number"===typeof e?e:1))})),w(g(t),"moveTo",(function(e){e=(e=e<0?0:e)>=t.state.lastPosition?t.state.lastPosition:e,t.setState({firstItem:e})})),t.state={selectedItem:e.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},t}return t=d,(n=[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(e){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==e.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,s.default)().addEventListener("resize",this.updateSizes),(0,s.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,s.default)().removeEventListener("resize",this.updateSizes),(0,s.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(e){var t=e;return e>=this.state.lastPosition&&(t=this.state.lastPosition),e<this.state.firstItem+this.state.visibleItems&&(t=this.state.firstItem),e<this.state.firstItem&&(t=e),t}},{key:"renderItems",value:function(){var e=this;return this.props.children.map((function(t,n){var r=i.default.ITEM(!1,n===e.state.selectedItem),l={key:n,ref:function(t){return e.setThumbsRef(t,n)},className:r,onClick:e.handleClickItem.bind(e,n,e.props.children[n]),onKeyDown:e.handleClickItem.bind(e,n,e.props.children[n]),"aria-label":"".concat(e.props.labels.item," ").concat(n+1),style:{width:e.props.thumbWidth}};return o.default.createElement("li",h({},l,{role:"button",tabIndex:0}),t)}))}},{key:"render",value:function(){var e=this;if(!this.props.children)return null;var t,n=o.Children.count(this.props.children)>1,r=this.state.showArrows&&this.state.firstItem>0,s=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,c=-this.state.firstItem*(this.state.itemSize||0),u=(0,l.default)(c,"px",this.props.axis),d=this.props.transitionTime+"ms";return t={WebkitTransform:u,MozTransform:u,MsTransform:u,OTransform:u,transform:u,msTransform:u,WebkitTransitionDuration:d,MozTransitionDuration:d,MsTransitionDuration:d,OTransitionDuration:d,transitionDuration:d,msTransitionDuration:d},o.default.createElement("div",{className:i.default.CAROUSEL(!1)},o.default.createElement("div",{className:i.default.WRAPPER(!1),ref:this.setItemsWrapperRef},o.default.createElement("button",{type:"button",className:i.default.ARROW_PREV(!r),onClick:function(){return e.slideRight()},"aria-label":this.props.labels.leftArrow}),n?o.default.createElement(a.default,{tagName:"ul",className:i.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:t,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):o.default.createElement("ul",{className:i.default.SLIDER(!1,this.state.swiping),ref:function(t){return e.setItemsListRef(t)},style:t},this.renderItems()),o.default.createElement("button",{type:"button",className:i.default.ARROW_NEXT(!s),onClick:function(){return e.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}])&&f(t.prototype,n),c&&f(t,c),d}(o.Component);t.default=b,w(b,"displayName","Thumbs"),w(b,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350})},75385:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o,i=(o=n(23175))&&o.__esModule?o:{default:o};var r={ROOT:function(e){return(0,i.default)(function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({"carousel-root":!0},e||"",!!e))},CAROUSEL:function(e){return(0,i.default)({carousel:!0,"carousel-slider":e})},WRAPPER:function(e,t){return(0,i.default)({"thumbs-wrapper":!e,"slider-wrapper":e,"axis-horizontal":"horizontal"===t,"axis-vertical":"horizontal"!==t})},SLIDER:function(e,t){return(0,i.default)({thumbs:!e,slider:e,animated:!t})},ITEM:function(e,t,n){return(0,i.default)({thumb:!e,slide:e,selected:t,previous:n})},ARROW_PREV:function(e){return(0,i.default)({"control-arrow control-prev":!0,"control-disabled":e})},ARROW_NEXT:function(e){return(0,i.default)({"control-arrow control-next":!0,"control-disabled":e})},DOT:function(e){return(0,i.default)({dot:!0,selected:e})}};t.default=r},24202:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.outerWidth=void 0;t.outerWidth=function(e){var t=e.offsetWidth,n=getComputedStyle(e);return t+=parseInt(n.marginLeft)+parseInt(n.marginRight)}},44303:(e,t,n)=>{"use strict";Object.defineProperty(t,"lr",{enumerable:!0,get:function(){return o.default}});var o=l(n(42852)),i=n(96239),r=l(n(91935));function l(e){return e&&e.__esModule?e:{default:e}}},39896:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return document}},22124:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return window}},52347:(e,t,n)=>{var o;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function r(e){return function(e,t){var n,o,l,a,s,c,u,d,h,f=1,p=e.length,v="";for(o=0;o<p;o++)if("string"===typeof e[o])v+=e[o];else if("object"===typeof e[o]){if((a=e[o]).keys)for(n=t[f],l=0;l<a.keys.length;l++){if(void 0==n)throw new Error(r('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[l],a.keys[l-1]));n=n[a.keys[l]]}else n=a.param_no?t[a.param_no]:t[f++];if(i.not_type.test(a.type)&&i.not_primitive.test(a.type)&&n instanceof Function&&(n=n()),i.numeric_arg.test(a.type)&&"number"!==typeof n&&isNaN(n))throw new TypeError(r("[sprintf] expecting number but found %T",n));switch(i.number.test(a.type)&&(d=n>=0),a.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,a.width?parseInt(a.width):0);break;case"e":n=a.precision?parseFloat(n).toExponential(a.precision):parseFloat(n).toExponential();break;case"f":n=a.precision?parseFloat(n).toFixed(a.precision):parseFloat(n);break;case"g":n=a.precision?String(Number(n.toPrecision(a.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=a.precision?n.substring(0,a.precision):n;break;case"t":n=String(!!n),n=a.precision?n.substring(0,a.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=a.precision?n.substring(0,a.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=a.precision?n.substring(0,a.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}i.json.test(a.type)?v+=n:(!i.number.test(a.type)||d&&!a.sign?h="":(h=d?"+":"-",n=n.toString().replace(i.sign,"")),c=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",u=a.width-(h+n).length,s=a.width&&u>0?c.repeat(u):"",v+=a.align?h+n+s:"0"===c?h+s+n:s+h+n)}return v}(function(e){if(a[e])return a[e];var t,n=e,o=[],r=0;for(;n;){if(null!==(t=i.text.exec(n)))o.push(t[0]);else if(null!==(t=i.modulo.exec(n)))o.push("%");else{if(null===(t=i.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){r|=1;var l=[],s=t[2],c=[];if(null===(c=i.key.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(l.push(c[1]);""!==(s=s.substring(c[0].length));)if(null!==(c=i.key_access.exec(s)))l.push(c[1]);else{if(null===(c=i.index_access.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");l.push(c[1])}t[2]=l}else r|=2;if(3===r)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");o.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}n=n.substring(t[0].length)}return a[e]=o}(e),arguments)}function l(e,t){return r.apply(null,[e].concat(t||[]))}var a=Object.create(null);t.sprintf=r,t.vsprintf=l,"undefined"!==typeof window&&(window.sprintf=r,window.vsprintf=l,void 0===(o=function(){return{sprintf:r,vsprintf:l}}.call(t,n,t,e))||(e.exports=o))}()},24665:()=>{},2739:()=>{},22951:(e,t,n)=>{"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,{Z:()=>o})},91976:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(55217);function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(0,o.Z)(i.key),i)}}function r(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}},9053:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(27597);var i=n(99492);function r(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,o.Z)(e);if(t){var l=(0,o.Z)(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return(0,i.Z)(this,n)}}},27597:(e,t,n)=>{"use strict";function o(e){return o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},o(e)}n.d(t,{Z:()=>o})},67591:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(6983);function i(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.Z)(e,t)}},99492:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(33940),i=n(47169);function r(e,t){if(t&&("object"===(0,o.Z)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.Z)(e)}},65809:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(64013);function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,l,a=[],s=!0,c=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=r.call(n)).done)&&(a.push(o.value),a.length!==t);s=!0);}catch(u){c=!0,i=u}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw i}}return a}}(e,t)||(0,o.Z)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},37538:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>d});var o=n(66845),i=n(74559),r=n(31208),l=n(67930),a=n(39806);const s=(0,i.z)("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),c=(e,t)=>{if(void 0===t||null===t)return"";const n=t.toISOString();switch(e){case"date":return n.split("T")[0];case"datetime-local":return n.replace("Z","");case"time":return n.split("T")[1].replace("Z","");default:throw new Error("Unknown date kind ".concat(e))}},u=e=>{const t=e.value.data,{format:n,displayDate:i}=t,l=void 0===t.step||Number.isNaN(Number(t.step))?void 0:Number(t.step),a=t.min instanceof Date?c(n,t.min):t.min,u=t.max instanceof Date?c(n,t.max):t.max;let d=t.date;const h=t.timezoneOffset?60*t.timezoneOffset*1e3:0;h&&d&&(d=new Date(d.getTime()+h));const f=c(n,d);return e.value.readonly?o.createElement(r.K,{highlight:!0,autoFocus:!1,disabled:!0,value:null!==i&&void 0!==i?i:"",onChange:()=>{}}):o.createElement(s,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:f,min:a,max:u,step:l,autoFocus:!0,onChange:t=>{isNaN(t.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(t.target.valueAsNumber-h)}})}})},d={kind:l.p6.Custom,isMatch:e=>"date-picker-cell"===e.data.kind,draw:(e,t)=>{const{displayDate:n}=t.data;return(0,a.L6)(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:o}=t.data;return e.measureText(o).width+2*n.cellHorizontalPadding},provideEditor:()=>({editor:u}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),"time"===t.format&&Number.isNaN(n)&&(n=Date.parse("1970-01-01T".concat(e,"Z"))))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}}},5527:(e,t,n)=>{"use strict";n.d(t,{Z:()=>on});var o=n(66845),i=n(74559),r=n(64649);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,r.Z)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var s=n(25773),c=n(7865),u=n(65809),d=n(53782),h=n(33940),f=n(50669),p=n(17664);const v=Math.min,g=Math.max,m=Math.round,w=Math.floor,b=e=>({x:e,y:e});function y(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function x(e){return S(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function C(e){var t;return null==(t=(S(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function S(e){return e instanceof Node||e instanceof k(e).Node}function M(e){return e instanceof Element||e instanceof k(e).Element}function R(e){return e instanceof HTMLElement||e instanceof k(e).HTMLElement}function E(e){return"undefined"!==typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function I(e){const{overflow:t,overflowX:n,overflowY:o,display:i}=P(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(i)}function T(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function O(e){return["html","body","#document"].includes(x(e))}function P(e){return k(e).getComputedStyle(e)}function D(e){if("html"===x(e))return e;const t=e.assignedSlot||e.parentNode||E(e)&&e.host||C(e);return E(t)?t.host:t}function H(e){const t=D(e);return O(t)?e.ownerDocument?e.ownerDocument.body:e.body:R(t)&&I(t)?t:H(t)}function z(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);const i=H(e),r=i===(null==(o=e.ownerDocument)?void 0:o.body),l=k(i);return r?t.concat(l,l.visualViewport||[],I(i)?i:[],l.frameElement&&n?z(l.frameElement):[]):t.concat(i,z(i,[],n))}function L(e){const t=P(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const i=R(e),r=i?e.offsetWidth:n,l=i?e.offsetHeight:o,a=m(n)!==r||m(o)!==l;return a&&(n=r,o=l),{width:n,height:o,$:a}}function F(e){return M(e)?e:e.contextElement}function A(e){const t=F(e);if(!R(t))return b(1);const n=t.getBoundingClientRect(),{width:o,height:i,$:r}=L(t);let l=(r?m(n.width):n.width)/o,a=(r?m(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}const V=b(0);function _(e){const t=k(e);return T()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:V}function N(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const i=e.getBoundingClientRect(),r=F(e);let l=b(1);t&&(o?M(o)&&(l=A(o)):l=A(e));const a=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==k(e))&&t}(r,n,o)?_(r):b(0);let s=(i.left+a.x)/l.x,c=(i.top+a.y)/l.y,u=i.width/l.x,d=i.height/l.y;if(r){const e=k(r),t=o&&M(o)?k(o):o;let n=e.frameElement;for(;n&&o&&t!==e;){const e=A(n),t=n.getBoundingClientRect(),o=P(n),i=t.left+(n.clientLeft+parseFloat(o.paddingLeft))*e.x,r=t.top+(n.clientTop+parseFloat(o.paddingTop))*e.y;s*=e.x,c*=e.y,u*=e.x,d*=e.y,s+=i,c+=r,n=k(n).frameElement}}return y({width:u,height:d,x:s,y:c})}function B(e,t,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:l="function"===typeof ResizeObserver,layoutShift:a="function"===typeof IntersectionObserver,animationFrame:s=!1}=o,c=F(e),u=i||r?[...c?z(c):[],...z(t)]:[];u.forEach((e=>{i&&e.addEventListener("scroll",n,{passive:!0}),r&&e.addEventListener("resize",n)}));const d=c&&a?function(e,t){let n,o=null;const i=C(e);function r(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),r();const{left:c,top:u,width:d,height:h}=e.getBoundingClientRect();if(a||t(),!d||!h)return;const f={rootMargin:-w(u)+"px "+-w(i.clientWidth-(c+d))+"px "+-w(i.clientHeight-(u+h))+"px "+-w(c)+"px",threshold:g(0,v(1,s))||1};let p=!0;function m(e){const t=e[0].intersectionRatio;if(t!==s){if(!p)return l();t?l(!1,t):n=setTimeout((()=>{l(!1,1e-7)}),100)}p=!1}try{o=new IntersectionObserver(m,{...f,root:i.ownerDocument})}catch(b){o=new IntersectionObserver(m,f)}o.observe(e)}(!0),r}(c,n):null;let h,f=-1,p=null;l&&(p=new ResizeObserver((e=>{let[o]=e;o&&o.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame((()=>{var e;null==(e=p)||e.observe(t)}))),n()})),c&&!s&&p.observe(c),p.observe(t));let m=s?N(e):null;return s&&function t(){const o=N(e);!m||o.x===m.x&&o.y===m.y&&o.width===m.width&&o.height===m.height||n();m=o,h=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{i&&e.removeEventListener("scroll",n),r&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=p)||e.disconnect(),p=null,s&&cancelAnimationFrame(h)}}const Z=o.useLayoutEffect;var W=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],j=function(){};function U(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function X(e,t){for(var n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];var r=[].concat(o);if(t&&e)for(var l in t)t.hasOwnProperty(l)&&t[l]&&r.push("".concat(U(e,l)));return r.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var G=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===(0,h.Z)(e)&&null!==e?[e]:[];var t},Y=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,a({},(0,d.Z)(e,W))},K=function(e,t,n){var o=e.cx,i=e.getStyles,r=e.getClassNames,l=e.className;return{css:i(t,e),className:o(null!==n&&void 0!==n?n:{},r(t,e),l)}};function $(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function q(e){return $(e)?window.pageYOffset:e.scrollTop}function Q(e,t){$(e)?window.scrollTo(0,t):e.scrollTop=t}function J(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:j,i=q(e),r=t-i,l=0;!function t(){var a,s=r*((a=(a=l+=10)/n-1)*a*a+1)+i;Q(e,s),l<n?window.requestAnimationFrame(t):o(e)}()}function ee(e,t){var n=e.getBoundingClientRect(),o=t.getBoundingClientRect(),i=t.offsetHeight/3;o.bottom+i>n.bottom?Q(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):o.top-i<n.top&&Q(e,Math.max(t.offsetTop-i,0))}function te(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var ne=!1,oe={get passive(){return ne=!0}},ie="undefined"!==typeof window?window:{};ie.addEventListener&&ie.removeEventListener&&(ie.addEventListener("p",j,oe),ie.removeEventListener("p",j,!1));var re=ne;function le(e){return null!=e}function ae(e,t,n){return e?t:n}var se=["children","innerProps"],ce=["children","innerProps"];function ue(e){var t=e.maxHeight,n=e.menuEl,o=e.minHeight,i=e.placement,r=e.shouldScroll,l=e.isFixedPosition,a=e.controlHeight,s=function(e){var t=getComputedStyle(e),n="absolute"===t.position,o=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),(!n||"static"!==t.position)&&o.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}(n),c={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return c;var u,d=s.getBoundingClientRect().height,h=n.getBoundingClientRect(),f=h.bottom,p=h.height,v=h.top,g=n.offsetParent.getBoundingClientRect().top,m=l?window.innerHeight:$(u=s)?window.innerHeight:u.clientHeight,w=q(s),b=parseInt(getComputedStyle(n).marginBottom,10),y=parseInt(getComputedStyle(n).marginTop,10),x=g-y,k=m-v,C=x+w,S=d-w-v,M=f-m+w+b,R=w+v-y,E=160;switch(i){case"auto":case"bottom":if(k>=p)return{placement:"bottom",maxHeight:t};if(S>=p&&!l)return r&&J(s,M,E),{placement:"bottom",maxHeight:t};if(!l&&S>=o||l&&k>=o)return r&&J(s,M,E),{placement:"bottom",maxHeight:l?k-b:S-b};if("auto"===i||l){var I=t,T=l?x:C;return T>=o&&(I=Math.min(T-b-a,t)),{placement:"top",maxHeight:I}}if("bottom"===i)return r&&Q(s,M),{placement:"bottom",maxHeight:t};break;case"top":if(x>=p)return{placement:"top",maxHeight:t};if(C>=p&&!l)return r&&J(s,R,E),{placement:"top",maxHeight:t};if(!l&&C>=o||l&&x>=o){var O=t;return(!l&&C>=o||l&&x>=o)&&(O=l?x-y:C-y),r&&J(s,R,E),{placement:"top",maxHeight:O}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return c}var de,he=function(e){return"auto"===e?"bottom":e},fe=(0,o.createContext)(null),pe=function(e){var t=e.children,n=e.minMenuHeight,i=e.maxMenuHeight,r=e.menuPlacement,l=e.menuPosition,s=e.menuShouldScrollIntoView,c=e.theme,d=((0,o.useContext)(fe)||{}).setPortalPlacement,h=(0,o.useRef)(null),f=(0,o.useState)(i),p=(0,u.Z)(f,2),v=p[0],g=p[1],m=(0,o.useState)(null),w=(0,u.Z)(m,2),b=w[0],y=w[1],x=c.spacing.controlHeight;return Z((function(){var e=h.current;if(e){var t="fixed"===l,o=ue({maxHeight:i,menuEl:e,minHeight:n,placement:r,shouldScroll:s&&!t,isFixedPosition:t,controlHeight:x});g(o.maxHeight),y(o.placement),null===d||void 0===d||d(o.placement)}}),[i,r,l,s,n,d,x]),t({ref:h,placerProps:a(a({},e),{},{placement:b||he(r),maxHeight:v})})},ve=function(e){var t=e.children,n=e.innerRef,o=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"menu",{menu:!0}),{ref:n},o),t)},ge=function(e,t){var n=e.theme,o=n.spacing.baseUnit,i=n.colors;return a({textAlign:"center"},t?{}:{color:i.neutral40,padding:"".concat(2*o,"px ").concat(3*o,"px")})},me=ge,we=ge,be=["size"],ye=["innerProps","isRtl","size"];var xe={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},ke=function(e){var t=e.size,n=(0,d.Z)(e,be);return(0,c.tZ)("svg",(0,s.Z)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:xe},n))},Ce=function(e){return(0,c.tZ)(ke,(0,s.Z)({size:20},e),(0,c.tZ)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},Se=function(e){return(0,c.tZ)(ke,(0,s.Z)({size:20},e),(0,c.tZ)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},Me=function(e,t){var n=e.isFocused,o=e.theme,i=o.spacing.baseUnit,r=o.colors;return a({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:n?r.neutral60:r.neutral20,padding:2*i,":hover":{color:n?r.neutral80:r.neutral40}})},Re=Me,Ee=Me,Ie=(0,c.F4)(de||(de=(0,f.Z)(["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"]))),Te=function(e){var t=e.delay,n=e.offset;return(0,c.tZ)("span",{css:(0,c.iv)({animation:"".concat(Ie," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","","","")})},Oe=function(e){var t=e.children,n=e.isDisabled,o=e.isFocused,i=e.innerRef,r=e.innerProps,l=e.menuIsOpen;return(0,c.tZ)("div",(0,s.Z)({ref:i},K(e,"control",{control:!0,"control--is-disabled":n,"control--is-focused":o,"control--menu-is-open":l}),r,{"aria-disabled":n||void 0}),t)},Pe=["data"],De=function(e){var t=e.children,n=e.cx,o=e.getStyles,i=e.getClassNames,r=e.Heading,l=e.headingProps,a=e.innerProps,u=e.label,d=e.theme,h=e.selectProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"group",{group:!0}),a),(0,c.tZ)(r,(0,s.Z)({},l,{selectProps:h,theme:d,getStyles:o,getClassNames:i,cx:n}),u),(0,c.tZ)("div",null,t))},He=["innerRef","isDisabled","isHidden","inputClassName"],ze={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Le={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":a({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ze)},Fe=function(e){return a({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},ze)},Ae=function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",n,t)};var Ve=function(e){var t=e.children,n=e.components,o=e.data,i=e.innerProps,r=e.isDisabled,l=e.removeProps,s=e.selectProps,u=n.Container,d=n.Label,h=n.Remove;return(0,c.tZ)(u,{data:o,innerProps:a(a({},K(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":r})),i),selectProps:s},(0,c.tZ)(d,{data:o,innerProps:a({},K(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:s},t),(0,c.tZ)(h,{data:o,innerProps:a(a({},K(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},l),selectProps:s}))},_e={ClearIndicator:function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),n),t||(0,c.tZ)(Ce,null))},Control:Oe,DropdownIndicator:function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),n),t||(0,c.tZ)(Se,null))},DownChevron:Se,CrossIcon:Ce,Group:De,GroupHeading:function(e){var t=Y(e);t.data;var n=(0,d.Z)(t,Pe);return(0,c.tZ)("div",(0,s.Z)({},K(e,"groupHeading",{"group-heading":!0}),n))},IndicatorsContainer:function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"indicatorsContainer",{indicators:!0}),n),t)},IndicatorSeparator:function(e){var t=e.innerProps;return(0,c.tZ)("span",(0,s.Z)({},t,K(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,n=e.value,o=Y(e),i=o.innerRef,r=o.isDisabled,l=o.isHidden,a=o.inputClassName,u=(0,d.Z)(o,He);return(0,c.tZ)("div",(0,s.Z)({},K(e,"input",{"input-container":!0}),{"data-value":n||""}),(0,c.tZ)("input",(0,s.Z)({className:t({input:!0},a),ref:i,style:Fe(l),disabled:r},u)))},LoadingIndicator:function(e){var t=e.innerProps,n=e.isRtl,o=e.size,i=void 0===o?4:o,r=(0,d.Z)(e,ye);return(0,c.tZ)("div",(0,s.Z)({},K(a(a({},r),{},{innerProps:t,isRtl:n,size:i}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),(0,c.tZ)(Te,{delay:0,offset:n}),(0,c.tZ)(Te,{delay:160,offset:!0}),(0,c.tZ)(Te,{delay:320,offset:!n}))},Menu:ve,MenuList:function(e){var t=e.children,n=e.innerProps,o=e.innerRef,i=e.isMulti;return(0,c.tZ)("div",(0,s.Z)({},K(e,"menuList",{"menu-list":!0,"menu-list--is-multi":i}),{ref:o},n),t)},MenuPortal:function(e){var t=e.appendTo,n=e.children,i=e.controlElement,r=e.innerProps,l=e.menuPlacement,d=e.menuPosition,h=(0,o.useRef)(null),f=(0,o.useRef)(null),v=(0,o.useState)(he(l)),g=(0,u.Z)(v,2),m=g[0],w=g[1],b=(0,o.useMemo)((function(){return{setPortalPlacement:w}}),[]),y=(0,o.useState)(null),x=(0,u.Z)(y,2),k=x[0],C=x[1],S=(0,o.useCallback)((function(){if(i){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(i),t="fixed"===d?0:window.pageYOffset,n=e[m]+t;n===(null===k||void 0===k?void 0:k.offset)&&e.left===(null===k||void 0===k?void 0:k.rect.left)&&e.width===(null===k||void 0===k?void 0:k.rect.width)||C({offset:n,rect:e})}}),[i,d,m,null===k||void 0===k?void 0:k.offset,null===k||void 0===k?void 0:k.rect.left,null===k||void 0===k?void 0:k.rect.width]);Z((function(){S()}),[S]);var M=(0,o.useCallback)((function(){"function"===typeof f.current&&(f.current(),f.current=null),i&&h.current&&(f.current=B(i,h.current,S,{elementResize:"ResizeObserver"in window}))}),[i,S]);Z((function(){M()}),[M]);var R=(0,o.useCallback)((function(e){h.current=e,M()}),[M]);if(!t&&"fixed"!==d||!k)return null;var E=(0,c.tZ)("div",(0,s.Z)({ref:R},K(a(a({},e),{},{offset:k.offset,position:d,rect:k.rect}),"menuPortal",{"menu-portal":!0}),r),n);return(0,c.tZ)(fe.Provider,{value:b},t?(0,p.createPortal)(E,t):E)},LoadingMessage:function(e){var t=e.children,n=void 0===t?"Loading...":t,o=e.innerProps,i=(0,d.Z)(e,ce);return(0,c.tZ)("div",(0,s.Z)({},K(a(a({},i),{},{children:n,innerProps:o}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),o),n)},NoOptionsMessage:function(e){var t=e.children,n=void 0===t?"No options":t,o=e.innerProps,i=(0,d.Z)(e,se);return(0,c.tZ)("div",(0,s.Z)({},K(a(a({},i),{},{children:n,innerProps:o}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),o),n)},MultiValue:Ve,MultiValueContainer:Ae,MultiValueLabel:Ae,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({role:"button"},n),t||(0,c.tZ)(Ce,{size:14}))},Option:function(e){var t=e.children,n=e.isDisabled,o=e.isFocused,i=e.isSelected,r=e.innerRef,l=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"option",{option:!0,"option--is-disabled":n,"option--is-focused":o,"option--is-selected":i}),{ref:r,"aria-disabled":n},l),t)},Placeholder:function(e){var t=e.children,n=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"placeholder",{placeholder:!0}),n),t)},SelectContainer:function(e){var t=e.children,n=e.innerProps,o=e.isDisabled,i=e.isRtl;return(0,c.tZ)("div",(0,s.Z)({},K(e,"container",{"--is-disabled":o,"--is-rtl":i}),n),t)},SingleValue:function(e){var t=e.children,n=e.isDisabled,o=e.innerProps;return(0,c.tZ)("div",(0,s.Z)({},K(e,"singleValue",{"single-value":!0,"single-value--is-disabled":n}),o),t)},ValueContainer:function(e){var t=e.children,n=e.innerProps,o=e.isMulti,i=e.hasValue;return(0,c.tZ)("div",(0,s.Z)({},K(e,"valueContainer",{"value-container":!0,"value-container--is-multi":o,"value-container--has-value":i}),n),t)}},Ne=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];var Be=n(22951),Ze=n(91976),We=n(67591),je=n(9053),Ue=n(30126),Xe=Number.isNaN||function(e){return"number"===typeof e&&e!==e};function Ge(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(o=e[n],i=t[n],!(o===i||Xe(o)&&Xe(i)))return!1;var o,i;return!0}for(var Ye={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Ke=function(e){return(0,c.tZ)("span",(0,s.Z)({css:Ye},e))},$e={guidance:function(e){var t=e.isSearchable,n=e.isMulti,o=e.tabSelectsValue,i=e.context,r=e.isInitialFocus;switch(i){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(o?", press Tab to select the option and exit the menu":"",".");case"input":return r?"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,o=void 0===n?"":n,i=e.labels,r=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(o,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(i.length>1?"s":""," ").concat(i.join(","),", selected.");case"select-option":return"option ".concat(o,r?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,o=e.options,i=e.label,r=void 0===i?"":i,l=e.selectValue,a=e.isDisabled,s=e.isSelected,c=e.isAppleDevice,u=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&l)return"value ".concat(r," focused, ").concat(u(l,n),".");if("menu"===t&&c){var d=a?" disabled":"",h="".concat(s?" selected":"").concat(d);return"".concat(r).concat(h,", ").concat(u(o,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},qe=function(e){var t=e.ariaSelection,n=e.focusedOption,i=e.focusedValue,r=e.focusableOptions,l=e.isFocused,s=e.selectValue,u=e.selectProps,d=e.id,h=e.isAppleDevice,f=u.ariaLiveMessages,p=u.getOptionLabel,v=u.inputValue,g=u.isMulti,m=u.isOptionDisabled,w=u.isSearchable,b=u.menuIsOpen,y=u.options,x=u.screenReaderStatus,k=u.tabSelectsValue,C=u.isLoading,S=u["aria-label"],M=u["aria-live"],R=(0,o.useMemo)((function(){return a(a({},$e),f||{})}),[f]),E=(0,o.useMemo)((function(){var e,n="";if(t&&R.onChange){var o=t.option,i=t.options,r=t.removedValue,l=t.removedValues,c=t.value,u=r||o||(e=c,Array.isArray(e)?null:e),d=u?p(u):"",h=i||l||void 0,f=h?h.map(p):[],v=a({isDisabled:u&&m(u,s),label:d,labels:f},t);n=R.onChange(v)}return n}),[t,R,m,s,p]),I=(0,o.useMemo)((function(){var e="",t=n||i,o=!!(n&&s&&s.includes(n));if(t&&R.onFocus){var l={focused:t,label:p(t),isDisabled:m(t,s),isSelected:o,options:r,context:t===n?"menu":"value",selectValue:s,isAppleDevice:h};e=R.onFocus(l)}return e}),[n,i,p,m,R,r,s,h]),T=(0,o.useMemo)((function(){var e="";if(b&&y.length&&!C&&R.onFilter){var t=x({count:r.length});e=R.onFilter({inputValue:v,resultsMessage:t})}return e}),[r,v,b,R,y,x,C]),O="initial-input-focus"===(null===t||void 0===t?void 0:t.action),P=(0,o.useMemo)((function(){var e="";if(R.guidance){var t=i?"value":b?"menu":"input";e=R.guidance({"aria-label":S,context:t,isDisabled:n&&m(n,s),isMulti:g,isSearchable:w,tabSelectsValue:k,isInitialFocus:O})}return e}),[S,n,i,g,m,w,b,R,s,k,O]),D=(0,c.tZ)(o.Fragment,null,(0,c.tZ)("span",{id:"aria-selection"},E),(0,c.tZ)("span",{id:"aria-focused"},I),(0,c.tZ)("span",{id:"aria-results"},T),(0,c.tZ)("span",{id:"aria-guidance"},P));return(0,c.tZ)(o.Fragment,null,(0,c.tZ)(Ke,{id:d},O&&D),(0,c.tZ)(Ke,{"aria-live":M,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},l&&!O&&D))},Qe=[{base:"A",letters:"A\u24b6\uff21\xc0\xc1\xc2\u1ea6\u1ea4\u1eaa\u1ea8\xc3\u0100\u0102\u1eb0\u1eae\u1eb4\u1eb2\u0226\u01e0\xc4\u01de\u1ea2\xc5\u01fa\u01cd\u0200\u0202\u1ea0\u1eac\u1eb6\u1e00\u0104\u023a\u2c6f"},{base:"AA",letters:"\ua732"},{base:"AE",letters:"\xc6\u01fc\u01e2"},{base:"AO",letters:"\ua734"},{base:"AU",letters:"\ua736"},{base:"AV",letters:"\ua738\ua73a"},{base:"AY",letters:"\ua73c"},{base:"B",letters:"B\u24b7\uff22\u1e02\u1e04\u1e06\u0243\u0182\u0181"},{base:"C",letters:"C\u24b8\uff23\u0106\u0108\u010a\u010c\xc7\u1e08\u0187\u023b\ua73e"},{base:"D",letters:"D\u24b9\uff24\u1e0a\u010e\u1e0c\u1e10\u1e12\u1e0e\u0110\u018b\u018a\u0189\ua779"},{base:"DZ",letters:"\u01f1\u01c4"},{base:"Dz",letters:"\u01f2\u01c5"},{base:"E",letters:"E\u24ba\uff25\xc8\xc9\xca\u1ec0\u1ebe\u1ec4\u1ec2\u1ebc\u0112\u1e14\u1e16\u0114\u0116\xcb\u1eba\u011a\u0204\u0206\u1eb8\u1ec6\u0228\u1e1c\u0118\u1e18\u1e1a\u0190\u018e"},{base:"F",letters:"F\u24bb\uff26\u1e1e\u0191\ua77b"},{base:"G",letters:"G\u24bc\uff27\u01f4\u011c\u1e20\u011e\u0120\u01e6\u0122\u01e4\u0193\ua7a0\ua77d\ua77e"},{base:"H",letters:"H\u24bd\uff28\u0124\u1e22\u1e26\u021e\u1e24\u1e28\u1e2a\u0126\u2c67\u2c75\ua78d"},{base:"I",letters:"I\u24be\uff29\xcc\xcd\xce\u0128\u012a\u012c\u0130\xcf\u1e2e\u1ec8\u01cf\u0208\u020a\u1eca\u012e\u1e2c\u0197"},{base:"J",letters:"J\u24bf\uff2a\u0134\u0248"},{base:"K",letters:"K\u24c0\uff2b\u1e30\u01e8\u1e32\u0136\u1e34\u0198\u2c69\ua740\ua742\ua744\ua7a2"},{base:"L",letters:"L\u24c1\uff2c\u013f\u0139\u013d\u1e36\u1e38\u013b\u1e3c\u1e3a\u0141\u023d\u2c62\u2c60\ua748\ua746\ua780"},{base:"LJ",letters:"\u01c7"},{base:"Lj",letters:"\u01c8"},{base:"M",letters:"M\u24c2\uff2d\u1e3e\u1e40\u1e42\u2c6e\u019c"},{base:"N",letters:"N\u24c3\uff2e\u01f8\u0143\xd1\u1e44\u0147\u1e46\u0145\u1e4a\u1e48\u0220\u019d\ua790\ua7a4"},{base:"NJ",letters:"\u01ca"},{base:"Nj",letters:"\u01cb"},{base:"O",letters:"O\u24c4\uff2f\xd2\xd3\xd4\u1ed2\u1ed0\u1ed6\u1ed4\xd5\u1e4c\u022c\u1e4e\u014c\u1e50\u1e52\u014e\u022e\u0230\xd6\u022a\u1ece\u0150\u01d1\u020c\u020e\u01a0\u1edc\u1eda\u1ee0\u1ede\u1ee2\u1ecc\u1ed8\u01ea\u01ec\xd8\u01fe\u0186\u019f\ua74a\ua74c"},{base:"OI",letters:"\u01a2"},{base:"OO",letters:"\ua74e"},{base:"OU",letters:"\u0222"},{base:"P",letters:"P\u24c5\uff30\u1e54\u1e56\u01a4\u2c63\ua750\ua752\ua754"},{base:"Q",letters:"Q\u24c6\uff31\ua756\ua758\u024a"},{base:"R",letters:"R\u24c7\uff32\u0154\u1e58\u0158\u0210\u0212\u1e5a\u1e5c\u0156\u1e5e\u024c\u2c64\ua75a\ua7a6\ua782"},{base:"S",letters:"S\u24c8\uff33\u1e9e\u015a\u1e64\u015c\u1e60\u0160\u1e66\u1e62\u1e68\u0218\u015e\u2c7e\ua7a8\ua784"},{base:"T",letters:"T\u24c9\uff34\u1e6a\u0164\u1e6c\u021a\u0162\u1e70\u1e6e\u0166\u01ac\u01ae\u023e\ua786"},{base:"TZ",letters:"\ua728"},{base:"U",letters:"U\u24ca\uff35\xd9\xda\xdb\u0168\u1e78\u016a\u1e7a\u016c\xdc\u01db\u01d7\u01d5\u01d9\u1ee6\u016e\u0170\u01d3\u0214\u0216\u01af\u1eea\u1ee8\u1eee\u1eec\u1ef0\u1ee4\u1e72\u0172\u1e76\u1e74\u0244"},{base:"V",letters:"V\u24cb\uff36\u1e7c\u1e7e\u01b2\ua75e\u0245"},{base:"VY",letters:"\ua760"},{base:"W",letters:"W\u24cc\uff37\u1e80\u1e82\u0174\u1e86\u1e84\u1e88\u2c72"},{base:"X",letters:"X\u24cd\uff38\u1e8a\u1e8c"},{base:"Y",letters:"Y\u24ce\uff39\u1ef2\xdd\u0176\u1ef8\u0232\u1e8e\u0178\u1ef6\u1ef4\u01b3\u024e\u1efe"},{base:"Z",letters:"Z\u24cf\uff3a\u0179\u1e90\u017b\u017d\u1e92\u1e94\u01b5\u0224\u2c7f\u2c6b\ua762"},{base:"a",letters:"a\u24d0\uff41\u1e9a\xe0\xe1\xe2\u1ea7\u1ea5\u1eab\u1ea9\xe3\u0101\u0103\u1eb1\u1eaf\u1eb5\u1eb3\u0227\u01e1\xe4\u01df\u1ea3\xe5\u01fb\u01ce\u0201\u0203\u1ea1\u1ead\u1eb7\u1e01\u0105\u2c65\u0250"},{base:"aa",letters:"\ua733"},{base:"ae",letters:"\xe6\u01fd\u01e3"},{base:"ao",letters:"\ua735"},{base:"au",letters:"\ua737"},{base:"av",letters:"\ua739\ua73b"},{base:"ay",letters:"\ua73d"},{base:"b",letters:"b\u24d1\uff42\u1e03\u1e05\u1e07\u0180\u0183\u0253"},{base:"c",letters:"c\u24d2\uff43\u0107\u0109\u010b\u010d\xe7\u1e09\u0188\u023c\ua73f\u2184"},{base:"d",letters:"d\u24d3\uff44\u1e0b\u010f\u1e0d\u1e11\u1e13\u1e0f\u0111\u018c\u0256\u0257\ua77a"},{base:"dz",letters:"\u01f3\u01c6"},{base:"e",letters:"e\u24d4\uff45\xe8\xe9\xea\u1ec1\u1ebf\u1ec5\u1ec3\u1ebd\u0113\u1e15\u1e17\u0115\u0117\xeb\u1ebb\u011b\u0205\u0207\u1eb9\u1ec7\u0229\u1e1d\u0119\u1e19\u1e1b\u0247\u025b\u01dd"},{base:"f",letters:"f\u24d5\uff46\u1e1f\u0192\ua77c"},{base:"g",letters:"g\u24d6\uff47\u01f5\u011d\u1e21\u011f\u0121\u01e7\u0123\u01e5\u0260\ua7a1\u1d79\ua77f"},{base:"h",letters:"h\u24d7\uff48\u0125\u1e23\u1e27\u021f\u1e25\u1e29\u1e2b\u1e96\u0127\u2c68\u2c76\u0265"},{base:"hv",letters:"\u0195"},{base:"i",letters:"i\u24d8\uff49\xec\xed\xee\u0129\u012b\u012d\xef\u1e2f\u1ec9\u01d0\u0209\u020b\u1ecb\u012f\u1e2d\u0268\u0131"},{base:"j",letters:"j\u24d9\uff4a\u0135\u01f0\u0249"},{base:"k",letters:"k\u24da\uff4b\u1e31\u01e9\u1e33\u0137\u1e35\u0199\u2c6a\ua741\ua743\ua745\ua7a3"},{base:"l",letters:"l\u24db\uff4c\u0140\u013a\u013e\u1e37\u1e39\u013c\u1e3d\u1e3b\u017f\u0142\u019a\u026b\u2c61\ua749\ua781\ua747"},{base:"lj",letters:"\u01c9"},{base:"m",letters:"m\u24dc\uff4d\u1e3f\u1e41\u1e43\u0271\u026f"},{base:"n",letters:"n\u24dd\uff4e\u01f9\u0144\xf1\u1e45\u0148\u1e47\u0146\u1e4b\u1e49\u019e\u0272\u0149\ua791\ua7a5"},{base:"nj",letters:"\u01cc"},{base:"o",letters:"o\u24de\uff4f\xf2\xf3\xf4\u1ed3\u1ed1\u1ed7\u1ed5\xf5\u1e4d\u022d\u1e4f\u014d\u1e51\u1e53\u014f\u022f\u0231\xf6\u022b\u1ecf\u0151\u01d2\u020d\u020f\u01a1\u1edd\u1edb\u1ee1\u1edf\u1ee3\u1ecd\u1ed9\u01eb\u01ed\xf8\u01ff\u0254\ua74b\ua74d\u0275"},{base:"oi",letters:"\u01a3"},{base:"ou",letters:"\u0223"},{base:"oo",letters:"\ua74f"},{base:"p",letters:"p\u24df\uff50\u1e55\u1e57\u01a5\u1d7d\ua751\ua753\ua755"},{base:"q",letters:"q\u24e0\uff51\u024b\ua757\ua759"},{base:"r",letters:"r\u24e1\uff52\u0155\u1e59\u0159\u0211\u0213\u1e5b\u1e5d\u0157\u1e5f\u024d\u027d\ua75b\ua7a7\ua783"},{base:"s",letters:"s\u24e2\uff53\xdf\u015b\u1e65\u015d\u1e61\u0161\u1e67\u1e63\u1e69\u0219\u015f\u023f\ua7a9\ua785\u1e9b"},{base:"t",letters:"t\u24e3\uff54\u1e6b\u1e97\u0165\u1e6d\u021b\u0163\u1e71\u1e6f\u0167\u01ad\u0288\u2c66\ua787"},{base:"tz",letters:"\ua729"},{base:"u",letters:"u\u24e4\uff55\xf9\xfa\xfb\u0169\u1e79\u016b\u1e7b\u016d\xfc\u01dc\u01d8\u01d6\u01da\u1ee7\u016f\u0171\u01d4\u0215\u0217\u01b0\u1eeb\u1ee9\u1eef\u1eed\u1ef1\u1ee5\u1e73\u0173\u1e77\u1e75\u0289"},{base:"v",letters:"v\u24e5\uff56\u1e7d\u1e7f\u028b\ua75f\u028c"},{base:"vy",letters:"\ua761"},{base:"w",letters:"w\u24e6\uff57\u1e81\u1e83\u0175\u1e87\u1e85\u1e98\u1e89\u2c73"},{base:"x",letters:"x\u24e7\uff58\u1e8b\u1e8d"},{base:"y",letters:"y\u24e8\uff59\u1ef3\xfd\u0177\u1ef9\u0233\u1e8f\xff\u1ef7\u1e99\u1ef5\u01b4\u024f\u1eff"},{base:"z",letters:"z\u24e9\uff5a\u017a\u1e91\u017c\u017e\u1e93\u1e95\u01b6\u0225\u0240\u2c6c\ua763"}],Je=new RegExp("["+Qe.map((function(e){return e.letters})).join("")+"]","g"),et={},tt=0;tt<Qe.length;tt++)for(var nt=Qe[tt],ot=0;ot<nt.letters.length;ot++)et[nt.letters[ot]]=nt.base;var it=function(e){return e.replace(Je,(function(e){return et[e]}))},rt=function(e,t){void 0===t&&(t=Ge);var n=null;function o(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];if(n&&n.lastThis===this&&t(o,n.lastArgs))return n.lastResult;var r=e.apply(this,o);return n={lastResult:r,lastArgs:o,lastThis:this},r}return o.clear=function(){n=null},o}(it),lt=function(e){return e.replace(/^\s+|\s+$/g,"")},at=function(e){return"".concat(e.label," ").concat(e.value)},st=["innerRef"];function ct(e){var t=e.innerRef,n=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var i=Object.entries(e).filter((function(e){var t=(0,u.Z)(e,1)[0];return!n.includes(t)}));return i.reduce((function(e,t){var n=(0,u.Z)(t,2),o=n[0],i=n[1];return e[o]=i,e}),{})}((0,d.Z)(e,st),"onExited","in","enter","exit","appear");return(0,c.tZ)("input",(0,s.Z)({ref:t},n,{css:(0,c.iv)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","","","")}))}var ut=function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()};var dt=["boxSizing","height","overflow","paddingRight","position"],ht={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function ft(e){e.preventDefault()}function pt(e){e.stopPropagation()}function vt(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function gt(){return"ontouchstart"in window||navigator.maxTouchPoints}var mt=!("undefined"===typeof window||!window.document||!window.document.createElement),wt=0,bt={capture:!1,passive:!1};var yt=function(e){var t=e.target;return t.ownerDocument.activeElement&&t.ownerDocument.activeElement.blur()},xt={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function kt(e){var t=e.children,n=e.lockEnabled,i=e.captureEnabled,r=function(e){var t=e.isEnabled,n=e.onBottomArrive,i=e.onBottomLeave,r=e.onTopArrive,l=e.onTopLeave,a=(0,o.useRef)(!1),s=(0,o.useRef)(!1),c=(0,o.useRef)(0),u=(0,o.useRef)(null),d=(0,o.useCallback)((function(e,t){if(null!==u.current){var o=u.current,c=o.scrollTop,d=o.scrollHeight,h=o.clientHeight,f=u.current,p=t>0,v=d-h-c,g=!1;v>t&&a.current&&(i&&i(e),a.current=!1),p&&s.current&&(l&&l(e),s.current=!1),p&&t>v?(n&&!a.current&&n(e),f.scrollTop=d,g=!0,a.current=!0):!p&&-t>c&&(r&&!s.current&&r(e),f.scrollTop=0,g=!0,s.current=!0),g&&ut(e)}}),[n,i,r,l]),h=(0,o.useCallback)((function(e){d(e,e.deltaY)}),[d]),f=(0,o.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),p=(0,o.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;d(e,t)}),[d]),v=(0,o.useCallback)((function(e){if(e){var t=!!re&&{passive:!1};e.addEventListener("wheel",h,t),e.addEventListener("touchstart",f,t),e.addEventListener("touchmove",p,t)}}),[p,f,h]),g=(0,o.useCallback)((function(e){e&&(e.removeEventListener("wheel",h,!1),e.removeEventListener("touchstart",f,!1),e.removeEventListener("touchmove",p,!1))}),[p,f,h]);return(0,o.useEffect)((function(){if(t){var e=u.current;return v(e),function(){g(e)}}}),[t,v,g]),function(e){u.current=e}}({isEnabled:void 0===i||i,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),l=function(e){var t=e.isEnabled,n=e.accountForScrollbars,i=void 0===n||n,r=(0,o.useRef)({}),l=(0,o.useRef)(null),a=(0,o.useCallback)((function(e){if(mt){var t=document.body,n=t&&t.style;if(i&&dt.forEach((function(e){var t=n&&n[e];r.current[e]=t})),i&&wt<1){var o=parseInt(r.current.paddingRight,10)||0,l=document.body?document.body.clientWidth:0,a=window.innerWidth-l+o||0;Object.keys(ht).forEach((function(e){var t=ht[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(a,"px"))}t&&gt()&&(t.addEventListener("touchmove",ft,bt),e&&(e.addEventListener("touchstart",vt,bt),e.addEventListener("touchmove",pt,bt))),wt+=1}}),[i]),s=(0,o.useCallback)((function(e){if(mt){var t=document.body,n=t&&t.style;wt=Math.max(wt-1,0),i&&wt<1&&dt.forEach((function(e){var t=r.current[e];n&&(n[e]=t)})),t&&gt()&&(t.removeEventListener("touchmove",ft,bt),e&&(e.removeEventListener("touchstart",vt,bt),e.removeEventListener("touchmove",pt,bt)))}}),[i]);return(0,o.useEffect)((function(){if(t){var e=l.current;return a(e),function(){s(e)}}}),[t,a,s]),function(e){l.current=e}}({isEnabled:n});return(0,c.tZ)(o.Fragment,null,n&&(0,c.tZ)("div",{onClick:yt,css:xt}),t((function(e){r(e),l(e)})))}var Ct={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},St=function(e){var t=e.name,n=e.onFocus;return(0,c.tZ)("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:n,css:Ct,value:"",onChange:function(){}})};function Mt(e){var t;return"undefined"!==typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function Rt(){return Mt(/^Mac/i)}function Et(){return Mt(/^iPhone/i)||Mt(/^iPad/i)||Rt()&&navigator.maxTouchPoints>1}var It={clearIndicator:Ee,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.theme,r=i.colors,l=i.borderRadius;return a({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:i.spacing.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:n?r.neutral5:r.neutral0,borderColor:n?r.neutral10:o?r.primary:r.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:o?"0 0 0 1px ".concat(r.primary):void 0,"&:hover":{borderColor:o?r.primary:r.neutral30}})},dropdownIndicator:Re,group:function(e,t){var n=e.theme.spacing;return t?{}:{paddingBottom:2*n.baseUnit,paddingTop:2*n.baseUnit}},groupHeading:function(e,t){var n=e.theme,o=n.colors,i=n.spacing;return a({label:"group",cursor:"default",display:"block"},t?{}:{color:o.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*i.baseUnit,paddingRight:3*i.baseUnit,textTransform:"uppercase"})},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing.baseUnit,r=o.colors;return a({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:n?r.neutral10:r.neutral20,marginBottom:2*i,marginTop:2*i})},input:function(e,t){var n=e.isDisabled,o=e.value,i=e.theme,r=i.spacing,l=i.colors;return a(a({visibility:n?"hidden":"visible",transform:o?"translateZ(0)":""},Le),t?{}:{margin:r.baseUnit/2,paddingBottom:r.baseUnit/2,paddingTop:r.baseUnit/2,color:l.neutral80})},loadingIndicator:function(e,t){var n=e.isFocused,o=e.size,i=e.theme,r=i.colors,l=i.spacing.baseUnit;return a({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:o,lineHeight:1,marginRight:o,textAlign:"center",verticalAlign:"middle"},t?{}:{color:n?r.neutral60:r.neutral20,padding:2*l})},loadingMessage:we,menu:function(e,t){var n,o=e.placement,i=e.theme,l=i.borderRadius,s=i.spacing,c=i.colors;return a((n={label:"menu"},(0,r.Z)(n,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(o),"100%"),(0,r.Z)(n,"position","absolute"),(0,r.Z)(n,"width","100%"),(0,r.Z)(n,"zIndex",1),n),t?{}:{backgroundColor:c.neutral0,borderRadius:l,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:s.menuGutter,marginTop:s.menuGutter})},menuList:function(e,t){var n=e.maxHeight,o=e.theme.spacing.baseUnit;return a({maxHeight:n,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:o,paddingTop:o})},menuPortal:function(e){var t=e.rect,n=e.offset,o=e.position;return{left:t.left,position:o,top:n,width:t.width,zIndex:1}},multiValue:function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,r=n.colors;return a({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:r.neutral10,borderRadius:i/2,margin:o.baseUnit/2})},multiValueLabel:function(e,t){var n=e.theme,o=n.borderRadius,i=n.colors,r=e.cropWithEllipsis;return a({overflow:"hidden",textOverflow:r||void 0===r?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:o/2,color:i.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},multiValueRemove:function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,r=n.colors,l=e.isFocused;return a({alignItems:"center",display:"flex"},t?{}:{borderRadius:i/2,backgroundColor:l?r.dangerLight:void 0,paddingLeft:o.baseUnit,paddingRight:o.baseUnit,":hover":{backgroundColor:r.dangerLight,color:r.danger}})},noOptionsMessage:me,option:function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.isSelected,r=e.theme,l=r.spacing,s=r.colors;return a({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:i?s.primary:o?s.primary25:"transparent",color:n?s.neutral20:i?s.neutral0:"inherit",padding:"".concat(2*l.baseUnit,"px ").concat(3*l.baseUnit,"px"),":active":{backgroundColor:n?void 0:i?s.primary:s.primary50}})},placeholder:function(e,t){var n=e.theme,o=n.spacing,i=n.colors;return a({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:i.neutral50,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},singleValue:function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing,r=o.colors;return a({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:n?r.neutral40:r.neutral80,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},valueContainer:function(e,t){var n=e.theme.spacing,o=e.isMulti,i=e.hasValue,r=e.selectProps.controlShouldRenderValue;return a({alignItems:"center",display:o&&i&&r?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(n.baseUnit/2,"px ").concat(2*n.baseUnit,"px")})}};var Tt,Ot={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},Pt={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:te(),captureMenuScroll:!te(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=a({ignoreCase:!0,ignoreAccents:!0,stringify:at,trim:!0,matchFrom:"any"},Tt),o=n.ignoreCase,i=n.ignoreAccents,r=n.stringify,l=n.trim,s=n.matchFrom,c=l?lt(t):t,u=l?lt(r(e)):r(e);return o&&(c=c.toLowerCase(),u=u.toLowerCase()),i&&(c=rt(c),u=it(u)),"start"===s?u.substr(0,c.length)===c:u.indexOf(c)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function Dt(e,t,n,o){return{type:"option",data:t,isDisabled:Nt(e,t,n),isSelected:Bt(e,t,n),label:Vt(e,t),value:_t(e,t),index:o}}function Ht(e,t){return e.options.map((function(n,o){if("options"in n){var i=n.options.map((function(n,o){return Dt(e,n,t,o)})).filter((function(t){return Ft(e,t)}));return i.length>0?{type:"group",data:n,options:i,index:o}:void 0}var r=Dt(e,n,t,o);return Ft(e,r)?r:void 0})).filter(le)}function zt(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,(0,Ue.Z)(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function Lt(e,t){return e.reduce((function(e,n){return"group"===n.type?e.push.apply(e,(0,Ue.Z)(n.options.map((function(e){return{data:e.data,id:"".concat(t,"-").concat(n.index,"-").concat(e.index)}})))):e.push({data:n.data,id:"".concat(t,"-").concat(n.index)}),e}),[])}function Ft(e,t){var n=e.inputValue,o=void 0===n?"":n,i=t.data,r=t.isSelected,l=t.label,a=t.value;return(!Wt(e)||!r)&&Zt(e,{label:l,value:a,data:i},o)}var At=function(e,t){var n;return(null===(n=e.find((function(e){return e.data===t})))||void 0===n?void 0:n.id)||null},Vt=function(e,t){return e.getOptionLabel(t)},_t=function(e,t){return e.getOptionValue(t)};function Nt(e,t,n){return"function"===typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function Bt(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"===typeof e.isOptionSelected)return e.isOptionSelected(t,n);var o=_t(e,t);return n.some((function(t){return _t(e,t)===o}))}function Zt(e,t,n){return!e.filterOption||e.filterOption(t,n)}var Wt=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},jt=1,Ut=function(e){(0,We.Z)(n,e);var t=(0,je.Z)(n);function n(e){var o;if((0,Be.Z)(this,n),(o=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},o.blockOptionHover=!1,o.isComposing=!1,o.commonProps=void 0,o.initialTouchX=0,o.initialTouchY=0,o.openAfterFocus=!1,o.scrollToFocusedOptionOnUpdate=!1,o.userIsDragging=void 0,o.isAppleDevice=Rt()||Et(),o.controlRef=null,o.getControlRef=function(e){o.controlRef=e},o.focusedOptionRef=null,o.getFocusedOptionRef=function(e){o.focusedOptionRef=e},o.menuListRef=null,o.getMenuListRef=function(e){o.menuListRef=e},o.inputRef=null,o.getInputRef=function(e){o.inputRef=e},o.focus=o.focusInput,o.blur=o.blurInput,o.onChange=function(e,t){var n=o.props,i=n.onChange,r=n.name;t.name=r,o.ariaOnChange(e,t),i(e,t)},o.setValue=function(e,t,n){var i=o.props,r=i.closeMenuOnSelect,l=i.isMulti,a=i.inputValue;o.onInputChange("",{action:"set-value",prevInputValue:a}),r&&(o.setState({inputIsHiddenAfterUpdate:!l}),o.onMenuClose()),o.setState({clearFocusValueOnUpdate:!0}),o.onChange(e,{action:t,option:n})},o.selectOption=function(e){var t=o.props,n=t.blurInputOnSelect,i=t.isMulti,r=t.name,l=o.state.selectValue,a=i&&o.isOptionSelected(e,l),s=o.isOptionDisabled(e,l);if(a){var c=o.getOptionValue(e);o.setValue(l.filter((function(e){return o.getOptionValue(e)!==c})),"deselect-option",e)}else{if(s)return void o.ariaOnChange(e,{action:"select-option",option:e,name:r});i?o.setValue([].concat((0,Ue.Z)(l),[e]),"select-option",e):o.setValue(e,"select-option")}n&&o.blurInput()},o.removeValue=function(e){var t=o.props.isMulti,n=o.state.selectValue,i=o.getOptionValue(e),r=n.filter((function(e){return o.getOptionValue(e)!==i})),l=ae(t,r,r[0]||null);o.onChange(l,{action:"remove-value",removedValue:e}),o.focusInput()},o.clearValue=function(){var e=o.state.selectValue;o.onChange(ae(o.props.isMulti,[],null),{action:"clear",removedValues:e})},o.popValue=function(){var e=o.props.isMulti,t=o.state.selectValue,n=t[t.length-1],i=t.slice(0,t.length-1),r=ae(e,i,i[0]||null);o.onChange(r,{action:"pop-value",removedValue:n})},o.getFocusedOptionId=function(e){return At(o.state.focusableOptionsWithIds,e)},o.getFocusableOptionsWithIds=function(){return Lt(Ht(o.props,o.state.selectValue),o.getElementId("option"))},o.getValue=function(){return o.state.selectValue},o.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return X.apply(void 0,[o.props.classNamePrefix].concat(t))},o.getOptionLabel=function(e){return Vt(o.props,e)},o.getOptionValue=function(e){return _t(o.props,e)},o.getStyles=function(e,t){var n=o.props.unstyled,i=It[e](t,n);i.boxSizing="border-box";var r=o.props.styles[e];return r?r(i,t):i},o.getClassNames=function(e,t){var n,i;return null===(n=(i=o.props.classNames)[e])||void 0===n?void 0:n.call(i,t)},o.getElementId=function(e){return"".concat(o.state.instancePrefix,"-").concat(e)},o.getComponents=function(){return e=o.props,a(a({},_e),e.components);var e},o.buildCategorizedOptions=function(){return Ht(o.props,o.state.selectValue)},o.getCategorizedOptions=function(){return o.props.menuIsOpen?o.buildCategorizedOptions():[]},o.buildFocusableOptions=function(){return zt(o.buildCategorizedOptions())},o.getFocusableOptions=function(){return o.props.menuIsOpen?o.buildFocusableOptions():[]},o.ariaOnChange=function(e,t){o.setState({ariaSelection:a({value:e},t)})},o.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),o.focusInput())},o.onMenuMouseMove=function(e){o.blockOptionHover=!1},o.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=o.props.openMenuOnClick;o.state.isFocused?o.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&o.onMenuClose():t&&o.openMenu("first"):(t&&(o.openAfterFocus=!0),o.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},o.onDropdownIndicatorMouseDown=function(e){if((!e||"mousedown"!==e.type||0===e.button)&&!o.props.isDisabled){var t=o.props,n=t.isMulti,i=t.menuIsOpen;o.focusInput(),i?(o.setState({inputIsHiddenAfterUpdate:!n}),o.onMenuClose()):o.openMenu("first"),e.preventDefault()}},o.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(o.clearValue(),e.preventDefault(),o.openAfterFocus=!1,"touchend"===e.type?o.focusInput():setTimeout((function(){return o.focusInput()})))},o.onScroll=function(e){"boolean"===typeof o.props.closeMenuOnScroll?e.target instanceof HTMLElement&&$(e.target)&&o.props.onMenuClose():"function"===typeof o.props.closeMenuOnScroll&&o.props.closeMenuOnScroll(e)&&o.props.onMenuClose()},o.onCompositionStart=function(){o.isComposing=!0},o.onCompositionEnd=function(){o.isComposing=!1},o.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(o.initialTouchX=n.clientX,o.initialTouchY=n.clientY,o.userIsDragging=!1)},o.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var i=Math.abs(n.clientX-o.initialTouchX),r=Math.abs(n.clientY-o.initialTouchY);o.userIsDragging=i>5||r>5}},o.onTouchEnd=function(e){o.userIsDragging||(o.controlRef&&!o.controlRef.contains(e.target)&&o.menuListRef&&!o.menuListRef.contains(e.target)&&o.blurInput(),o.initialTouchX=0,o.initialTouchY=0)},o.onControlTouchEnd=function(e){o.userIsDragging||o.onControlMouseDown(e)},o.onClearIndicatorTouchEnd=function(e){o.userIsDragging||o.onClearIndicatorMouseDown(e)},o.onDropdownIndicatorTouchEnd=function(e){o.userIsDragging||o.onDropdownIndicatorMouseDown(e)},o.handleInputChange=function(e){var t=o.props.inputValue,n=e.currentTarget.value;o.setState({inputIsHiddenAfterUpdate:!1}),o.onInputChange(n,{action:"input-change",prevInputValue:t}),o.props.menuIsOpen||o.onMenuOpen()},o.onInputFocus=function(e){o.props.onFocus&&o.props.onFocus(e),o.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(o.openAfterFocus||o.props.openMenuOnFocus)&&o.openMenu("first"),o.openAfterFocus=!1},o.onInputBlur=function(e){var t=o.props.inputValue;o.menuListRef&&o.menuListRef.contains(document.activeElement)?o.inputRef.focus():(o.props.onBlur&&o.props.onBlur(e),o.onInputChange("",{action:"input-blur",prevInputValue:t}),o.onMenuClose(),o.setState({focusedValue:null,isFocused:!1}))},o.onOptionHover=function(e){if(!o.blockOptionHover&&o.state.focusedOption!==e){var t=o.getFocusableOptions().indexOf(e);o.setState({focusedOption:e,focusedOptionId:t>-1?o.getFocusedOptionId(e):null})}},o.shouldHideSelectedOptions=function(){return Wt(o.props)},o.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),o.focus()},o.onKeyDown=function(e){var t=o.props,n=t.isMulti,i=t.backspaceRemovesValue,r=t.escapeClearsValue,l=t.inputValue,a=t.isClearable,s=t.isDisabled,c=t.menuIsOpen,u=t.onKeyDown,d=t.tabSelectsValue,h=t.openMenuOnFocus,f=o.state,p=f.focusedOption,v=f.focusedValue,g=f.selectValue;if(!s&&("function"!==typeof u||(u(e),!e.defaultPrevented))){switch(o.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||l)return;o.focusValue("previous");break;case"ArrowRight":if(!n||l)return;o.focusValue("next");break;case"Delete":case"Backspace":if(l)return;if(v)o.removeValue(v);else{if(!i)return;n?o.popValue():a&&o.clearValue()}break;case"Tab":if(o.isComposing)return;if(e.shiftKey||!c||!d||!p||h&&o.isOptionSelected(p,g))return;o.selectOption(p);break;case"Enter":if(229===e.keyCode)break;if(c){if(!p)return;if(o.isComposing)return;o.selectOption(p);break}return;case"Escape":c?(o.setState({inputIsHiddenAfterUpdate:!1}),o.onInputChange("",{action:"menu-close",prevInputValue:l}),o.onMenuClose()):a&&r&&o.clearValue();break;case" ":if(l)return;if(!c){o.openMenu("first");break}if(!p)return;o.selectOption(p);break;case"ArrowUp":c?o.focusOption("up"):o.openMenu("last");break;case"ArrowDown":c?o.focusOption("down"):o.openMenu("first");break;case"PageUp":if(!c)return;o.focusOption("pageup");break;case"PageDown":if(!c)return;o.focusOption("pagedown");break;case"Home":if(!c)return;o.focusOption("first");break;case"End":if(!c)return;o.focusOption("last");break;default:return}e.preventDefault()}},o.state.instancePrefix="react-select-"+(o.props.instanceId||++jt),o.state.selectValue=G(e.value),e.menuIsOpen&&o.state.selectValue.length){var i=o.getFocusableOptionsWithIds(),r=o.buildFocusableOptions(),l=r.indexOf(o.state.selectValue[0]);o.state.focusableOptionsWithIds=i,o.state.focusedOption=r[l],o.state.focusedOptionId=At(i,r[l])}return o}return(0,Ze.Z)(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&ee(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,o=t.menuIsOpen,i=this.state.isFocused;(i&&!n&&e.isDisabled||i&&o&&!e.menuIsOpen)&&this.focusInput(),i&&n&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):i||n||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(ee(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,o=n.selectValue,i=n.isFocused,r=this.buildFocusableOptions(),l="first"===e?0:r.length-1;if(!this.props.isMulti){var a=r.indexOf(o[0]);a>-1&&(l=a)}this.scrollToFocusedOptionOnUpdate=!(i&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:r[l],focusedOptionId:this.getFocusedOptionId(r[l])},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,o=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var i=n.indexOf(o);o||(i=-1);var r=n.length-1,l=-1;if(n.length){switch(e){case"previous":l=0===i?0:-1===i?r:i-1;break;case"next":i>-1&&i<r&&(l=i+1)}this.setState({inputIsHidden:-1!==l,focusedValue:n[l]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,o=this.getFocusableOptions();if(o.length){var i=0,r=o.indexOf(n);n||(r=-1),"up"===e?i=r>0?r-1:o.length-1:"down"===e?i=(r+1)%o.length:"pageup"===e?(i=r-t)<0&&(i=0):"pagedown"===e?(i=r+t)>o.length-1&&(i=o.length-1):"last"===e&&(i=o.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:o[i],focusedValue:null,focusedOptionId:this.getFocusedOptionId(o[i])})}}},{key:"getTheme",value:function(){return this.props.theme?"function"===typeof this.props.theme?this.props.theme(Ot):a(a({},Ot),this.props.theme):Ot}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,o=this.getClassNames,i=this.getValue,r=this.selectOption,l=this.setValue,a=this.props,s=a.isMulti,c=a.isRtl,u=a.options;return{clearValue:e,cx:t,getStyles:n,getClassNames:o,getValue:i,hasValue:this.hasValue(),isMulti:s,isRtl:c,options:u,selectOption:r,selectProps:a,setValue:l,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return Nt(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return Bt(this.props,e,t)}},{key:"filterOption",value:function(e,t){return Zt(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"===typeof this.props.formatOptionLabel){var n=this.props.inputValue,o=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:o})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,i=e.inputId,r=e.inputValue,l=e.tabIndex,c=e.form,u=e.menuIsOpen,d=e.required,h=this.getComponents().Input,f=this.state,p=f.inputIsHidden,v=f.ariaSelection,g=this.commonProps,m=i||this.getElementId("input"),w=a(a(a({"aria-autocomplete":"list","aria-expanded":u,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":d,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},u&&{"aria-controls":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null===v||void 0===v?void 0:v.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?o.createElement(h,(0,s.Z)({},g,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:m,innerRef:this.getInputRef,isDisabled:t,isHidden:p,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:l,form:c,type:"text",value:r},w)):o.createElement(ct,(0,s.Z)({id:m,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:j,onFocus:this.onInputFocus,disabled:t,tabIndex:l,inputMode:"none",form:c,value:""},w))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,i=t.MultiValueContainer,r=t.MultiValueLabel,l=t.MultiValueRemove,a=t.SingleValue,c=t.Placeholder,u=this.commonProps,d=this.props,h=d.controlShouldRenderValue,f=d.isDisabled,p=d.isMulti,v=d.inputValue,g=d.placeholder,m=this.state,w=m.selectValue,b=m.focusedValue,y=m.isFocused;if(!this.hasValue()||!h)return v?null:o.createElement(c,(0,s.Z)({},u,{key:"placeholder",isDisabled:f,isFocused:y,innerProps:{id:this.getElementId("placeholder")}}),g);if(p)return w.map((function(t,a){var c=t===b,d="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return o.createElement(n,(0,s.Z)({},u,{components:{Container:i,Label:r,Remove:l},isFocused:c,isDisabled:f,key:d,index:a,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(v)return null;var x=w[0];return o.createElement(a,(0,s.Z)({},u,{data:x,isDisabled:f}),this.formatOptionLabel(x,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,i=n.isDisabled,r=n.isLoading,l=this.state.isFocused;if(!this.isClearable()||!e||i||!this.hasValue()||r)return null;var a={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return o.createElement(e,(0,s.Z)({},t,{innerProps:a,isFocused:l}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,i=n.isDisabled,r=n.isLoading,l=this.state.isFocused;if(!e||!r)return null;return o.createElement(e,(0,s.Z)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:i,isFocused:l}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var i=this.commonProps,r=this.props.isDisabled,l=this.state.isFocused;return o.createElement(n,(0,s.Z)({},i,{isDisabled:r,isFocused:l}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,i=this.state.isFocused,r={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return o.createElement(e,(0,s.Z)({},t,{innerProps:r,isDisabled:n,isFocused:i}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,i=t.GroupHeading,r=t.Menu,l=t.MenuList,a=t.MenuPortal,c=t.LoadingMessage,u=t.NoOptionsMessage,d=t.Option,h=this.commonProps,f=this.state.focusedOption,p=this.props,v=p.captureMenuScroll,g=p.inputValue,m=p.isLoading,w=p.loadingMessage,b=p.minMenuHeight,y=p.maxMenuHeight,x=p.menuIsOpen,k=p.menuPlacement,C=p.menuPosition,S=p.menuPortalTarget,M=p.menuShouldBlockScroll,R=p.menuShouldScrollIntoView,E=p.noOptionsMessage,I=p.onMenuScrollToTop,T=p.onMenuScrollToBottom;if(!x)return null;var O,P=function(t,n){var i=t.type,r=t.data,l=t.isDisabled,a=t.isSelected,c=t.label,u=t.value,p=f===r,v=l?void 0:function(){return e.onOptionHover(r)},g=l?void 0:function(){return e.selectOption(r)},m="".concat(e.getElementId("option"),"-").concat(n),w={id:m,onClick:g,onMouseMove:v,onMouseOver:v,tabIndex:-1,role:"option","aria-selected":e.isAppleDevice?void 0:a};return o.createElement(d,(0,s.Z)({},h,{innerProps:w,data:r,isDisabled:l,isSelected:a,key:m,label:c,type:i,value:u,isFocused:p,innerRef:p?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())O=this.getCategorizedOptions().map((function(t){if("group"===t.type){var r=t.data,l=t.options,a=t.index,c="".concat(e.getElementId("group"),"-").concat(a),u="".concat(c,"-heading");return o.createElement(n,(0,s.Z)({},h,{key:c,data:r,options:l,Heading:i,headingProps:{id:u,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return P(e,"".concat(a,"-").concat(e.index))})))}if("option"===t.type)return P(t,"".concat(t.index))}));else if(m){var D=w({inputValue:g});if(null===D)return null;O=o.createElement(c,h,D)}else{var H=E({inputValue:g});if(null===H)return null;O=o.createElement(u,h,H)}var z={minMenuHeight:b,maxMenuHeight:y,menuPlacement:k,menuPosition:C,menuShouldScrollIntoView:R},L=o.createElement(pe,(0,s.Z)({},h,z),(function(t){var n=t.ref,i=t.placerProps,a=i.placement,c=i.maxHeight;return o.createElement(r,(0,s.Z)({},h,z,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:m,placement:a}),o.createElement(kt,{captureEnabled:v,onTopArrive:I,onBottomArrive:T,lockEnabled:M},(function(t){return o.createElement(l,(0,s.Z)({},h,{innerRef:function(n){e.getMenuListRef(n),t(n)},innerProps:{role:"listbox","aria-multiselectable":h.isMulti,id:e.getElementId("listbox")},isLoading:m,maxHeight:c,focusedOption:f}),O)})))}));return S||"fixed"===C?o.createElement(a,(0,s.Z)({},h,{appendTo:S,controlElement:this.controlRef,menuPlacement:k,menuPosition:C}),L):L}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,i=t.isDisabled,r=t.isMulti,l=t.name,a=t.required,s=this.state.selectValue;if(a&&!this.hasValue()&&!i)return o.createElement(St,{name:l,onFocus:this.onValueInputFocus});if(l&&!i){if(r){if(n){var c=s.map((function(t){return e.getOptionValue(t)})).join(n);return o.createElement("input",{name:l,type:"hidden",value:c})}var u=s.length>0?s.map((function(t,n){return o.createElement("input",{key:"i-".concat(n),name:l,type:"hidden",value:e.getOptionValue(t)})})):o.createElement("input",{name:l,type:"hidden",value:""});return o.createElement("div",null,u)}var d=s[0]?this.getOptionValue(s[0]):"";return o.createElement("input",{name:l,type:"hidden",value:d})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,i=t.focusedOption,r=t.focusedValue,l=t.isFocused,a=t.selectValue,c=this.getFocusableOptions();return o.createElement(qe,(0,s.Z)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:i,focusedValue:r,isFocused:l,selectValue:a,focusableOptions:c,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,i=e.SelectContainer,r=e.ValueContainer,l=this.props,a=l.className,c=l.id,u=l.isDisabled,d=l.menuIsOpen,h=this.state.isFocused,f=this.commonProps=this.getCommonProps();return o.createElement(i,(0,s.Z)({},f,{className:a,innerProps:{id:c,onKeyDown:this.onKeyDown},isDisabled:u,isFocused:h}),this.renderLiveRegion(),o.createElement(t,(0,s.Z)({},f,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:u,isFocused:h,menuIsOpen:d}),o.createElement(r,(0,s.Z)({},f,{isDisabled:u}),this.renderPlaceholderOrValue(),this.renderInput()),o.createElement(n,(0,s.Z)({},f,{isDisabled:u}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,o=t.clearFocusValueOnUpdate,i=t.inputIsHiddenAfterUpdate,r=t.ariaSelection,l=t.isFocused,s=t.prevWasFocused,c=t.instancePrefix,u=e.options,d=e.value,h=e.menuIsOpen,f=e.inputValue,p=e.isMulti,v=G(d),g={};if(n&&(d!==n.value||u!==n.options||h!==n.menuIsOpen||f!==n.inputValue)){var m=h?function(e,t){return zt(Ht(e,t))}(e,v):[],w=h?Lt(Ht(e,v),"".concat(c,"-option")):[],b=o?function(e,t){var n=e.focusedValue,o=e.selectValue.indexOf(n);if(o>-1){if(t.indexOf(n)>-1)return n;if(o<t.length)return t[o]}return null}(t,v):null,y=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,m);g={selectValue:v,focusedOption:y,focusedOptionId:At(w,y),focusableOptionsWithIds:w,focusedValue:b,clearFocusValueOnUpdate:!1}}var x=null!=i&&e!==n?{inputIsHidden:i,inputIsHiddenAfterUpdate:void 0}:{},k=r,C=l&&s;return l&&!C&&(k={value:ae(p,v,v[0]||null),options:v,action:"initial-input-focus"},C=!s),"initial-input-focus"===(null===r||void 0===r?void 0:r.action)&&(k=null),a(a(a({},g),x),{},{prevProps:e,ariaSelection:k,prevWasFocused:C})}}]),n}(o.Component);Ut.defaultProps=Pt;n(17715);var Xt=(0,o.forwardRef)((function(e,t){var n=function(e){var t=e.defaultInputValue,n=void 0===t?"":t,i=e.defaultMenuIsOpen,r=void 0!==i&&i,l=e.defaultValue,s=void 0===l?null:l,c=e.inputValue,h=e.menuIsOpen,f=e.onChange,p=e.onInputChange,v=e.onMenuClose,g=e.onMenuOpen,m=e.value,w=(0,d.Z)(e,Ne),b=(0,o.useState)(void 0!==c?c:n),y=(0,u.Z)(b,2),x=y[0],k=y[1],C=(0,o.useState)(void 0!==h?h:r),S=(0,u.Z)(C,2),M=S[0],R=S[1],E=(0,o.useState)(void 0!==m?m:s),I=(0,u.Z)(E,2),T=I[0],O=I[1],P=(0,o.useCallback)((function(e,t){"function"===typeof f&&f(e,t),O(e)}),[f]),D=(0,o.useCallback)((function(e,t){var n;"function"===typeof p&&(n=p(e,t)),k(void 0!==n?n:e)}),[p]),H=(0,o.useCallback)((function(){"function"===typeof g&&g(),R(!0)}),[g]),z=(0,o.useCallback)((function(){"function"===typeof v&&v(),R(!1)}),[v]),L=void 0!==c?c:x,F=void 0!==h?h:M,A=void 0!==m?m:T;return a(a({},w),{},{inputValue:L,menuIsOpen:F,onChange:P,onInputChange:D,onMenuClose:z,onMenuOpen:H,value:A})}(e);return o.createElement(Ut,(0,s.Z)({ref:t},n))})),Gt=Xt,Yt=n(3263),Kt=n(31208),$t=n(67930),qt=n(39806);const Qt=e=>{const{Menu:t}=_e,{children:n,...i}=e;return o.createElement(t,{...i},n)},Jt=(0,i.z)("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),en=(0,i.z)("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),tn=(0,i.z)("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),nn=e=>{const{value:t,onFinishedEditing:n,initialValue:i}=e,{allowedValues:r,value:l}=t.data,[a,s]=o.useState(l),[c,u]=o.useState(null!==i&&void 0!==i?i:""),d=(0,Yt.Fg)(),h=o.useMemo((()=>r.map((e=>{var t;return"string"===typeof e||null===e||void 0===e?{value:e,label:null!==(t=null===e||void 0===e?void 0:e.toString())&&void 0!==t?t:""}:e}))),[r]);return t.readonly?o.createElement(tn,null,o.createElement(Kt.K,{highlight:!0,autoFocus:!1,disabled:!0,value:null!==a&&void 0!==a?a:"",onChange:()=>{}})):o.createElement(Jt,null,o.createElement(Gt,{className:"glide-select",inputValue:c,onInputChange:u,menuPlacement:"auto",value:h.find((e=>e.value===a)),styles:{control:e=>({...e,border:0,boxShadow:"none"}),option:(e,t)=>{let{isFocused:n}=t;return{...e,fontSize:d.editorFontSize,fontFamily:d.fontFamily,cursor:n?"pointer":void 0,paddingLeft:d.cellHorizontalPadding,paddingRight:d.cellHorizontalPadding,":active":{...e[":active"],color:d.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}}}},theme:e=>({...e,colors:{...e.colors,neutral0:d.bgCell,neutral5:d.bgCell,neutral10:d.bgCell,neutral20:d.bgCellMedium,neutral30:d.bgCellMedium,neutral40:d.bgCellMedium,neutral50:d.textLight,neutral60:d.textMedium,neutral70:d.textMedium,neutral80:d.textDark,neutral90:d.textDark,neutral100:d.textDark,primary:d.accentColor,primary75:d.accentColor,primary50:d.accentColor,primary25:d.accentLight}}),menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:e=>o.createElement(en,null,o.createElement(Qt,{className:"click-outside-ignore",...e}))},options:h,onChange:async e=>{null!==e&&(s(e.value),await new Promise((e=>window.requestAnimationFrame(e))),n({...t,data:{...t.data,value:e.value}}))}}))},on={kind:$t.p6.Custom,isMatch:e=>"dropdown-cell"===e.data.kind,draw:(e,t)=>{var n;const{ctx:o,theme:i,rect:r}=e,{value:l}=t.data,a=t.data.allowedValues.find((e=>"string"===typeof e||null===e||void 0===e?e===l:e.value===l)),s="string"===typeof a?a:null!==(n=null===a||void 0===a?void 0:a.label)&&void 0!==n?n:"";return s&&(o.fillStyle=i.textDark,o.fillText(s,r.x+i.cellHorizontalPadding,r.y+r.height/2+(0,qt.aX)(o,i))),!0},measure:(e,t,n)=>{const{value:o}=t.data;return(o?e.measureText(o).width:0)+2*n.cellHorizontalPadding},provideEditor:()=>({editor:nn,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.includes(e)?e:t.value})}},85e3:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var o=n(67930),i=n(39806),r=n(66845);function l(e,t,n,o,i,r){o<=0||i<=0||("number"===typeof r&&r<=0?e.rect(t,n,o,i):("number"===typeof r&&(r={tl:r,tr:r,br:r,bl:r}),(r={tl:Math.min(r.tl,i/2,o/2),tr:Math.min(r.tr,i/2,o/2),bl:Math.min(r.bl,i/2,o/2),br:Math.min(r.br,i/2,o/2)}).tl=Math.max(0,r.tl),r.tr=Math.max(0,r.tr),r.br=Math.max(0,r.br),r.bl=Math.max(0,r.bl),e.moveTo(t+r.tl,n),e.arcTo(t+o,n,t+o,n+r.tr,r.tr),e.arcTo(t+o,n+i,t+o-r.br,n+i,r.br),e.arcTo(t,n+i,t,n+i-r.bl,r.bl),e.arcTo(t,n,t+r.tl,n,r.tl)))}const a={marginRight:8},s={display:"flex",alignItems:"center",flexGrow:1},c={kind:o.p6.Custom,isMatch:e=>"range-cell"===e.data.kind,draw:(e,t)=>{const{ctx:n,theme:o,rect:r}=e,{min:a,max:s,value:c,label:u,measureLabel:d}=t.data,h=r.x+o.cellHorizontalPadding,f=r.y+r.height/2,p=(c-a)/(s-a);n.save();let v=0;void 0!==u&&(n.font="12px ".concat(o.fontFamily),v=(0,i.P7)(null!==d&&void 0!==d?d:u,n,"12px ".concat(o.fontFamily)).width+o.cellHorizontalPadding);const g=r.width-2*o.cellHorizontalPadding-v;if(g>=6){const e=n.createLinearGradient(h,f,h+g,f);e.addColorStop(0,o.accentColor),e.addColorStop(p,o.accentColor),e.addColorStop(p,o.bgBubble),e.addColorStop(1,o.bgBubble),n.beginPath(),n.fillStyle=e,l(n,h,f-3,g,6,3),n.fill(),n.beginPath(),l(n,h+.5,f-3+.5,g-1,5,2.5),n.strokeStyle=o.accentLight,n.lineWidth=1,n.stroke()}return void 0!==u&&(n.textAlign="right",n.fillStyle=o.textDark,n.fillText(u,r.x+r.width-o.cellHorizontalPadding,f+(0,i.aX)(n,"12px ".concat(o.fontFamily)))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,o=t.value.toString(),i=t.min.toString(),l=t.max.toString(),c=t.step.toString();return r.createElement("label",{style:s},r.createElement("input",{style:a,type:"range",value:o,min:i,max:l,step:c,onChange:n=>{e.onChange({...e.value,data:{...t,value:Number(n.target.value)}})},disabled:n}),o)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}}},97613:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(67930),i=n(85735);const r={kind:o.p6.Custom,isMatch:e=>"sparkline-cell"===e.data.kind,needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:o,rect:r,hoverAmount:l,hoverX:a}=e;let{values:s,yAxis:c,color:u,graphKind:d="area",displayValues:h,hideAxis:f}=t.data;const[p,v]=c;if(0===s.length)return!0;s=s.map((e=>Math.min(1,Math.max(0,(e-p)/(v-p)))));const g=o.cellHorizontalPadding,m=g+r.x,w=r.y+3,b=r.height-6,y=r.width-2*g,x=v<=0?w:p>=0?w+b:w+b*(v/(v-p));if(!f&&p<=0&&v>=0&&(n.beginPath(),n.moveTo(m,x),n.lineTo(m+y,x),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=o.textLight,n.stroke(),n.globalAlpha=1),"bar"===d){var k;n.beginPath();const e=2,i=(y-(s.length-1)*e)/s.length;let r=m;for(const t of s){const o=w+b-t*b;n.moveTo(r,x),n.lineTo(r+i,x),n.lineTo(r+i,o),n.lineTo(r,o),r+=i+e}n.fillStyle=null!==(k=t.data.color)&&void 0!==k?k:o.accentColor,n.fill()}else{1===s.length&&(s=[s[0],s[0]],h&&(h=[h[0],h[0]])),n.beginPath();const e=(r.width-16)/(s.length-1),t=s.map(((t,n)=>({x:m+e*n,y:w+b-t*b})));n.moveTo(t[0].x,t[0].y);let c=0;if(t.length>2)for(c=1;c<t.length-2;c++){const e=(t[c].x+t[c+1].x)/2,o=(t[c].y+t[c+1].y)/2;n.quadraticCurveTo(t[c].x,t[c].y,e,o)}if(n.quadraticCurveTo(t[c].x,t[c].y,t[c+1].x,t[c+1].y),n.strokeStyle=null!==u&&void 0!==u?u:o.accentColor,n.lineWidth=1+.5*l,n.stroke(),n.lineTo(r.x+r.width-g,x),n.lineTo(r.x+g,x),n.closePath(),"area"===d){n.globalAlpha=.2+.2*l;const e=n.createLinearGradient(0,w,0,w*****b);e.addColorStop(0,null!==u&&void 0!==u?u:o.accentColor);const[t,r,a]=(0,i.dF)(null!==u&&void 0!==u?u:o.accentColor);e.addColorStop(1,"rgba(".concat(t,", ").concat(r,", ").concat(a,", 0)")),n.fillStyle=e,n.fill(),n.globalAlpha=1}if(void 0!==a&&("line"===d||"area"===d)&&void 0!==h){n.beginPath();const t=Math.min(s.length-1,Math.max(0,Math.round((a-g)/e)));n.moveTo(m+t*e,r.y+1),n.lineTo(m+t*e,r.y+r.height),n.lineWidth=1,n.strokeStyle=o.textLight,n.stroke(),n.save(),n.font="8px ".concat(o.fontFamily),n.fillStyle=o.textMedium,n.textBaseline="top",n.fillText(h[t],m,r.y+o.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t}},32700:(e,t,n)=>{"use strict";n.d(t,{fF:()=>s});var o=n(67930),i=n(7974),r=n(66845);function l(e){var t,n,i,r,l;switch(e.kind){case o.p6.Number:return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:"";case o.p6.Boolean:return null!==(i=null===(r=e.data)||void 0===r?void 0:r.toString())&&void 0!==i?i:"";case o.p6.Markdown:case o.p6.RowID:case o.p6.Text:case o.p6.Uri:return null!==(l=e.data)&&void 0!==l?l:"";case o.p6.Bubble:case o.p6.Image:return e.data.join("");case o.p6.Drilldown:return e.data.map((e=>e.text)).join("");case o.p6.Protected:case o.p6.Loading:return"";case o.p6.Custom:return e.copyData}}function a(e){if("number"===typeof e)return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function s(e){var t;const{sort:n,rows:o,getCellContent:s}=e;let c=void 0===n?void 0:e.columns.findIndex((e=>n.column===e||void 0!==e.id&&n.column.id===e.id));-1===c&&(c=void 0);const u=null!==(t=null===n||void 0===n?void 0:n.direction)&&void 0!==t?t:"asc",d=r.useMemo((()=>{if(void 0===c)return;const e=new Array(o),t=[c,0];for(let n=0;n<o;n++)t[1]=n,e[n]=l(s(t));let r;return r="raw"===(null===n||void 0===n?void 0:n.mode)?i(o).sort(((t,n)=>function(e,t){return e>t?1:e===t?0:-1}(e[t],e[n]))):"smart"===(null===n||void 0===n?void 0:n.mode)?i(o).sort(((t,n)=>function(e,t){return e=a(e),t=a(t),"string"===typeof e&&"string"===typeof t?e.localeCompare(t):"number"===typeof e&&"number"===typeof t?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}(e[t],e[n]))):i(o).sort(((t,n)=>e[t].localeCompare(e[n]))),"desc"===u&&r.reverse(),r}),[s,o,null===n||void 0===n?void 0:n.mode,u,c]),h=r.useCallback((e=>void 0===d?e:d[e]),[d]),f=r.useCallback((e=>{let[t,n]=e;return void 0===d||(n=d[n]),s([t,n])}),[s,d]);return void 0===d?{getCellContent:e.getCellContent,getOriginalIndex:h}:{getOriginalIndex:h,getCellContent:f}}},3263:(e,t,n)=>{"use strict";n.d(t,{Fg:()=>c,Ni:()=>s,Zu:()=>a,be:()=>r,yR:()=>u});var o=n(66845),i=n(85735);function r(e){var t,n;return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":null!==(t=e.textGroupHeader)&&void 0!==t?t:e.textHeader,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":null!==(n=e.horizontalBorderColor)&&void 0!==n?n:e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":"".concat(e.cellHorizontalPadding,"px"),"--gdg-cell-vertical-padding":"".concat(e.cellVerticalPadding,"px"),"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,...void 0===e.resizeIndicatorColor?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...void 0===e.headerBottomBorderColor?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...void 0===e.roundingRadius?{}:{"--gdg-rounding-radius":"".concat(e.roundingRadius,"px")}}}const l={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4};function a(){return l}const s=o.createContext(l);function c(){return o.useContext(s)}function u(e){const t={...e};for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];for(const l of o)if(void 0!==l)for(const e in l)l.hasOwnProperty(e)&&(t[e]="bgCell"===e?(0,i.NH)(l[e],t[e]):l[e]);return void 0!==t.headerFontFull&&e.fontFamily===t.fontFamily&&e.headerFontStyle===t.headerFontStyle||(t.headerFontFull="".concat(t.headerFontStyle," ").concat(t.fontFamily)),void 0!==t.baseFontFull&&e.fontFamily===t.fontFamily&&e.baseFontStyle===t.baseFontStyle||(t.baseFontFull="".concat(t.baseFontStyle," ").concat(t.fontFamily)),void 0!==t.markerFontFull&&e.fontFamily===t.fontFamily&&e.markerFontStyle===t.markerFontStyle||(t.markerFontFull="".concat(t.markerFontStyle," ").concat(t.fontFamily)),t}},63674:(e,t,n)=>{"use strict";function o(e){}function i(){throw new Error(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This should not happen")}function r(e){if(!e)return i(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Assertion failed")}function l(e,t){return i(null!==t&&void 0!==t?t:"Hell froze over")}function a(e,t){try{return e()}catch{return t}}n.d(t,{NG:()=>o,hu:()=>r,vE:()=>l,vZ:()=>c,wY:()=>a});const s=Object.prototype.hasOwnProperty;function c(e,t){let n,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((o=e.length)===t.length)for(;o--&&c(e[o],t[o]););return-1===o}if(!n||"object"===typeof e){for(n in o=0,e){if(s.call(e,n)&&++o&&!s.call(t,n))return!1;if(!(n in t)||!c(e[n],t[n]))return!1}return Object.keys(t).length===o}}return e!==e&&t!==t}},965:(e,t,n)=>{"use strict";n.d(t,{Ht:()=>c,Iz:()=>y,MC:()=>v,OR:()=>l,Qo:()=>h,Qy:()=>g,Wy:()=>p,XC:()=>d,ig:()=>k,jM:()=>C,kq:()=>u,o7:()=>w,qJ:()=>a,qq:()=>f,vE:()=>S});var o=n(66845),i=n(56797),r=n(63674);function l(e,t,n,i){let r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const l=o.useRef();l.current=t,o.useEffect((()=>{if(null===n||void 0===n.addEventListener)return;const t=n,o=e=>{var n;null===(n=l.current)||void 0===n||n.call(t,e)};return t.addEventListener(e,o,{passive:i,capture:r}),()=>{t.removeEventListener(e,o,{capture:r})}}),[e,n,i,r])}function a(e,t){return void 0===e?void 0:t}const s=Math.PI;function c(e){return e*s/180}const u=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),d=(e,t,n,o,i)=>{switch(e){case"left":return Math.floor(t)+o+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-o-i/2}},h=(e,t,n)=>Math.min(e,t-2*n),f=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,p=e=>{var t;const n=null!==(t=e.fgColor)&&void 0!==t?t:"currentColor";return o.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),o.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},v=e=>{var t;const n=null!==(t=e.fgColor)&&void 0!==t?t:"currentColor";return o.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function g(e,t,n){const[r,l]=o.useState(e),a=o.useRef(!0);o.useEffect((()=>()=>{a.current=!1}),[]);const s=o.useRef(i((e=>{a.current&&l(e)}),n));return o.useLayoutEffect((()=>{a.current&&s.current((()=>e()))}),t),r}const m=new RegExp("^[^A-Za-z\xc0-\xd6\xd8-\xf6\xf8-\u02b8\u0300-\u0590\u0800-\u1fff\u200e\u2c00-\ufb1c\ufe00-\ufe6f\ufefd-\uffff]*[\u0591-\u07ff\ufb1d-\ufdfd\ufe70-\ufefc]");function w(e){return m.test(e)?"rtl":"not-rtl"}let b;function y(){if("undefined"===typeof document)return 0;if(void 0!==b)return b;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let o=e.offsetWidth;return n===o&&(o=t.clientWidth),t.remove(),b=n-o,b}const x=Symbol();function k(e){const t=o.useRef([x,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,i]=o.useState(e),[,r]=o.useState(),l=o.useCallback((e=>{const n=t.current[0];n!==x&&(e="function"===typeof e?e(n):e)===n||(n!==x&&r({}),i((t=>"function"===typeof e?e(n===x?t:n):e)),t.current[0]=x)}),[]),a=o.useCallback((()=>{t.current[0]=x,r({})}),[]);return[t.current[0]===x?n:t.current[0],l,a]}function C(e){if(0===e.length)return"";let t=0,n=0;for(const o of e){if(n+=o.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function S(e){const t=o.useRef(e);return(0,r.vZ)(e,t.current)||(t.current=e),t.current}},78170:(e,t,n)=>{"use strict";n.d(t,{F:()=>An});var o=n(66845),i=n(63674),r=n(51586),l=n(17015),a=n(86995),s=n(7974),c=n(56797),u=n(67930),d=n(39806),h=n(64649);const f=1<<21;function p(e,t){return(t+2)*f+e}function v(e){return e%f}function g(e){return Math.floor(e/f)-2}function m(e){return[v(e),g(e)]}class w{constructor(){(0,h.Z)(this,"visibleWindow",{x:0,y:0,width:0,height:0}),(0,h.Z)(this,"freezeCols",0),(0,h.Z)(this,"freezeRows",[]),(0,h.Z)(this,"isInWindow",(e=>{const t=v(e),n=g(e),o=this.visibleWindow,i=t>=o.x&&t<=o.x+o.width||t<this.freezeCols,r=n>=o.y&&n<=o.y+o.height||this.freezeRows.includes(n);return i&&r}))}setWindow(e,t,n){this.visibleWindow.x===e.x&&this.visibleWindow.y===e.y&&this.visibleWindow.width===e.width&&this.visibleWindow.height===e.height&&this.freezeCols===t&&(0,i.vZ)(this.freezeRows,n)||(this.visibleWindow=e,this.freezeCols=t,this.freezeRows=n,this.clearOutOfWindow())}}class b extends w{constructor(){super(...arguments),(0,h.Z)(this,"cache",new Map),(0,h.Z)(this,"setValue",((e,t)=>{this.cache.set(p(e[0],e[1]),t)})),(0,h.Z)(this,"getValue",(e=>this.cache.get(p(e[0],e[1])))),(0,h.Z)(this,"clearOutOfWindow",(()=>{for(const[e]of this.cache.entries())this.isInWindow(e)||this.cache.delete(e)}))}}class y{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];(0,h.Z)(this,"cells",void 0),this.cells=new Set(e.map((e=>p(e[0],e[1]))))}add(e){this.cells.add(p(e[0],e[1]))}has(e){return void 0!==e&&this.cells.has(p(e[0],e[1]))}remove(e){this.cells.delete(p(e[0],e[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const e of this.cells){if(g(e)<0)return!0}return!1}hasItemInRectangle(e){for(let t=e.y;t<e.y+e.height;t++)for(let n=e.x;n<e.x+e.width;n++)if(this.cells.has(p(n,t)))return!0;return!1}hasItemInRegion(e){for(const t of e)if(this.hasItemInRectangle(t))return!0;return!1}*values(){for(const e of this.cells)yield m(e)}}class x{constructor(e,t){(0,h.Z)(this,"onSettled",void 0),(0,h.Z)(this,"spriteMap",new Map),(0,h.Z)(this,"headerIcons",void 0),(0,h.Z)(this,"inFlight",0),this.onSettled=t,this.headerIcons=null!==e&&void 0!==e?e:{}}drawSprite(e,t,n,o,i,r,l){let a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1;const[s,c]=function(e,t){return"normal"===e?[t.bgIconHeader,t.fgIconHeader]:"selected"===e?["white",t.accentColor]:[t.accentColor,t.bgHeader]}(t,l),u=r*Math.ceil(window.devicePixelRatio),d="".concat(s,"_").concat(c,"_").concat(u,"_").concat(e);let h=this.spriteMap.get(d);if(void 0===h){const t=this.headerIcons[e];if(void 0===t)return;h=document.createElement("canvas");const n=h.getContext("2d");if(null===n)return;const o=new Image;o.src="data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(t({fgColor:c,bgColor:s}))),this.spriteMap.set(d,h);const i=o.decode();if(void 0===i)return;this.inFlight++,i.then((()=>{n.drawImage(o,0,0,u,u)})).finally((()=>{this.inFlight--,0===this.inFlight&&this.onSettled()}))}else a<1&&(n.globalAlpha=a),n.drawImage(h,0,0,u,u,o,i,r,r),a<1&&(n.globalAlpha=1)}}var k=n(965),C=n(85735);function S(e){if(0===e.length)return;let t;for(const o of e){var n;t=Math.min(null!==(n=t)&&void 0!==n?n:o.y,o.y)}}function M(e,t,n,o,i,r,l,a,s){var c;a=null!==(c=a)&&void 0!==c?c:t;let u=t,d=e;const h=o-r;let f=!1;for(;u<n&&d<h;){const e=i(d);if(u+e>a&&!0===s(u,d,e,!1,l&&d===o-1)){f=!0;break}u+=e,d++}if(!f){u=n;for(let e=0;e<r;e++){d=o-1-e;const t=i(d);u-=t,s(u,d,t,!0,l&&d===o-1)}}}function R(e,t,n,o,i,r){let l=0,a=0;const s=i+o;for(const c of e){if(!0===r(c,c.sticky?a:l+n,s,c.sticky?0:a,t))break;l+=c.width,a+=c.sticky?c.width:0}}function E(e,t,n,o,i){let r=0,l=0;for(let s=0;s<e.length;s++){var a;const c=e[s];let u=s+1,h=c.width;for(c.sticky&&(l+=h);u<e.length&&(0,d.PU)(e[u].group,c.group)&&e[u].sticky===e[s].sticky;){const t=e[u];h+=t.width,u++,s++,t.sticky&&(l+=t.width)}const f=r+(c.sticky?0:n),p=c.sticky?0:Math.max(0,l-f),v=Math.min(h-p,t-(f+p));i([c.sourceIndex,e[u-1].sourceIndex],null!==(a=c.group)&&void 0!==a?a:"",f+p,0,v,o),r+=h}}function I(e,t,n,o,i,r,l){var a,s;const[c,u]=e;let d,h;const f=null!==(a=null===(s=l.find((e=>!e.sticky)))||void 0===s?void 0:s.sourceIndex)&&void 0!==a?a:0;if(u>f){const e=Math.max(c,f);let a=t,s=o;for(let t=r.sourceIndex-1;t>=e;t--)a-=l[t].width,s+=l[t].width;for(let t=r.sourceIndex+1;t<=u;t++)s+=l[t].width;h={x:a,y:n,width:s,height:i}}if(f>c){const e=Math.min(u,f-1);let a=t,s=o;for(let t=r.sourceIndex-1;t>=c;t--)a-=l[t].width,s+=l[t].width;for(let t=r.sourceIndex+1;t<=e;t++)s+=l[t].width;d={x:a,y:n,width:s,height:i}}return[d,h]}var T=n(3263);function O(e,t,n,o,i,r,l,a){return e<=i+l&&i<=e+n&&t<=r+a&&r<=t+o}function P(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function D(e,t){const n=Math.min(e.x,t.x),o=Math.min(e.y,t.y);return{x:n,y:o,width:Math.max(e.x+e.width,t.x+t.width)-n,height:Math.max(e.y+e.height,t.y+t.height)-o}}function H(e,t,n,o){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=t+4,r=n+4,l=-4-e.x,a=e.x+e.width-i,s=-4-e.y,c=e.y+e.height-r,u=l>0?e.x+Math.floor(l/o)*o:e.x,d=a>0?e.x+e.width-Math.floor(a/o)*o:e.x+e.width,h=s>0?e.y+Math.floor(s/o)*o:e.y;return{x:u,y:h,width:d-u,height:(c>0?e.y+e.height-Math.floor(c/o)*o:e.y+e.height)-h}}const z={kind:u.p6.Loading,allowOverlay:!1};function L(e,t,n,o,i,r,l,a,s,c,h,f,p,v,g,m,w,b,y,x,k,E,P,D,H,L,F,A,V,_,N,Z,W,j,U){var X;let G=null!==(X=null===x||void 0===x?void 0:x.size)&&void 0!==X?X:Number.MAX_SAFE_INTEGER;const Y=performance.now();let K=_.baseFontFull;e.font=K;const $={ctx:e},q=[0,0],Q=w>0?(0,d.YN)(s,w,c):0;let J,ee;const te=S(y);return R(t,a,r,l,i,((t,r,l,a,S)=>{var R;const X=Math.max(0,a-r),ne=r+X,oe=i+1,ie=t.width-X,re=o-i-1;if(y.length>0){let e=!1;for(let t=0;t<y.length;t++){const n=y[t];if(O(ne,oe,ie,re,n.x,n.y,n.width,n.height)){e=!0;break}}if(!e)return}const le=()=>{e.save(),e.beginPath(),e.rect(ne,oe,ie,re),e.clip()},ae=k.columns.hasIndex(t.sourceIndex),se=f(null!==(R=t.group)&&void 0!==R?R:"").overrideTheme,ce=void 0===t.themeOverride&&void 0===se?_:(0,T.yR)(_,se,t.themeOverride),ue=ce.baseFontFull;let de;return ue!==K&&(K=ue,e.font=ue),le(),M(S,l,o,s,c,w,b,te,((i,l,c,f,w)=>{var b,S;if(l<0)return;if(q[0]=t.sourceIndex,q[1]=l,void 0!==x&&!x.has(q))return;if(y.length>0){let e=!1;for(let n=0;n<y.length;n++){const o=y[n];if(O(r,i,t.width,c,o.x,o.y,o.width,o.height)){e=!0;break}}if(!e)return}const M=k.rows.hasIndex(l),R=v.hasIndex(l),X=l<s?h(q):z;let te=r,ne=t.width,oe=!1,ie=!1;if(void 0!==X.span){const[o,s]=X.span,u="".concat(l,",").concat(o,",").concat(s,",").concat(t.sticky);if(void 0===ee&&(ee=new Set),ee.has(u))return void G--;{const o=I(X.span,r,i,t.width,c,t,n),l=t.sticky?o[0]:o[1];if(t.sticky||void 0===o[0]||(ie=!0),void 0!==l){te=l.x,ne=l.width,ee.add(u),e.restore(),de=void 0,e.save(),e.beginPath();const t=Math.max(0,a-l.x);e.rect(l.x+t,i,l.width-t,c),void 0===J&&(J=[]),J.push({x:l.x+t,y:i,width:l.width-t,height:c}),e.clip(),oe=!0}}}const re=null===p||void 0===p?void 0:p(l),se=w&&void 0!==(null===(b=t.trailingRowOptions)||void 0===b?void 0:b.themeOverride)?null===(S=t.trailingRowOptions)||void 0===S?void 0:S.themeOverride:void 0,he=void 0===X.themeOverride&&void 0===re&&void 0===se?ce:(0,T.yR)(ce,re,se,X.themeOverride);e.beginPath();const fe=(0,d.Sb)(q,X,k);let pe=(0,d.H1)(q,X,k,m);const ve=void 0!==X.span&&k.columns.some((e=>void 0!==X.span&&e>=X.span[0]&&e<=X.span[1]));fe&&!g&&m?pe=0:fe&&m&&(pe=Math.max(pe,1)),ve&&pe++,fe||(M&&pe++,ae&&!w&&pe++);const ge=X.kind===u.p6.Protected?he.bgCellMedium:he.bgCell;let me;if((f||ge!==_.bgCell)&&(me=(0,C.NH)(ge,me)),pe>0||R){R&&(me=(0,C.NH)(he.bgHeader,me));for(let e=0;e<pe;e++)me=(0,C.NH)(he.accentLight,me)}else if(void 0!==E)for(const e of E)if(e[0]===t.sourceIndex&&e[1]===l){me=(0,C.NH)(he.bgSearchResult,me);break}if(void 0!==P)for(let e=0;e<P.length;e++){const n=P[e],o=n.range;"solid-outline"!==n.style&&o.x<=t.sourceIndex&&t.sourceIndex<o.x+o.width&&o.y<=l&&l<o.y+o.height&&(me=(0,C.NH)(n.color,me))}let we=!1;if(void 0!==x){const t=i+1,n=(f?t+c-1:Math.min(t+c-1,o-Q))-t;(n!==c-1||te+1<=a)&&(we=!0,e.save(),e.beginPath(),e.rect(te+1,t,ne-1,n),e.clip()),me=void 0===me?he.bgCell:(0,C.NH)(me,he.bgCell)}const be=t.sourceIndex===n.length-1,ye=l===s-1;let xe;void 0!==me&&(e.fillStyle=me,void 0!==de&&(de.fillStyle=me),void 0!==x?e.fillRect(te+1,i+1,ne-(be?2:1),c-(ye?2:1)):e.fillRect(te,i,ne,c)),"faded"===X.style&&(e.globalAlpha=.6);for(let e=0;e<L.length;e++){const n=L[e];if(n.item[0]===t.sourceIndex&&n.item[1]===l){xe=n;break}}if(ne>U&&!ie){var ke,Ce,Se;const n=he.baseFontFull;n!==K&&(e.font=n,K=n),de=B(e,X,t.sourceIndex,l,be,ye,te,i,ne,c,pe>0,he,null!==(ke=me)&&void 0!==ke?ke:he.bgCell,D,H,null!==(Ce=null===(Se=xe)||void 0===Se?void 0:Se.hoverAmount)&&void 0!==Ce?Ce:0,F,V,Y,A,de,N,Z,W,j)}var Me,Re;(we&&e.restore(),"faded"===X.style&&(e.globalAlpha=1),G--,oe)&&(e.restore(),null===(Me=de)||void 0===Me||null===(Re=Me.deprep)||void 0===Re||Re.call(Me,$),de=void 0,le(),K=ue,e.font=ue);return G<=0})),e.restore(),G<=0})),J}const F=[0,0],A={x:0,y:0,width:0,height:0},V=[void 0,()=>{}];let _=!1;function N(){_=!0}function B(e,t,n,o,i,r,l,a,s,c,h,f,p,v,g,m,w,b,y,x,k,C,S,M,R){let E,I,T;void 0!==w&&w[0][0]===n&&w[0][1]===o&&(E=w[1][0],I=w[1][1]),F[0]=n,F[1]=o,A.x=l,A.y=a,A.width=s,A.height=c,V[0]=S.getValue(F),V[1]=e=>S.setValue(F,e),_=!1;const O={ctx:e,theme:f,col:n,row:o,cell:t,rect:A,highlighted:h,cellFillColor:p,hoverAmount:m,frameTime:y,hoverX:E,drawState:V,hoverY:I,imageLoader:v,spriteManager:g,hyperWrapping:b,overrideCursor:void 0!==E?R:void 0,requestAnimationFrame:N},P=(0,d.vr)(O,t.lastUpdated,y,k,i,r),D=M(t);if(void 0!==D){var H,z,L,B;if((null===(H=k)||void 0===H?void 0:H.renderer)!==D)null===(L=k)||void 0===L||null===(B=L.deprep)||void 0===B||B.call(L,O),k=void 0;const e=null===(z=D.drawPrep)||void 0===z?void 0:z.call(D,O,k);void 0===x||(0,u.rs)(O.cell)?D.draw(O,t):x(O,(()=>D.draw(O,t))),T=void 0===e?void 0:{deprep:null===e||void 0===e?void 0:e.deprep,fillStyle:null===e||void 0===e?void 0:e.fillStyle,font:null===e||void 0===e?void 0:e.font,renderer:D}}return(P||_)&&(null===C||void 0===C||C(F)),T}function Z(e,t,n,o,r,l,a,s){var c;let h=arguments.length>8&&void 0!==arguments[8]?arguments[8]:-20,f=arguments.length>9&&void 0!==arguments[9]?arguments[9]:-20,p=arguments.length>10&&void 0!==arguments[10]?arguments[10]:32,v=arguments.length>11&&void 0!==arguments[11]?arguments[11]:"center",g=arguments.length>12&&void 0!==arguments[12]?arguments[12]:"square";const m=Math.floor(r+a/2),w="circle"===g?1e4:null!==(c=t.roundingRadius)&&void 0!==c?c:4;let b=(0,k.Qo)(p,a,t.cellVerticalPadding),y=b/2;const x=(0,k.XC)(v,o,l,t.cellHorizontalPadding,b),C=(0,k.kq)(x,m,b),S=(0,k.qq)(o+h,r+f,C);switch(n){case!0:e.beginPath(),(0,d.NK)(e,x-b/2,m-b/2,b,b,w),"circle"===g&&(y*=.8,b*=.8),e.fillStyle=s?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(x-y+b/4.23,m-y+b/1.97),e.lineTo(x-y+b/2.42,m-y+b/1.44),e.lineTo(x-y+b/1.29,m-y+b/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break;case u.qF:case!1:e.beginPath(),(0,d.NK)(e,x-b/2+.5,m-b/2+.5,b-1,b-1,w),e.lineWidth=1,e.strokeStyle=S?t.textDark:t.textMedium,e.stroke();break;case u.sd:e.beginPath(),(0,d.NK)(e,x-b/2,m-b/2,b,b,w),e.fillStyle=S?t.textMedium:t.textLight,e.fill(),"circle"===g&&(y*=.8,b*=.8),e.beginPath(),e.moveTo(x-b/3,m),e.lineTo(x+b/3,m),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break;default:(0,i.vE)(n)}}function W(e,t,n,o,i,r,l,a,s,c,u,h,f,p,v,g,m,w,b){var y,x,k,S;const M=l+a;if(M<=0)return;e.fillStyle=h.bgHeader,e.fillRect(0,0,i,M);const I=null===o||void 0===o||null===(y=o[0])||void 0===y?void 0:y[0],O=null===o||void 0===o||null===(x=o[0])||void 0===x?void 0:x[1],D=null===o||void 0===o||null===(k=o[1])||void 0===k?void 0:k[0],H=null===o||void 0===o||null===(S=o[1])||void 0===S?void 0:S[1],z=h.headerFontFull;e.font=z,R(t,0,r,0,M,((t,o,i,r)=>{var d,v,y;if(void 0!==m&&!m.has([t.sourceIndex,-1]))return;const x=Math.max(0,r-o);e.save(),e.beginPath(),e.rect(o+x,a,t.width-x,l),e.clip();const k=g(null!==(d=t.group)&&void 0!==d?d:"").overrideTheme,C=void 0===t.themeOverride&&void 0===k?h:(0,T.yR)(h,k,t.themeOverride);C.bgHeader!==h.bgHeader&&(e.fillStyle=C.bgHeader,e.fill()),C!==h&&(e.font=C.baseFontFull);const S=u.columns.hasIndex(t.sourceIndex),M=void 0!==s||c,R=!M&&-1===O&&I===t.sourceIndex,E=M?0:null!==(v=null===(y=p.find((e=>e.item[0]===t.sourceIndex&&-1===e.item[1])))||void 0===y?void 0:y.hoverAmount)&&void 0!==v?v:0,P=void 0!==(null===u||void 0===u?void 0:u.current)&&u.current.cell[0]===t.sourceIndex,z=S?C.accentColor:P?C.bgHeaderHasFocus:C.bgHeader,L=n?a:0,F=0===t.sourceIndex?0:1;S?(e.fillStyle=z,e.fillRect(o+F,L,t.width-F,l)):(P||E>0)&&(e.beginPath(),e.rect(o+F,L,t.width-F,l),P&&(e.fillStyle=C.bgHeaderHasFocus,e.fill()),E>0&&(e.globalAlpha=E,e.fillStyle=C.bgHeaderHovered,e.fill(),e.globalAlpha=1)),K(e,o,L,t.width,l,t,S,C,R,R?D:void 0,R?H:void 0,P,E,f,w,b),e.restore()})),n&&function(e,t,n,o,i,r,l,a,s,c,u,h){var f;const p=8,[v,g]=null!==(f=null===r||void 0===r?void 0:r[0])&&void 0!==f?f:[];let m=0;E(t,n,o,i,((t,n,o,s,f,w)=>{var b;if(void 0!==h&&!h.hasItemInRectangle({x:t[0],y:-2,width:t[1]-t[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(o,s,f,w),e.clip();const y=u(n),x=void 0===(null===y||void 0===y?void 0:y.overrideTheme)?l:(0,T.yR)(l,y.overrideTheme),k=-2===g&&void 0!==v&&v>=t[0]&&v<=t[1],S=k?x.bgHeaderHovered:x.bgHeader;if(S!==l.bgHeader&&(e.fillStyle=S,e.fill()),e.fillStyle=null!==(b=x.textGroupHeader)&&void 0!==b?b:x.textHeader,void 0!==y){let t=o;if(void 0!==y.icon&&(a.drawSprite(y.icon,"normal",e,t+p,(i-20)/2,20,x),t+=26),e.fillText(y.name,t+p,i/2+(0,d.aX)(e,l.headerFontFull)),void 0!==y.actions&&k){var M;const t=U({x:o,y:s,width:f,height:w},y.actions);e.beginPath();const n=t[0].x-10,l=o+f-n;e.rect(n,0,l,i);const c=e.createLinearGradient(n,0,n+l,0),u=(0,C.fG)(S,0);c.addColorStop(0,u),c.addColorStop(10/l,S),c.addColorStop(1,S),e.fillStyle=c,e.fill(),e.globalAlpha=.6;const[d,h]=null!==(M=null===r||void 0===r?void 0:r[1])&&void 0!==M?M:[-1,-1];for(let i=0;i<y.actions.length;i++){const n=y.actions[i],r=t[i],l=P(r,d+o,h);l&&(e.globalAlpha=1),a.drawSprite(n.icon,"normal",e,r.x+r.width/2-10,r.y+r.height/2-10,20,x),l&&(e.globalAlpha=.6)}e.globalAlpha=1}}0!==o&&c(t[0])&&(e.beginPath(),e.moveTo(o+.5,0),e.lineTo(o+.5,i),e.strokeStyle=l.borderColor,e.lineWidth=1,e.stroke()),e.restore(),m=o+f})),e.beginPath(),e.moveTo(m+.5,0),e.lineTo(m+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=l.borderColor,e.lineWidth=1,e.stroke()}(e,t,i,r,a,o,h,f,0,v,g,m)}const j=30;function U(e,t){const n=[];let o=e.x+e.width-26*t.length;const i=e.y+e.height/2-13;for(let r=0;r<t.length;r++)n.push({x:o,y:i,width:26,height:26}),o+=26;return n}function X(e,t,n){return n&&void 0!==e?(e.x=t-(e.x-t)-e.width,e):e}function G(e,t,n,o,i,r,l,a){const s=l.cellHorizontalPadding,c=l.headerIconSize,u=function(e,t,n,o,i){return i?{x:e,y:t,width:j,height:Math.min(j,o)}:{x:e+n-j,y:Math.max(t,t+o/2-j/2),width:j,height:Math.min(j,o)}}(n,o,i,r,!1);let h=n+s;const f=void 0===t.icon?void 0:{x:h,y:o+(r-c)/2,width:c,height:c},p=void 0===f||void 0===t.overlayIcon?void 0:{x:f.x+9,y:f.y+6,width:18,height:18};void 0!==f&&(h+=Math.ceil(1.3*c));const v={x:h,y:o,width:i-h,height:r};let g;if(void 0!==t.indicatorIcon){var m,w;const n=void 0===e?null!==(m=null===(w=(0,d._y)(t.title,l.headerFontFull))||void 0===w?void 0:w.width)&&void 0!==m?m:0:(0,d.P7)(t.title,e,l.headerFontFull).width;v.width=n,h+=n+s,g={x:h,y:o+(r-c)/2,width:c,height:c}}const b=n+i/2;return{menuBounds:X(u,b,a),iconBounds:X(f,b,a),iconOverlayBounds:X(p,b,a),textBounds:X(v,b,a),indicatorIconBounds:X(g,b,a)}}function Y(e,t,n,o,i,r,l,a,s,c,h,f,p,v,g,m){if(void 0!==r.rowMarker&&!0!==r.headerRowMarkerDisabled){const l=r.rowMarkerChecked;!0!==l&&!0!==r.headerRowMarkerAlwaysVisible&&(e.globalAlpha=f);return Z(e,void 0!==r.headerRowMarkerTheme?(0,T.yR)(a,r.headerRowMarkerTheme):a,l,t,n,o,i,!1,void 0,void 0,18,"center",r.rowMarker),void(!0!==l&&!0!==r.headerRowMarkerAlwaysVisible&&(e.globalAlpha=1))}const w=l?a.textHeaderSelected:a.textHeader,b=!0===r.hasMenu&&(s||v&&l)&&void 0!==m.menuBounds;if(void 0!==r.icon&&void 0!==m.iconBounds){let t=l?"selected":"normal";"highlight"===r.style&&(t=l?"selected":"special"),p.drawSprite(r.icon,t,e,m.iconBounds.x,m.iconBounds.y,m.iconBounds.width,a),void 0!==r.overlayIcon&&void 0!==m.iconOverlayBounds&&p.drawSprite(r.overlayIcon,l?"selected":"special",e,m.iconOverlayBounds.x,m.iconOverlayBounds.y,m.iconOverlayBounds.width,a)}if(b&&o>35){const n=35,i=(g?n:o-n)/o,r=(g?.7*n:o-.7*n)/o,l=e.createLinearGradient(t,0,t+o,0),a=(0,C.fG)(w,0);l.addColorStop(g?1:0,w),l.addColorStop(i,w),l.addColorStop(r,a),l.addColorStop(g?0:1,a),e.fillStyle=l}else e.fillStyle=w;if(g&&(e.textAlign="right"),void 0!==m.textBounds&&e.fillText(r.title,g?m.textBounds.x+m.textBounds.width:m.textBounds.x,n+i/2+(0,d.aX)(e,a.headerFontFull)),g&&(e.textAlign="left"),void 0!==r.indicatorIcon&&void 0!==m.indicatorIconBounds&&(!b||!O(m.menuBounds.x,m.menuBounds.y,m.menuBounds.width,m.menuBounds.height,m.indicatorIconBounds.x,m.indicatorIconBounds.y,m.indicatorIconBounds.width,m.indicatorIconBounds.height))){let t=l?"selected":"normal";"highlight"===r.style&&(t=l?"selected":"special"),p.drawSprite(r.indicatorIcon,t,e,m.indicatorIconBounds.x,m.indicatorIconBounds.y,m.indicatorIconBounds.width,a)}if(b&&void 0!==m.menuBounds){const o=m.menuBounds,i=void 0!==c&&void 0!==h&&P(o,c+t,h+n);if(i||(e.globalAlpha=.7),void 0===r.menuIcon||r.menuIcon===u.pN.Triangle){e.beginPath();const t=o.x+o.width/2-5.5,n=o.y+o.height/2-3;(0,d.zu)(e,[{x:t,y:n},{x:t+11,y:n},{x:t+5.5,y:n+6}],1),e.fillStyle=w,e.fill()}else if(r.menuIcon===u.pN.Dots){e.beginPath();const t=o.x+o.width/2,n=o.y+o.height/2;(0,d.Ld)(e,t,n),e.fillStyle=w,e.fill()}else{const t=o.x+(o.width-a.headerIconSize)/2,n=o.y+(o.height-a.headerIconSize)/2;p.drawSprite(r.menuIcon,"normal",e,t,n,a.headerIconSize,a)}i||(e.globalAlpha=1)}}function K(e,t,n,o,i,r,l,a,s,c,u,d,h,f,p,v){const g="rtl"===(0,k.o7)(r.title),m=G(e,r,t,n,o,i,a,g);var w;void 0!==p?p({ctx:e,theme:a,rect:{x:t,y:n,width:o,height:i},column:r,columnIndex:r.sourceIndex,isSelected:l,hoverAmount:h,isHovered:s,hasSelectedCell:d,spriteManager:f,menuBounds:null!==(w=null===m||void 0===m?void 0:m.menuBounds)&&void 0!==w?w:{x:0,y:0,height:0,width:0}},(()=>Y(e,t,n,o,i,r,l,a,s,c,u,h,f,v,g,m))):Y(e,t,n,o,i,r,l,a,s,c,u,h,f,v,g,m)}var $=n(76236);const q=(e,t,n)=>{let o=0,i=t,r=0,l=n;if(void 0!==e&&e.length>0){o=Number.MAX_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,l=Number.MIN_SAFE_INTEGER;for(const t of e)o=Math.min(o,t.x-1),i=Math.max(i,t.x+t.width+1),r=Math.min(r,t.y-1),l=Math.max(l,t.y+t.height+1)}return{minX:o,maxX:i,minY:r,maxY:l}};function Q(e,t,n,o,i,r,l,a,s,c,u,d,h,f,p,v,g){var m;let w=arguments.length>17&&void 0!==arguments[17]&&arguments[17];if(void 0!==s){e.beginPath(),e.save(),e.rect(0,0,r,l);for(const t of s)e.rect(t.x+1,t.y+1,t.width-1,t.height-1);e.clip("evenodd")}const b=null!==(m=g.horizontalBorderColor)&&void 0!==m?m:g.borderColor,y=g.borderColor,{minX:x,maxX:k,minY:C,maxY:S}=q(a,r,l),M=[];e.beginPath();let R=.5;for(let P=0;P<t.length;P++){const e=t[P];if(0===e.width)continue;R+=e.width;const n=e.sticky?R:R+o;n>=x&&n<=k&&f(P+1)&&M.push({x1:n,y1:Math.max(c,C),x2:n,y2:Math.min(l,S),color:y})}let E=l+.5;for(let P=v-p;P<v;P++){E-=d(P),M.push({x1:x,y1:E,x2:k,y2:E,color:b})}if(!0!==w){let e=u+.5,t=n;const o=E;for(;e+i<o;){const n=e+i;if(n>=C&&n<=S-1){var I,T;const e=null===h||void 0===h?void 0:h(t);M.push({x1:x,y1:n,x2:k,y2:n,color:null!==(I=null!==(T=null===e||void 0===e?void 0:e.horizontalBorderColor)&&void 0!==T?T:null===e||void 0===e?void 0:e.borderColor)&&void 0!==I?I:b})}e+=d(t),t++}}const O=$(M,(e=>e.color));for(const P of Object.keys(O)){e.strokeStyle=P;for(const t of O[P])e.moveTo(t.x1,t.y1),e.lineTo(t.x2,t.y2);e.stroke(),e.beginPath()}void 0!==s&&e.restore()}function J(e,t,n,o,i,r,l,a,s,c,u,h,f,p,v,g){const m=null===v||void 0===v?void 0:v.filter((e=>"no-outline"!==e.style));if(void 0===m||0===m.length)return;const w=(0,d.G6)(a),b=(0,d.YN)(p,f,h),y=[s,0,a.length,p-f],x=[w,0,t,n-b],k=m.map((e=>{var v;const g=e.range,m=null!==(v=e.style)&&void 0!==v?v:"dashed";return function(e,t,n,o,i){const[r,l,a,s]=t,[c,u,d,h]=i,{x:f,y:p,width:v,height:g}=e,m=[];if(v<=0||g<=0)return m;const w=f+v,b=p+g,y=f<r,x=p<l,k=f+v>a,C=p+g>s,S=f>=r&&f<a||w>r&&w<=a||f<r&&w>a,M=p>=l&&p<s||b>l&&b<=s||p<l&&b>s;if(S&&M){const e=Math.max(f,r),t=Math.max(p,l),n=Math.min(w,a),o=Math.min(b,s);m.push({rect:{x:e,y:t,width:n-e,height:o-t},clip:{x:c,y:u,width:d-c+1,height:h-u+1}})}if(y&&x){const e=f,t=p,n=Math.min(w,r),o=Math.min(b,l);m.push({rect:{x:e,y:t,width:n-e,height:o-t},clip:{x:0,y:0,width:c+1,height:u+1}})}if(x&&S){const e=Math.max(f,r),t=p,n=Math.min(w,a),o=Math.min(b,l);m.push({rect:{x:e,y:t,width:n-e,height:o-t},clip:{x:c,y:0,width:d-c+1,height:u+1}})}if(x&&k){const e=Math.max(f,a),t=p,o=w,i=Math.min(b,l);m.push({rect:{x:e,y:t,width:o-e,height:i-t},clip:{x:d,y:0,width:n-d+1,height:u+1}})}if(y&&M){const e=f,t=Math.max(p,l),n=Math.min(w,r),o=Math.min(b,s);m.push({rect:{x:e,y:t,width:n-e,height:o-t},clip:{x:0,y:u,width:c+1,height:h-u+1}})}if(k&&M){const e=Math.max(f,a),t=Math.max(p,l),o=w,i=Math.min(b,s);m.push({rect:{x:e,y:t,width:o-e,height:i-t},clip:{x:d,y:u,width:n-d+1,height:h-u+1}})}if(y&&C){const e=f,t=Math.max(p,s),n=Math.min(w,r),i=b;m.push({rect:{x:e,y:t,width:n-e,height:i-t},clip:{x:0,y:h,width:c+1,height:o-h+1}})}if(C&&S){const e=Math.max(f,r),t=Math.max(p,s),n=Math.min(w,a),i=b;m.push({rect:{x:e,y:t,width:n-e,height:i-t},clip:{x:c,y:h,width:d-c+1,height:o-h+1}})}if(k&&C){const e=Math.max(f,a),t=Math.max(p,s),i=w,r=b;m.push({rect:{x:e,y:t,width:i-e,height:r-t},clip:{x:d,y:h,width:n-d+1,height:o-h+1}})}return m}(g,y,t,n,x).map((v=>{const g=v.rect,w=(0,d.Ve)(g.x,g.y,t,n,u,c+u,o,i,r,l,p,s,f,a,h),b=1===g.width&&1===g.height?w:(0,d.Ve)(g.x+g.width-1,g.y+g.height-1,t,n,u,c+u,o,i,r,l,p,s,f,a,h);return g.x+g.width>=a.length&&(b.width-=1),g.y+g.height>=p&&(b.height-=1),{color:e.color,style:m,clip:v.clip,rect:H({x:w.x,y:w.y,width:b.x+b.width-w.x,height:b.y+b.height-w.y},t,n,8)}}))})),S=()=>{e.lineWidth=1;let o=!1;for(const l of k)for(const a of l)if(void 0!==(null===a||void 0===a?void 0:a.rect)&&O(0,0,t,n,a.rect.x,a.rect.y,a.rect.width,a.rect.height)){const t=o,n=(i=a.clip,r=a.rect,!(i.x<=r.x&&i.y<=r.y&&i.x+i.width>=r.x+r.width&&i.y+i.height>=r.y+r.height));n&&(e.save(),e.rect(a.clip.x,a.clip.y,a.clip.width,a.clip.height),e.clip()),"dashed"!==a.style||o?"solid"!==a.style&&"solid-outline"!==a.style||!o||(e.setLineDash([]),o=!1):(e.setLineDash([5,3]),o=!0),e.strokeStyle="solid-outline"===a.style?(0,C.NH)((0,C.NH)(a.color,g.borderColor),g.bgCell):(0,C.fG)(a.color,1),e.strokeRect(a.rect.x+.5,a.rect.y+.5,a.rect.width-1,a.rect.height-1),n&&(e.restore(),o=t)}var i,r;o&&e.setLineDash([])};return S(),S}function ee(e,t,n,o,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,o),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function te(e,t,n,o,i,r,l,a,s,c,u,h,f,p,v,g,m){var w;if(void 0===u.current)return;const b=u.current.range,y=u.current.cell,x=[b.x+b.width-1,b.y+b.height-1];if(y[1]>=m&&x[1]>=m)return;if(!l.some((e=>e.sourceIndex===y[0]||e.sourceIndex===x[0])))return;const[k,C]=u.current.cell,S=f(u.current.cell),E=null!==(w=S.span)&&void 0!==w?w:[k,k],T=p>0&&!(C>=m-p)?(0,d.YN)(m,p,h)-1:0,O=x[1];let P;if(R(l,o,i,r,c,((o,i,r,l,c)=>{if(o.sticky&&k>o.sourceIndex)return;const u=o.sourceIndex<E[0],d=o.sourceIndex>E[1],f=o.sourceIndex===x[0];return f||!u&&!d?(M(c,r,n,m,h,p,v,void 0,((r,c,u)=>{if(c!==C&&c!==O)return;let d=i,h=o.width;if(void 0!==S.span){const e=I(S.span,i,r,o.width,u,o,a),t=o.sticky?e[0]:e[1];void 0!==t&&(d=t.x,h=t.width)}return c===O&&f&&g&&(P=()=>{var i,a;l>d&&!o.sticky&&(e.beginPath(),e.rect(l,0,t-l,n),e.clip()),e.beginPath(),e.rect(d+h-4,r+u-4,4,4),e.fillStyle=null!==(i=null===(a=o.themeOverride)||void 0===a?void 0:a.accentColor)&&void 0!==i?i:s.accentColor,e.fill()}),void 0!==P})),void 0!==P):void 0})),void 0===P)return;const D=()=>{var o;e.save(),e.beginPath(),e.rect(0,c,t,n-c-T),e.clip(),null===(o=P)||void 0===o||o(),e.restore()};return D(),D}function ne(e,t){var n,o;const{canvasCtx:r,headerCanvasCtx:l,width:a,height:s,cellXOffset:c,cellYOffset:u,translateX:h,translateY:f,mappedColumns:p,enableGroups:v,freezeColumns:g,dragAndDropState:m,theme:w,drawFocus:b,headerHeight:y,groupHeaderHeight:x,disabledRows:k,rowHeight:I,verticalBorder:P,overrideCursor:D,isResizing:H,selection:z,fillHandle:F,freezeTrailingRows:A,rows:V,getCellContent:_,getGroupDetails:N,getRowThemeOverride:B,isFocused:Z,drawHeaderCallback:j,prelightCells:U,drawCellCallback:X,highlightRegions:G,resizeCol:Y,imageLoader:K,lastBlitData:$,hoverValues:ne,hyperWrapping:oe,hoverInfo:ie,spriteManager:re,maxScaleFactor:le,hasAppendRow:ae,touchMode:se,enqueue:ce,renderStateProvider:ue,getCellRenderer:de,renderStrategy:he,bufferACtx:fe,bufferBCtx:pe,damage:ve,minimumCellWidth:ge,resizeIndicator:me}=e;if(0===a||0===s)return;const we="double-buffer"===he,be=Math.min(le,Math.ceil(null!==(n=window.devicePixelRatio)&&void 0!==n?n:1)),ye="direct"!==he&&function(e,t){if(void 0===t)return!1;if(e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let o=0;o<e.mappedColumns.length;o++){const r=e.mappedColumns[o],l=t.mappedColumns[o];if((0,i.vZ)(r,l))continue;if(void 0!==n)return!1;if(r.width===l.width)return!1;const{width:a,...s}=r,{width:c,...u}=l;if(!(0,i.vZ)(s,u))return!1;n=o}return void 0===n||n}return!0}(e,t),xe=r.canvas;xe.width===a*be&&xe.height===s*be||(xe.width=a*be,xe.height=s*be,xe.style.width=a+"px",xe.style.height=s+"px");const ke=l.canvas,Ce=v?x+y:y,Se=Ce+1;ke.width===a*be&&ke.height===Se*be||(ke.width=a*be,ke.height=Se*be,ke.style.width=a+"px",ke.style.height=Se+"px");const Me=fe.canvas,Re=pe.canvas;!we||Me.width===a*be&&Me.height===s*be||(Me.width=a*be,Me.height=s*be,void 0!==$.current&&($.current.aBufferScroll=void 0)),!we||Re.width===a*be&&Re.height===s*be||(Re.width=a*be,Re.height=s*be,void 0!==$.current&&($.current.bBufferScroll=void 0));const Ee=$.current;if(!0===ye&&c===(null===Ee||void 0===Ee?void 0:Ee.cellXOffset)&&u===(null===Ee||void 0===Ee?void 0:Ee.cellYOffset)&&h===(null===Ee||void 0===Ee?void 0:Ee.translateX)&&f===(null===Ee||void 0===Ee?void 0:Ee.translateY))return;let Ie=null;we&&(Ie=r);const Te=l;let Oe;Oe=we?void 0!==ve?"b"===(null===Ee||void 0===Ee?void 0:Ee.lastBuffer)?pe:fe:"b"===(null===Ee||void 0===Ee?void 0:Ee.lastBuffer)?fe:pe:r;const Pe=Oe.canvas,De=we?Pe===Me?Re:Me:xe,He="number"===typeof I?()=>I:I;Te.save(),Oe.save(),Te.beginPath(),Oe.beginPath(),Te.textBaseline="middle",Oe.textBaseline="middle",1!==be&&(Te.scale(be,be),Oe.scale(be,be));const ze=(0,d.ih)(p,c,a,m,h);let Le=[];const Fe=b&&(null===(o=z.current)||void 0===o?void 0:o.cell[1])===u&&0===f;let Ae=!1;if(void 0!==G)for(const i of G)if("no-outline"!==i.style&&i.range.y===u&&0===f){Ae=!0;break}const Ve=()=>{var e,t;W(Te,ze,v,ie,a,h,y,x,m,H,z,w,re,ne,P,N,ve,j,se),Q(Te,ze,u,h,f,a,s,void 0,void 0,x,Ce,He,B,P,A,V,w,!0),Te.beginPath(),Te.moveTo(0,Se-.5),Te.lineTo(a,Se-.5),Te.strokeStyle=(0,C.NH)(null!==(e=null!==(t=w.headerBottomBorderColor)&&void 0!==t?t:w.horizontalBorderColor)&&void 0!==e?e:w.borderColor,w.bgHeader),Te.stroke(),Ae&&J(Te,a,s,c,u,h,f,p,g,y,x,I,A,V,G,w),Fe&&te(Te,a,s,u,h,f,ze,p,w,Ce,z,He,_,A,ae,F,V)};if(void 0!==ve){const e=ze[ze.length-1].sourceIndex+1,t=e=>{L(e,ze,p,s,Ce,h,f,u,V,He,_,N,B,k,Z,b,A,ae,Le,ve,z,U,G,K,re,ne,ie,X,oe,w,ce,ue,de,D,ge);const t=z.current;F&&b&&void 0!==t&&ve.has((0,d.zU)(t.range))&&te(e,a,s,u,h,f,ze,p,w,Ce,z,He,_,A,ae,F,V)};if(ve.hasItemInRegion([{x:c,y:-2,width:e,height:2},{x:c,y:u,width:e,height:300},{x:0,y:u,width:g,height:300},{x:0,y:-2,width:g,height:2},{x:c,y:V-A,width:e,height:A,when:A>0}])){t(Oe),null!==Ie&&(Ie.save(),Ie.scale(be,be),Ie.textBaseline="middle",t(Ie),Ie.restore());ve.hasHeader()&&(!function(e,t,n,o,i,r,l,a,s){void 0!==s&&0!==s.size&&(e.beginPath(),E(t,n,r,o,((t,n,o,i,r,l)=>{s.hasItemInRectangle({x:t[0],y:-2,width:t[1]-t[0]+1,height:1})&&e.rect(o,i,r,l)})),R(t,a,r,l,i,((t,n,r,l)=>{const a=Math.max(0,l-n),c=n+a+1,u=t.width-a-1;s.has([t.sourceIndex,-1])&&e.rect(c,o,u,i-o)})),e.clip())}(Te,ze,a,x,Ce,h,f,u,ve),Ve())}return Oe.restore(),void Te.restore()}if(!0===ye&&c===(null===Ee||void 0===Ee?void 0:Ee.cellXOffset)&&h===(null===Ee||void 0===Ee?void 0:Ee.translateX)&&Fe===(null===Ee||void 0===Ee?void 0:Ee.mustDrawFocusOnHeader)&&Ae===(null===Ee||void 0===Ee?void 0:Ee.mustDrawHighlightRingsOnHeader)||Ve(),!0===ye){(0,i.hu)(void 0!==De&&void 0!==Ee);const{regions:e}=function(e,t,n,o,i,r,l,a,s,c,u,h,f,p,v,g,m,w,b){const y=[];e.imageSmoothingEnabled=!1;const x=Math.min(i.cellYOffset,l),k=Math.max(i.cellYOffset,l);let C=0;if("number"===typeof w)C+=(k-x)*w;else for(let d=x;d<k;d++)C+=w(d);l>i.cellYOffset&&(C=-C),C+=s-i.translateY;const S=Math.min(i.cellXOffset,r),M=Math.max(i.cellXOffset,r);let R=0;for(let d=S;d<M;d++)R+=g[d].width;r>i.cellXOffset&&(R=-R),R+=a-i.translateX;const E=(0,d.G6)(m);if(0!==R&&0!==C)return{regions:[]};const I=c>0?(0,d.YN)(f,c,w):0,T=u-E-Math.abs(R),O=h-p-I-Math.abs(C)-1;if(T>150&&O>150){const i={sx:0,sy:0,sw:u*v,sh:h*v,dx:0,dy:0,dw:u*v,dh:h*v};if(C>0?(i.sy=(p+1)*v,i.sh=O*v,i.dy=(C+p+1)*v,i.dh=O*v,y.push({x:0,y:p,width:u,height:C+1})):C<0&&(i.sy=(-C+p+1)*v,i.sh=O*v,i.dy=(p+1)*v,i.dh=O*v,y.push({x:0,y:h+C-I,width:u,height:-C+I})),R>0?(i.sx=E*v,i.sw=T*v,i.dx=(R+E)*v,i.dw=T*v,y.push({x:E-1,y:0,width:R+2,height:h})):R<0&&(i.sx=(E-R)*v,i.sw=T*v,i.dx=E*v,i.dw=T*v,y.push({x:u+R,y:0,width:-R,height:h})),e.setTransform(1,0,0,1,0,0),b){if(E>0&&0!==R&&0===C&&(void 0===o||!1!==(null===n||void 0===n?void 0:n[1]))){const n=E*v,o=h*v;e.drawImage(t,0,0,n,o,0,0,n,o)}if(I>0&&0===R&&0!==C&&(void 0===o||!1!==(null===n||void 0===n?void 0:n[0]))){const n=(h-I)*v,o=u*v,i=I*v;e.drawImage(t,0,n,o,i,0,n,o,i)}}e.drawImage(t,i.sx,i.sy,i.sw,i.sh,i.dx,i.dy,i.dw,i.dh),e.scale(v,v)}return e.imageSmoothingEnabled=!0,{regions:y}}(Oe,De,De===Me?Ee.aBufferScroll:Ee.bBufferScroll,De===Me?Ee.bBufferScroll:Ee.aBufferScroll,Ee,c,u,h,f,A,a,s,V,Ce,be,p,ze,I,we);Le=e}else if(!1!==ye){(0,i.hu)(void 0!==Ee);Le=function(e,t,n,o,i,r,l,a,s,c){const u=[];return t!==e.cellXOffset||n!==e.cellYOffset||o!==e.translateX||i!==e.translateY||R(s,n,o,i,a,((e,t,n,o)=>{if(e.sourceIndex===c){const e=Math.max(t,o)+1;return u.push({x:e,y:0,width:r-e,height:l}),!0}})),u}(Ee,c,u,h,f,a,s,Ce,ze,ye)}!function(e,t,n,o,i,r,l,a,s){var c;let u=!1;for(const d of t)if(!d.sticky){u=l(d.sourceIndex);break}const h=null!==(c=s.horizontalBorderColor)&&void 0!==c?c:s.borderColor,f=s.borderColor,p=u?(0,d.G6)(t):0;let v;if(0!==p&&(v=(0,C.mv)(f,s.bgCell),e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,o),e.strokeStyle=v,e.stroke()),i>0){const t=f===h&&void 0!==v?v:(0,C.mv)(h,s.bgCell),l=(0,d.YN)(r,i,a);e.beginPath(),e.moveTo(0,o-l+.5),e.lineTo(n,o-l+.5),e.strokeStyle=t,e.stroke()}}(Oe,ze,a,s,A,V,P,He,w);const _e=J(Oe,a,s,c,u,h,f,p,g,y,x,I,A,V,G,w),Ne=b?te(Oe,a,s,u,h,f,ze,p,w,Ce,z,He,_,A,ae,F,V):void 0;if(Oe.fillStyle=w.bgCell,Le.length>0){Oe.beginPath();for(const e of Le)Oe.rect(e.x,e.y,e.width,e.height);Oe.clip(),Oe.fill(),Oe.beginPath()}else Oe.fillRect(0,0,a,s);const Be=L(Oe,ze,p,s,Ce,h,f,u,V,He,_,N,B,k,Z,b,A,ae,Le,ve,z,U,G,K,re,ne,ie,X,oe,w,ce,ue,de,D,ge);!function(e,t,n,o,i,r,l,a,s,c,u,d,h,f,p,v,g,m,w){if(void 0!==m||t[t.length-1]!==n[t.length-1])return;const b=S(g);R(t,s,l,a,r,((n,l,a,s,m)=>{if(n!==t[t.length-1])return;l+=n.width;const y=Math.max(l,s);y>o||(e.save(),e.beginPath(),e.rect(y,r+1,1e4,i-r-1),e.clip(),M(m,a,i,c,u,p,v,b,((t,n,o,i)=>{if(!i&&g.length>0&&!g.some((e=>O(l,t,1e4,o,e.x,e.y,e.width,e.height))))return;const r=h.hasIndex(n),a=f.hasIndex(n);e.beginPath();const s=null===d||void 0===d?void 0:d(n),c=void 0===s?w:(0,T.yR)(w,s);c.bgCell!==w.bgCell&&(e.fillStyle=c.bgCell,e.fillRect(l,t,1e4,o)),a&&(e.fillStyle=c.bgHeader,e.fillRect(l,t,1e4,o)),r&&(e.fillStyle=c.accentLight,e.fillRect(l,t,1e4,o))})),e.restore())}))}(Oe,ze,p,a,s,Ce,h,f,u,V,He,B,z.rows,k,A,ae,Le,ve,w),function(e,t,n,o,i,r,l,a,s,c,u,h,f,p,v){const g=v.bgCell,{minX:m,maxX:w,minY:b,maxY:y}=q(a,r,l),x=[],k=l-(0,d.YN)(p,f,c);let C=s,S=n,M=0;for(;C+i<k;){const e=C+i,t=c(S);if(e>=b&&e<=y-1){const n=null===u||void 0===u?void 0:u(S),o=null===n||void 0===n?void 0:n.bgCell;void 0!==o&&o!==g&&S>=p-f&&x.push({x:m,y:e,w:w-m,h:t,color:o})}C+=t,S<p-f&&(M=C),S++}let R=0;const E=Math.min(k,y)-M;if(E>0)for(let d=0;d<t.length;d++){var I;const e=t[d];if(0===e.width)continue;const n=e.sticky?R:R+o,i=null===(I=e.themeOverride)||void 0===I?void 0:I.bgCell;void 0!==i&&i!==g&&n>=m&&n<=w&&h(d+1)&&x.push({x:n,y:M,w:e.width,h:E,color:i}),R+=e.width}if(0===x.length)return;let T;e.beginPath();for(let d=x.length-1;d>=0;d--){const t=x[d];void 0===T?T=t.color:t.color!==T&&(e.fillStyle=T,e.fill(),e.beginPath(),T=t.color),e.rect(t.x,t.y,t.w,t.h)}void 0!==T&&(e.fillStyle=T,e.fill()),e.beginPath()}(Oe,ze,u,h,f,a,s,Le,Ce,He,B,P,A,V,w),Q(Oe,ze,u,h,f,a,s,Le,Be,x,Ce,He,B,P,A,V,w),null===_e||void 0===_e||_e(),null===Ne||void 0===Ne||Ne(),H&&"none"!==me&&R(ze,0,h,0,Ce,((e,t)=>{if(e.sourceIndex===Y){var n,o;if(ee(Te,t+e.width,0,Ce+1,(0,C.NH)(null!==(n=w.resizeIndicatorColor)&&void 0!==n?n:w.accentLight,w.bgHeader)),"full"===me)ee(Oe,t+e.width,Ce,s,(0,C.NH)(null!==(o=w.resizeIndicatorColor)&&void 0!==o?o:w.accentLight,w.bgCell));return!0}return!1})),null!==Ie&&(Ie.fillStyle=w.bgCell,Ie.fillRect(0,0,a,s),Ie.drawImage(Oe.canvas,0,0));const Ze=function(e,t,n,o,i,r,l,a,s,c){let u=0;return R(e,r,o,i,n,((e,n,o,i,r)=>(M(r,o,t,l,a,s,c,void 0,((e,t,n,o)=>{o||(u=Math.max(t,u))})),!0))),u}(ze,s,Ce,h,f,u,V,He,A,ae);null===K||void 0===K||K.setWindow({x:c,y:u,width:ze.length,height:Ze-u},g,Array.from({length:A},((e,t)=>V-1-t)));const We=void 0!==Ee&&(c!==Ee.cellXOffset||h!==Ee.translateX),je=void 0!==Ee&&(u!==Ee.cellYOffset||f!==Ee.translateY);$.current={cellXOffset:c,cellYOffset:u,translateX:h,translateY:f,mustDrawFocusOnHeader:Fe,mustDrawHighlightRingsOnHeader:Ae,lastBuffer:we?Pe===Me?"a":"b":void 0,aBufferScroll:Pe===Me?[We,je]:null===Ee||void 0===Ee?void 0:Ee.aBufferScroll,bBufferScroll:Pe===Re?[We,je]:null===Ee||void 0===Ee?void 0:Ee.bBufferScroll},Oe.restore(),Te.restore()}function oe(e){const t=e-1;return t*t*t+1}class ie{constructor(e){(0,h.Z)(this,"callback",void 0),(0,h.Z)(this,"currentHoveredItem",void 0),(0,h.Z)(this,"leavingItems",[]),(0,h.Z)(this,"lastAnimationTime",void 0),(0,h.Z)(this,"addToLeavingItems",(e=>{this.leavingItems.some((t=>(0,d.pU)(t.item,e.item)))||this.leavingItems.push(e)})),(0,h.Z)(this,"removeFromLeavingItems",(e=>{var t;const n=this.leavingItems.find((t=>(0,d.pU)(t.item,e)));return this.leavingItems=this.leavingItems.filter((e=>e!==n)),null!==(t=null===n||void 0===n?void 0:n.hoverAmount)&&void 0!==t?t:0})),(0,h.Z)(this,"cleanUpLeavingElements",(()=>{this.leavingItems=this.leavingItems.filter((e=>e.hoverAmount>0))})),(0,h.Z)(this,"shouldStep",(()=>{const e=this.leavingItems.length>0,t=void 0!==this.currentHoveredItem&&this.currentHoveredItem.hoverAmount<1;return e||t})),(0,h.Z)(this,"getAnimatingItems",(()=>void 0!==this.currentHoveredItem?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map((e=>({...e,hoverAmount:oe(e.hoverAmount)}))))),(0,h.Z)(this,"step",(e=>{if(void 0===this.lastAnimationTime)this.lastAnimationTime=e;else{const t=(e-this.lastAnimationTime)/80;for(const e of this.leavingItems)e.hoverAmount=r(e.hoverAmount-t,0,1);void 0!==this.currentHoveredItem&&(this.currentHoveredItem.hoverAmount=r(this.currentHoveredItem.hoverAmount+t,0,1));const n=this.getAnimatingItems();this.callback(n),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=e,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0})),(0,h.Z)(this,"setHovered",(e=>{var t;if(!(0,d.pU)(null===(t=this.currentHoveredItem)||void 0===t?void 0:t.item,e)){if(void 0!==this.currentHoveredItem&&this.addToLeavingItems(this.currentHoveredItem),void 0!==e){const t=this.removeFromLeavingItems(e);this.currentHoveredItem={item:e,hoverAmount:t}}else this.currentHoveredItem=void 0;void 0===this.lastAnimationTime&&window.requestAnimationFrame(this.step)}})),this.callback=e}}class re{constructor(e){(0,h.Z)(this,"fn",void 0),(0,h.Z)(this,"val",void 0),this.fn=e}get value(){var e;return null!==(e=this.val)&&void 0!==e?e:this.val=this.fn()}}function le(e){return new re(e)}const ae=le((()=>window.navigator.userAgent.includes("Firefox"))),se=le((()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome"))),ce=le((()=>window.navigator.platform.toLowerCase().startsWith("mac")));const ue="header",de="group-header",he="out-of-bounds";var fe;function pe(e,t){return e===t||("out-of-bounds"===(null===e||void 0===e?void 0:e.kind)?(null===e||void 0===e?void 0:e.kind)===(null===t||void 0===t?void 0:t.kind)&&(null===e||void 0===e?void 0:e.location[0])===(null===t||void 0===t?void 0:t.location[0])&&(null===e||void 0===e?void 0:e.location[1])===(null===t||void 0===t?void 0:t.location[1])&&(null===e||void 0===e?void 0:e.region[0])===(null===t||void 0===t?void 0:t.region[0])&&(null===e||void 0===e?void 0:e.region[1])===(null===t||void 0===t?void 0:t.region[1]):(null===e||void 0===e?void 0:e.kind)===(null===t||void 0===t?void 0:t.kind)&&(null===e||void 0===e?void 0:e.location[0])===(null===t||void 0===t?void 0:t.location[0])&&(null===e||void 0===e?void 0:e.location[1])===(null===t||void 0===t?void 0:t.location[1]))}!function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"}(fe||(fe={}));const ve=o.memo(o.forwardRef(((e,t)=>{var n,l,a,c,h,f,v,g,w,C;const{width:S,height:M,accessibilityHeight:R,columns:E,cellXOffset:I,cellYOffset:T,headerHeight:O,fillHandle:D=!1,groupHeaderHeight:H,rowHeight:z,rows:L,getCellContent:F,getRowThemeOverride:A,onHeaderMenuClick:V,onHeaderIndicatorClick:_,enableGroups:N,isFilling:Z,onCanvasFocused:W,onCanvasBlur:j,isFocused:X,selection:Y,freezeColumns:$,onContextMenu:q,freezeTrailingRows:Q,fixedShadowX:J=!0,fixedShadowY:ee=!0,drawFocusRing:te,onMouseDown:oe,onMouseUp:re,onMouseMoveRaw:le,onMouseMove:ce,onItemHovered:ve,dragAndDropState:ge,firstColAccessible:me,onKeyDown:we,onKeyUp:be,highlightRegions:ye,canvasRef:xe,onDragStart:ke,onDragEnd:Ce,eventTargetRef:Se,isResizing:Me,resizeColumn:Re,isDragging:Ee,isDraggable:Ie=!1,allowResize:Te,disabledRows:Oe,hasAppendRow:Pe,getGroupDetails:De,theme:He,prelightCells:ze,headerIcons:Le,verticalBorder:Fe,drawCell:Ae,drawHeader:Ve,onCellFocused:_e,onDragOverCell:Ne,onDrop:Be,onDragLeave:Ze,imageWindowLoader:We,smoothScrollX:je=!1,smoothScrollY:Ue=!1,experimental:Xe,getCellRenderer:Ge,resizeIndicator:Ye="full"}=e,Ke=null!==(n=e.translateX)&&void 0!==n?n:0,$e=null!==(l=e.translateY)&&void 0!==l?l:0,qe=Math.max($,Math.min(E.length-1,I)),Qe=o.useRef(null),Je=o.useRef(window),et=Je.current,tt=We,nt=o.useRef(),[ot,it]=o.useState(!1),rt=o.useRef([]),lt=o.useRef(),[at,st]=o.useState(),[ct,ut]=o.useState(),dt=o.useRef(null),[ht,ft]=o.useState(),[pt,vt]=o.useState(!1),gt=o.useRef(pt);gt.current=pt;const mt=o.useMemo((()=>new x(Le,(()=>{zt.current=void 0,Vt.current()}))),[Le]),wt=N?H+O:O,bt=o.useRef(-1),yt=null!==(a=null===Xe||void 0===Xe?void 0:Xe.enableFirefoxRescaling)&&void 0!==a&&a&&ae.value,xt=null!==(c=null===Xe||void 0===Xe?void 0:Xe.enableSafariRescaling)&&void 0!==c&&c&&se.value;o.useLayoutEffect((()=>{1!==window.devicePixelRatio&&(yt||xt)&&(-1!==bt.current&&it(!0),window.clearTimeout(bt.current),bt.current=window.setTimeout((()=>{it(!1),bt.current=-1}),200))}),[T,qe,Ke,$e,yt,xt]);const kt=(0,d.NZ)(E,$),Ct=J?(0,d.G6)(kt,ge):0,St=o.useCallback(((e,t,n)=>{const o=e.getBoundingClientRect();if(t>=kt.length||n>=L)return;const i=o.width/S,r=(0,d.Ve)(t,n,S,M,H,wt,qe,T,Ke,$e,L,$,Q,kt,z);return 1!==i&&(r.x*=i,r.y*=i,r.width*=i,r.height*=i),r.x+=o.x,r.y+=o.y,r}),[S,M,H,wt,qe,T,Ke,$e,L,$,Q,kt,z]),Mt=o.useCallback(((e,t,n,o)=>{const r=e.getBoundingClientRect(),l=r.width/S,a=(t-r.left)/l,s=(n-r.top)/l,c=(0,d.ih)(kt,qe,S,void 0,Ke);let u=0,h=0;o instanceof MouseEvent&&(u=o.button,h=o.buttons);const f=(0,d.oK)(a,c,Ke),p=(0,d.pV)(s,M,N,O,H,L,z,T,$e,Q),v=!0===(null===o||void 0===o?void 0:o.shiftKey),g=!0===(null===o||void 0===o?void 0:o.ctrlKey),m=!0===(null===o||void 0===o?void 0:o.metaKey),w=void 0!==o&&!(o instanceof MouseEvent)||"touch"===(null===o||void 0===o?void 0:o.pointerType),b=[a<0?-1:S<a?1:0,s<wt?-1:M<s?1:0];let y;if(-1===f||s<0||a<0||void 0===p||a>S||s>M){const n=a>S?1:a<0?-1:0,o=s>M?1:s<0?-1:0;let r=2*n,l=2*o;0===n&&(r=-1===f?fe.EndPadding:fe.Center),0===o&&(l=void 0===p?fe.EndPadding:fe.Center);let c=!1;if(-1===f&&-1===p){const n=St(e,kt.length-1,-1);(0,i.hu)(void 0!==n),c=t<n.x+n.width+5}const d=a>S&&a<S+(0,k.Iz)()||s>M&&s<M+(0,k.Iz)();y={kind:he,location:[-1!==f?f:a<0?0:kt.length-1,null!==p&&void 0!==p?p:L-1],region:[r,l],shiftKey:v,ctrlKey:g,metaKey:m,isEdge:c,isTouch:w,button:u,buttons:h,scrollEdge:b,isMaybeScrollbar:d}}else if(p<=-1){let o=St(e,f,p);(0,i.hu)(void 0!==o);let r=void 0!==o&&o.x+o.width-t<=5;const l=f-1;var x,C;if(t-o.x<=5&&l>=0)r=!0,o=St(e,l,p),(0,i.hu)(void 0!==o),y={kind:N&&-2===p?de:ue,location:[l,p],bounds:o,group:null!==(x=kt[l].group)&&void 0!==x?x:"",isEdge:r,shiftKey:v,ctrlKey:g,metaKey:m,isTouch:w,localEventX:t-o.x,localEventY:n-o.y,button:u,buttons:h,scrollEdge:b};else y={kind:N&&-2===p?de:ue,group:null!==(C=kt[f].group)&&void 0!==C?C:"",location:[f,p],bounds:o,isEdge:r,shiftKey:v,ctrlKey:g,metaKey:m,isTouch:w,localEventX:t-o.x,localEventY:n-o.y,button:u,buttons:h,scrollEdge:b}}else{const o=St(e,f,p);(0,i.hu)(void 0!==o);const r=void 0!==o&&o.x+o.width-t<5;let l=!1;if(D&&void 0!==Y.current){const o=(0,d.zU)(Y.current.range),i=St(e,o[0],o[1]);if(void 0!==i){const e=i.x+i.width-2,o=i.y+i.height-2;l=Math.abs(e-t)<6&&Math.abs(o-n)<6}}y={kind:"cell",location:[f,p],bounds:o,isEdge:r,shiftKey:v,ctrlKey:g,isFillHandle:l,metaKey:m,isTouch:w,localEventX:t-o.x,localEventY:n-o.y,button:u,buttons:h,scrollEdge:b}}return y}),[S,kt,qe,Ke,M,N,O,H,L,z,T,$e,Q,St,D,Y,wt]),[Rt]=null!==at&&void 0!==at?at:[],Et=o.useRef((()=>{})),It=o.useRef(at);It.current=at;const[Tt,Ot]=o.useMemo((()=>{const e=document.createElement("canvas"),t=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",t.style.display="none",t.style.opacity="0",t.style.position="fixed",[e.getContext("2d",{alpha:!1}),t.getContext("2d",{alpha:!1})]}),[]);o.useLayoutEffect((()=>{if(null!==Tt&&null!==Ot)return document.documentElement.append(Tt.canvas),document.documentElement.append(Ot.canvas),()=>{Tt.canvas.remove(),Ot.canvas.remove()}}),[Tt,Ot]);const Pt=o.useMemo((()=>new b),[]),Dt=yt&&ot?1:xt&&ot?2:5,Ht=!0===(null===Xe||void 0===Xe?void 0:Xe.disableMinimumCellWidth)?1:10,zt=o.useRef(),Lt=o.useRef(null),Ft=o.useRef(null),At=o.useCallback((()=>{var e,t,n;const o=Qe.current,i=dt.current;if(null===o||null===i)return;if(null===Lt.current&&(Lt.current=o.getContext("2d",{alpha:!1}),o.width=0,o.height=0),null===Ft.current&&(Ft.current=i.getContext("2d",{alpha:!1}),i.width=0,i.height=0),null===Lt.current||null===Ft.current||null===Tt||null===Ot)return;let r=!1;const l=zt.current,a={headerCanvasCtx:Ft.current,canvasCtx:Lt.current,bufferACtx:Tt,bufferBCtx:Ot,width:S,height:M,cellXOffset:qe,cellYOffset:T,translateX:Math.round(Ke),translateY:Math.round($e),mappedColumns:kt,enableGroups:N,freezeColumns:$,dragAndDropState:ge,theme:He,headerHeight:O,groupHeaderHeight:H,disabledRows:null!==Oe&&void 0!==Oe?Oe:u.EV.empty(),rowHeight:z,verticalBorder:Fe,isResizing:Me,resizeCol:Re,isFocused:X,selection:Y,fillHandle:D,drawCellCallback:Ae,hasAppendRow:Pe,overrideCursor:e=>{r=!0,ft(e)},maxScaleFactor:Dt,freezeTrailingRows:Q,rows:L,drawFocus:te,getCellContent:F,getGroupDetails:null!==De&&void 0!==De?De:e=>({name:e}),getRowThemeOverride:A,drawHeaderCallback:Ve,prelightCells:ze,highlightRegions:ye,imageLoader:tt,lastBlitData:lt,damage:nt.current,hoverValues:rt.current,hoverInfo:It.current,spriteManager:mt,scrolling:ot,hyperWrapping:null!==(e=null===Xe||void 0===Xe?void 0:Xe.hyperWrapping)&&void 0!==e&&e,touchMode:pt,enqueue:Et.current,renderStateProvider:Pt,renderStrategy:null!==(t=null===Xe||void 0===Xe?void 0:Xe.renderStrategy)&&void 0!==t?t:se.value?"double-buffer":"single-buffer",getCellRenderer:Ge,minimumCellWidth:Ht,resizeIndicator:Ye};void 0===a.damage?(zt.current=a,ne(a,l)):ne(a,void 0),r||void 0!==a.damage&&!a.damage.has(null===It||void 0===It||null===(n=It.current)||void 0===n?void 0:n[0])||ft(void 0)}),[Tt,Ot,S,M,qe,T,Ke,$e,kt,N,$,ge,He,O,H,Oe,z,Fe,Me,Pe,Re,X,Y,D,Q,L,te,Dt,F,De,A,Ae,Ve,ze,ye,tt,mt,ot,null===Xe||void 0===Xe?void 0:Xe.hyperWrapping,null===Xe||void 0===Xe?void 0:Xe.renderStrategy,pt,Pt,Ge,Ht,Ye]),Vt=o.useRef(At);o.useLayoutEffect((()=>{At(),Vt.current=At}),[At]),o.useLayoutEffect((()=>{(async()=>{var e,t;void 0!==(null===(e=document)||void 0===e||null===(t=e.fonts)||void 0===t?void 0:t.ready)&&(await document.fonts.ready,zt.current=void 0,Vt.current())})()}),[]);const _t=o.useCallback((e=>{nt.current=e,Vt.current(),nt.current=void 0}),[]),Nt=function(e){const t=o.useRef([]),n=o.useRef(0),i=o.useRef(e);i.current=e;const r=o.useCallback((()=>{const e=()=>{const e=t.current.map(m);t.current=[],i.current(new y(e)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?()=>window.requestAnimationFrame(e):e)}),[]);return o.useCallback((e=>{0===t.current.length&&r();const n=p(e[0],e[1]);t.current.includes(n)||t.current.push(n)}),[r])}(_t);Et.current=Nt;const Bt=o.useCallback((e=>{_t(new y(e.map((e=>e.cell))))}),[_t]);tt.setCallback(_t);const[Zt,Wt]=o.useState(!1),[jt,Ut]=null!==Rt&&void 0!==Rt?Rt:[],Xt=void 0!==jt&&-1===Ut,Gt=void 0!==jt&&-2===Ut;let Yt=!1,Kt=!1,$t=ht;if(void 0===$t&&void 0!==jt&&void 0!==Ut&&Ut>-1&&Ut<L){const e=F([jt,Ut],!0);Yt=e.kind===u.$o.NewRow||e.kind===u.$o.Marker&&"number"!==e.markerKind,Kt=e.kind===u.p6.Boolean&&(0,u.kf)(e),$t=e.cursor}const qt=Ee?"grabbing":null!==ct&&void 0!==ct&&ct||Me?"col-resize":Zt||Z?"crosshair":void 0!==$t?$t:Xt||Yt||Kt||Gt?"pointer":"default",Qt=o.useMemo((()=>({contain:"strict",display:"block",cursor:qt})),[qt]),Jt=o.useRef("default"),en=null===Se||void 0===Se?void 0:Se.current;null!==en&&void 0!==en&&Jt.current!==Qt.cursor&&(en.style.cursor=Jt.current=Qt.cursor);const tn=o.useCallback(((e,t,n,o)=>{if(void 0===De)return;const i=De(e);if(void 0!==i.actions){const e=U(t,i.actions);for(const[r,l]of e.entries())if(P(l,n+t.x,o+l.y))return i.actions[r]}}),[De]),nn=o.useCallback(((e,t,n,o)=>{const r=kt[t];if(!Ee&&!Me&&(null===ct||void 0===ct||!ct)){const l=St(e,t,-1);(0,i.hu)(void 0!==l);const a=G(void 0,r,l.x,l.y,l.width,l.height,He,"rtl"===(0,k.o7)(r.title));if(!0===r.hasMenu&&void 0!==a.menuBounds&&P(a.menuBounds,n,o))return{area:"menu",bounds:a.menuBounds};if(void 0!==r.indicatorIcon&&void 0!==a.indicatorIconBounds&&P(a.indicatorIconBounds,n,o))return{area:"indicator",bounds:a.indicatorIconBounds}}}),[kt,St,ct,Ee,Me,He]),on=o.useRef(0),rn=o.useRef(),ln=o.useRef(!1),an=o.useCallback((e=>{const t=Qe.current,n=null===Se||void 0===Se?void 0:Se.current;if(null===t||e.target!==t&&e.target!==n)return;let o,i;if(ln.current=!0,e instanceof MouseEvent?(o=e.clientX,i=e.clientY):(o=e.touches[0].clientX,i=e.touches[0].clientY),e.target===n&&null!==n){const e=n.getBoundingClientRect();if(o>e.right||i>e.bottom)return}const r=Mt(t,o,i,e);if(rn.current=r.location,r.isTouch&&(on.current=Date.now()),gt.current!==r.isTouch&&vt(r.isTouch),r.kind!==ue||void 0===nn(t,r.location[0],o,i)){if(r.kind===de){if(void 0!==tn(r.group,r.bounds,r.localEventX,r.localEventY))return}null===oe||void 0===oe||oe(r),!r.isTouch&&!0!==Ie&&Ie!==r.kind&&r.button<3&&1!==r.button&&e.preventDefault()}}),[Se,Ie,Mt,tn,nn,oe]);(0,k.OR)("touchstart",an,et,!1),(0,k.OR)("mousedown",an,et,!1);const sn=o.useRef(0),cn=o.useCallback((e=>{const t=sn.current;sn.current=Date.now();const n=Qe.current;if(ln.current=!1,void 0===re||null===n)return;const o=null===Se||void 0===Se?void 0:Se.current,i=e.target!==n&&e.target!==o;let r,l,a=!0;if(e instanceof MouseEvent){if(r=e.clientX,l=e.clientY,a=e.button<3,"touch"===e.pointerType)return}else r=e.changedTouches[0].clientX,l=e.changedTouches[0].clientY;let s=Mt(n,r,l,e);s.isTouch&&0!==on.current&&Date.now()-on.current>500&&(s={...s,isLongTouch:!0}),0!==t&&Date.now()-t<(s.isTouch?1e3:500)&&(s={...s,isDoubleClick:!0}),gt.current!==s.isTouch&&vt(s.isTouch),!i&&e.cancelable&&a&&e.preventDefault();const[c]=s.location,u=nn(n,c,r,l);var d,h;if(s.kind!==ue||void 0===u){if(s.kind===de){const e=tn(s.group,s.bounds,s.localEventX,s.localEventY);if(void 0!==e)return void(0===s.button&&e.onClick(s))}re(s,i)}else 0===s.button&&(null===(d=rn.current)||void 0===d?void 0:d[0])===c&&-1===(null===(h=rn.current)||void 0===h?void 0:h[1])||re(s,!0)}),[re,Se,Mt,nn,tn]);(0,k.OR)("mouseup",cn,et,!1),(0,k.OR)("touchend",cn,et,!1);const un=o.useCallback((e=>{const t=Qe.current;if(null===t)return;const n=null===Se||void 0===Se?void 0:Se.current,o=e.target!==t&&e.target!==n;let i,r,l=!0;e instanceof MouseEvent?(i=e.clientX,r=e.clientY,l=e.button<3):(i=e.changedTouches[0].clientX,r=e.changedTouches[0].clientY);const a=Mt(t,i,r,e);gt.current!==a.isTouch&&vt(a.isTouch),!o&&e.cancelable&&l&&e.preventDefault();const[s]=a.location;if(a.kind===ue){var c,u;const e=nn(t,s,i,r);void 0!==e&&0===a.button&&(null===(c=rn.current)||void 0===c?void 0:c[0])===s&&-1===(null===(u=rn.current)||void 0===u?void 0:u[1])&&("menu"===e.area?null===V||void 0===V||V(s,e.bounds):"indicator"===e.area&&(null===_||void 0===_||_(s,e.bounds)))}else if(a.kind===de){const e=tn(a.group,a.bounds,a.localEventX,a.localEventY);void 0!==e&&0===a.button&&e.onClick(a)}}),[Se,Mt,nn,V,_,tn]);(0,k.OR)("click",un,et,!1);const dn=o.useCallback((e=>{const t=Qe.current,n=null===Se||void 0===Se?void 0:Se.current;if(null===t||e.target!==t&&e.target!==n||void 0===q)return;const o=Mt(t,e.clientX,e.clientY,e);q(o,(()=>{e.cancelable&&e.preventDefault()}))}),[Se,Mt,q]);(0,k.OR)("contextmenu",dn,null!==(h=null===Se||void 0===Se?void 0:Se.current)&&void 0!==h?h:null,!1);const hn=o.useCallback((e=>{nt.current=new y(e.map((e=>e.item))),rt.current=e,Vt.current(),nt.current=void 0}),[]),fn=o.useMemo((()=>new ie(hn)),[hn]),pn=o.useRef(fn);pn.current=fn,o.useLayoutEffect((()=>{const e=pn.current;if(void 0===Rt||Rt[1]<0)return void e.setHovered(Rt);const t=F(Rt,!0),n=Ge(t),o=void 0===n&&t.kind===u.p6.Custom||void 0!==(null===n||void 0===n?void 0:n.needsHover)&&("boolean"===typeof n.needsHover?n.needsHover:n.needsHover(t));e.setHovered(o?Rt:void 0)}),[F,Ge,Rt]);const vn=o.useRef(),gn=o.useCallback((e=>{const t=Qe.current;if(null===t)return;const n=null===Se||void 0===Se?void 0:Se.current,o=e.target!==t&&e.target!==n,i=Mt(t,e.clientX,e.clientY,e);if("out-of-bounds"!==i.kind&&o&&!ln.current&&!i.isTouch)return;const r=(e,t)=>{st((n=>n===e?n:(null===n||void 0===n?void 0:n[0][0])!==(null===e||void 0===e?void 0:e[0][0])||(null===n||void 0===n?void 0:n[0][1])!==(null===e||void 0===e?void 0:e[0][1])||((null===n||void 0===n?void 0:n[1][0])!==(null===e||void 0===e?void 0:e[1][0])||(null===n||void 0===n?void 0:n[1][1])!==(null===e||void 0===e?void 0:e[1][1]))&&t?e:n))};if(pe(i,vn.current)){if("cell"===i.kind||i.kind===ue||i.kind===de){let e=!1,t=!0;if("cell"===i.kind){var l;const n=F(i.location),o=null===(l=Ge(n))||void 0===l?void 0:l.needsHoverPosition;t=null!==o&&void 0!==o?o:n.kind===u.p6.Custom,e=t}else e=!0;const n=[i.location,[i.localEventX,i.localEventY]];r(n,t),It.current=n,e&&_t(new y([i.location]))}}else ft(void 0),null===ve||void 0===ve||ve(i),r(i.kind===he?void 0:[i.location,[i.localEventX,i.localEventY]],!0),vn.current=i;const a=i.location[0]>=(me?0:1);ut(i.kind===ue&&i.isEdge&&a&&!0===Te),Wt("cell"===i.kind&&i.isFillHandle),null===le||void 0===le||le(e),ce(i)}),[Se,Mt,me,Te,le,ce,ve,F,Ge,_t]);(0,k.OR)("mousemove",gn,et,!0);const mn=o.useCallback((e=>{const t=Qe.current;if(null===t)return;let n,o;void 0!==Y.current&&(n=St(t,Y.current.cell[0],Y.current.cell[1]),o=Y.current.cell),null===we||void 0===we||we({bounds:n,stopPropagation:()=>e.stopPropagation(),preventDefault:()=>e.preventDefault(),cancel:()=>{},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:o})}),[we,Y,St]),wn=o.useCallback((e=>{const t=Qe.current;if(null===t)return;let n,o;void 0!==Y.current&&(n=St(t,Y.current.cell[0],Y.current.cell[1]),o=Y.current.cell),null===be||void 0===be||be({bounds:n,stopPropagation:()=>e.stopPropagation(),preventDefault:()=>e.preventDefault(),cancel:()=>{},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:o})}),[be,Y,St]),bn=o.useCallback((e=>{if(Qe.current=e,void 0!==xe&&(xe.current=e),null===e)Je.current=window;else{const t=e.getRootNode();t===document&&(Je.current=window),Je.current=t}}),[xe]),yn=o.useCallback((e=>{const t=Qe.current;if(null===t||!1===Ie||Me)return void e.preventDefault();let n,o;const r=Mt(t,e.clientX,e.clientY);if(!0!==Ie&&r.kind!==Ie)return void e.preventDefault();let l,a,s;let c=!1;if(null===ke||void 0===ke||ke({...r,setData:(e,t)=>{n=e,o=t},setDragImage:(e,t,n)=>{l=e,a=t,s=n},preventDefault:()=>c=!0,defaultPrevented:()=>c}),c||void 0===n||void 0===o||null===e.dataTransfer)e.preventDefault();else if(e.dataTransfer.setData(n,o),e.dataTransfer.effectAllowed="copyLink",void 0!==l&&void 0!==a&&void 0!==s)e.dataTransfer.setDragImage(l,a,s);else{const[n,o]=r.location;if(void 0!==o){var u;const r=document.createElement("canvas"),l=St(t,n,o);(0,i.hu)(void 0!==l);const a=Math.ceil(null!==(u=window.devicePixelRatio)&&void 0!==u?u:1);r.width=l.width*a,r.height=l.height*a;const s=r.getContext("2d");null!==s&&(s.scale(a,a),s.textBaseline="middle",-1===o?(s.font=He.headerFontFull,s.fillStyle=He.bgHeader,s.fillRect(0,0,r.width,r.height),K(s,0,0,l.width,l.height,kt[n],!1,He,!1,void 0,void 0,!1,0,mt,Ve,!1)):(s.font=He.baseFontFull,s.fillStyle=He.bgCell,s.fillRect(0,0,r.width,r.height),B(s,F([n,o]),0,o,!1,!1,0,0,l.width,l.height,!1,He,He.bgCell,tt,mt,1,void 0,!1,0,void 0,void 0,void 0,Pt,Ge,(()=>{})))),r.style.left="-100%",r.style.position="absolute",r.style.width="".concat(l.width,"px"),r.style.height="".concat(l.height,"px"),document.body.append(r),e.dataTransfer.setDragImage(r,l.width/2,l.height/2),window.setTimeout((()=>{r.remove()}),0)}}}),[Ie,Me,Mt,ke,St,He,kt,mt,Ve,F,tt,Pt,Ge]);(0,k.OR)("dragstart",yn,null!==(f=null===Se||void 0===Se?void 0:Se.current)&&void 0!==f?f:null,!1,!1);const xn=o.useRef(),kn=o.useCallback((e=>{var t;const n=Qe.current;if(void 0!==Be&&e.preventDefault(),null===n||void 0===Ne)return;const o=Mt(n,e.clientX,e.clientY),[i,r]=o.location,l=i-(me?0:1),[a,s]=null!==(t=xn.current)&&void 0!==t?t:[];a===l&&s===r||(xn.current=[l,r],Ne([l,r],e.dataTransfer))}),[me,Mt,Ne,Be]);(0,k.OR)("dragover",kn,null!==(v=null===Se||void 0===Se?void 0:Se.current)&&void 0!==v?v:null,!1,!1);const Cn=o.useCallback((()=>{xn.current=void 0,null===Ce||void 0===Ce||Ce()}),[Ce]);(0,k.OR)("dragend",Cn,null!==(g=null===Se||void 0===Se?void 0:Se.current)&&void 0!==g?g:null,!1,!1);const Sn=o.useCallback((e=>{const t=Qe.current;if(null===t||void 0===Be)return;e.preventDefault();const n=Mt(t,e.clientX,e.clientY),[o,i]=n.location;Be([o-(me?0:1),i],e.dataTransfer)}),[me,Mt,Be]);(0,k.OR)("drop",Sn,null!==(w=null===Se||void 0===Se?void 0:Se.current)&&void 0!==w?w:null,!1,!1);const Mn=o.useCallback((()=>{null===Ze||void 0===Ze||Ze()}),[Ze]);(0,k.OR)("dragleave",Mn,null!==(C=null===Se||void 0===Se?void 0:Se.current)&&void 0!==C?C:null,!1,!1);const Rn=o.useRef(Y);Rn.current=Y;const En=o.useRef(null),In=o.useCallback((e=>{if(null!==Qe.current&&Qe.current.contains(document.activeElement)){var t;if(null===e&&void 0!==Rn.current.current)null===xe||void 0===xe||null===(t=xe.current)||void 0===t||t.focus({preventScroll:!0});else null!==e&&e.focus({preventScroll:!0});En.current=e}}),[xe]);o.useImperativeHandle(t,(()=>({focus:()=>{const e=En.current;var t;null!==e&&document.contains(e)?e.focus({preventScroll:!0}):null===xe||void 0===xe||null===(t=xe.current)||void 0===t||t.focus({preventScroll:!0})},getBounds:(e,t)=>{if(void 0!==xe&&null!==xe.current)return St(xe.current,null!==e&&void 0!==e?e:0,null!==t&&void 0!==t?t:-1)},damage:Bt})),[xe,Bt,St]);const Tn=o.useRef(),On=(0,k.Qy)((()=>{var e,t,n,i;if(S<50||!0===(null===Xe||void 0===Xe?void 0:Xe.disableAccessibilityTree))return null;let r=(0,d.ih)(kt,qe,S,ge,Ke);const l=me?0:-1;me||0!==(null===(e=r[0])||void 0===e?void 0:e.sourceIndex)||(r=r.slice(1));const[a,c]=null!==(t=null===(n=Y.current)||void 0===n?void 0:n.cell)&&void 0!==t?t:[],h=null===(i=Y.current)||void 0===i?void 0:i.range,f=r.map((e=>e.sourceIndex)),v=s(T,Math.min(L,T+R));return void 0===a||void 0===c||f.includes(a)&&v.includes(c)||In(null),o.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":L+1,"aria-multiselectable":"true","aria-colcount":kt.length+l},o.createElement("thead",{role:"rowgroup"},o.createElement("tr",{role:"row","aria-rowindex":1},r.map((e=>o.createElement("th",{role:"columnheader","aria-selected":Y.columns.hasIndex(e.sourceIndex),"aria-colindex":e.sourceIndex+1+l,tabIndex:-1,onFocus:t=>{if(t.target!==En.current)return null===_e||void 0===_e?void 0:_e([e.sourceIndex,-1])},key:e.sourceIndex},e.title))))),o.createElement("tbody",{role:"rowgroup"},v.map((e=>o.createElement("tr",{role:"row","aria-selected":Y.rows.hasIndex(e),key:e,"aria-rowindex":e+2},r.map((t=>{const n=t.sourceIndex,i=p(n,e),r=a===n&&c===e,s=void 0!==h&&n>=h.x&&n<h.x+h.width&&e>=h.y&&e<h.y+h.height,d="glide-cell-".concat(n,"-").concat(e),f=[n,e],v=F(f,!0);return o.createElement("td",{key:i,role:"gridcell","aria-colindex":n+1+l,"aria-selected":s,"aria-readonly":(0,u.rs)(v)||!(0,u.Qo)(v),id:d,"data-testid":d,onClick:()=>{const t=null===xe||void 0===xe?void 0:xe.current;if(null!==t&&void 0!==t)return null===we||void 0===we?void 0:we({bounds:St(t,n,e),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:f})},onFocusCapture:t=>{var o,i;if(t.target!==En.current&&((null===(o=Tn.current)||void 0===o?void 0:o[0])!==n||(null===(i=Tn.current)||void 0===i?void 0:i[1])!==e))return Tn.current=f,null===_e||void 0===_e?void 0:_e(f)},ref:r?In:void 0,tabIndex:-1},((e,t)=>{var n;if(e.kind===u.p6.Custom)return e.copyData;const o=null===t||void 0===t?void 0:t(e);return null!==(n=null===o||void 0===o?void 0:o.getAccessibilityString(e))&&void 0!==n?n:""})(v,Ge))})))))))}),[S,kt,qe,ge,Ke,L,T,R,Y,In,F,xe,we,St,_e],200),Pn=0!==$&&J?qe>$?1:r(-Ke/100,0,1):0,Dn=ee?r(-(32*-T+$e)/100,0,1):0,Hn=o.useMemo((()=>{if(!Pn&&!Dn)return null;const e={position:"absolute",top:0,left:Ct,width:S-Ct,height:M,opacity:Pn,pointerEvents:"none",transition:je?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},t={position:"absolute",top:wt,left:0,width:S,height:M,opacity:Dn,pointerEvents:"none",transition:Ue?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return o.createElement(o.Fragment,null,Pn>0&&o.createElement("div",{id:"shadow-x",style:e}),Dn>0&&o.createElement("div",{id:"shadow-y",style:t}))}),[Pn,Dn,Ct,S,je,wt,M,Ue]),zn=o.useMemo((()=>({position:"absolute",top:0,left:0})),[]);return o.createElement(o.Fragment,null,o.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:mn,onKeyUp:wn,onFocus:W,onBlur:j,ref:bn,style:Qt},On),o.createElement("canvas",{ref:dt,style:zn}),Hn)})));function ge(e,t,n,o){var i;return r(Math.round(t-(null!==(i=e.growOffset)&&void 0!==i?i:0)),Math.ceil(n),Math.floor(o))}const me=e=>{var t;const[n,i]=o.useState(),[r,l]=o.useState(),[a,s]=o.useState(),[c,u]=o.useState(),[d,h]=o.useState(!1),[f,p]=o.useState(),[v,g]=o.useState(),[m,w]=o.useState(),[b,y]=o.useState(!1),[x,k]=o.useState(),{onHeaderMenuClick:C,onHeaderIndicatorClick:S,getCellContent:M,onColumnMoved:R,onColumnResize:E,onColumnResizeStart:I,onColumnResizeEnd:T,gridRef:O,maxColumnWidth:P,minColumnWidth:D,onRowMoved:H,lockColumns:z,onColumnProposeMove:L,onMouseDown:F,onMouseUp:A,onItemHovered:V,onDragStart:_,canvasRef:N}=e,B=void 0!==(null!==(t=null!==E&&void 0!==E?E:T)&&void 0!==t?t:I),{columns:Z,selection:W}=e,j=W.columns,U=o.useCallback((e=>{const[t,n]=e.location;void 0!==a&&c!==t&&t>=z?(h(!0),u(t)):void 0!==v&&void 0!==n?(y(!0),w(Math.max(0,n))):void 0!==r||d||b||null===V||void 0===V||V(e)}),[a,v,c,V,z,r,d,b]),X=void 0!==R,G=o.useCallback((e=>{if(0===e.button){const[o,r]=e.location;if("out-of-bounds"===e.kind&&e.isEdge&&B){var t;const e=null===O||void 0===O||null===(t=O.current)||void 0===t?void 0:t.getBounds(Z.length-1,-1);void 0!==e&&(i(e.x),l(Z.length-1))}else if("header"===e.kind&&o>=z){const t=null===N||void 0===N?void 0:N.current;if(e.isEdge&&B&&t){var n;i(e.bounds.x),l(o);const r=t.getBoundingClientRect().width/t.offsetWidth,a=e.bounds.width/r;null===I||void 0===I||I(Z[o],a,o,a+(null!==(n=Z[o].growOffset)&&void 0!==n?n:0))}else"header"===e.kind&&X&&(p(e.bounds.x),s(o))}else"cell"===e.kind&&z>0&&0===o&&void 0!==r&&void 0!==H&&(k(e.bounds.y),g(r))}null===F||void 0===F||F(e)}),[F,B,z,H,O,Z,X,I,N]),Y=o.useCallback(((e,t)=>{d||b||null===C||void 0===C||C(e,t)}),[d,b,C]),K=o.useCallback(((e,t)=>{d||b||null===S||void 0===S||S(e,t)}),[d,b,S]),$=o.useRef(-1),q=o.useCallback((()=>{$.current=-1,g(void 0),w(void 0),k(void 0),y(!1),s(void 0),u(void 0),p(void 0),h(!1),l(void 0),i(void 0)}),[]),Q=o.useCallback(((e,t)=>{if(0===e.button){if(void 0!==r){var n;if(!0===(null===j||void 0===j?void 0:j.hasIndex(r)))for(const t of j){var o;if(t===r)continue;const e=Z[t],n=ge(e,$.current,D,P);null===E||void 0===E||E(e,n,t,n+(null!==(o=e.growOffset)&&void 0!==o?o:0))}const e=ge(Z[r],$.current,D,P);if(null===T||void 0===T||T(Z[r],e,r,e+(null!==(n=Z[r].growOffset)&&void 0!==n?n:0)),j.hasIndex(r))for(const t of j){var i;if(t===r)continue;const e=Z[t],n=ge(e,$.current,D,P);null===T||void 0===T||T(e,n,t,n+(null!==(i=e.growOffset)&&void 0!==i?i:0))}}q(),void 0!==a&&void 0!==c&&(null===R||void 0===R||R(a,c)),void 0!==v&&void 0!==m&&(null===H||void 0===H||H(v,m))}null===A||void 0===A||A(e,t)}),[A,r,a,c,v,m,j,T,Z,D,P,E,R,H,q]),J=o.useMemo((()=>{if(void 0!==a&&void 0!==c&&a!==c&&!1!==(null===L||void 0===L?void 0:L(a,c)))return{src:a,dest:c}}),[a,c,L]),ee=o.useCallback((e=>{const t=null===N||void 0===N?void 0:N.current;if(void 0!==a&&void 0!==f){Math.abs(e.clientX-f)>20&&h(!0)}else if(void 0!==v&&void 0!==x){Math.abs(e.clientY-x)>20&&y(!0)}else if(void 0!==r&&void 0!==n&&t){var o;const l=t.getBoundingClientRect().width/t.offsetWidth,a=(e.clientX-n)/l,s=Z[r],c=ge(s,a,D,P);if(null===E||void 0===E||E(s,c,r,c+(null!==(o=s.growOffset)&&void 0!==o?o:0)),$.current=a,(null===j||void 0===j?void 0:j.first())===r)for(const e of j){var i;if(e===r)continue;const t=Z[e],n=ge(t,$.current,D,P);null===E||void 0===E||E(t,n,e,n+(null!==(i=t.growOffset)&&void 0!==i?i:0))}}}),[a,f,v,x,r,n,Z,D,P,E,j,N]),te=o.useCallback(((e,t)=>{if(void 0===v||void 0===m)return M(e,t);let[n,o]=e;return o===m?o=v:(o>m&&(o-=1),o>=v&&(o+=1)),M([n,o],t)}),[v,m,M]),ne=o.useCallback((e=>{null===_||void 0===_||_(e),e.defaultPrevented()||q()}),[q,_]);return o.createElement(ve,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:te,isResizing:void 0!==r,onHeaderMenuClick:Y,onHeaderIndicatorClick:K,isDragging:d,onItemHovered:U,onDragStart:ne,onMouseDown:G,allowResize:B,onMouseUp:Q,dragAndDropState:J,onMouseMoveRaw:ee,ref:O})};var we=n(74559);const be=(e,t,n)=>{const i=(0,o.useRef)(null),r=(0,o.useRef)(null),l=(0,o.useRef)(null),a=(0,o.useRef)(0),s=(0,o.useRef)(t);s.current=t;const c=n.current;(0,o.useEffect)((()=>{const t=()=>{if(!1===r.current&&null!==c){var e,n;const o=[c.scrollLeft,c.scrollTop];if((null===(e=l.current)||void 0===e?void 0:e[0])===o[0]&&(null===(n=l.current)||void 0===n?void 0:n[1])===o[1]){if(a.current>10)return l.current=null,void(r.current=null);a.current++}else a.current=0,s.current(o[0],o[1]),l.current=o;i.current=window.setTimeout(t,1e3/120)}},n=()=>{r.current=!0,l.current=null,null!==i.current&&(window.clearTimeout(i.current),i.current=null)},o=e=>{0===e.touches.length&&(r.current=!1,a.current=0,i.current=window.setTimeout(t,1e3/120))};if(e&&null!==c){const e=c;return e.addEventListener("touchstart",n),e.addEventListener("touchend",o),()=>{e.removeEventListener("touchstart",n),e.removeEventListener("touchend",o),null!==i.current&&window.clearTimeout(i.current)}}}),[e,c])},ye=(0,we.z)("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[e=>e.isSafari?"scroll":"auto"]}});const xe=e=>{var t,n,i,r;const{children:l,clientHeight:a,scrollHeight:s,scrollWidth:c,update:u,draggable:d,className:h,preventDiagonalScrolling:f=!1,paddingBottom:p=0,paddingRight:v=0,rightElement:g,rightElementProps:m,kineticScrollPerfHack:w=!1,scrollRef:b,initialSize:y}=e,x=[],C=null!==(t=null===m||void 0===m?void 0:m.sticky)&&void 0!==t&&t,S=null!==(n=null===m||void 0===m?void 0:m.fill)&&void 0!==n&&n,M=o.useRef(0),R=o.useRef(0),E=o.useRef(null),I="undefined"===typeof window?1:window.devicePixelRatio,T=o.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),O=o.useRef(null),P=function(e){const[t,n]=o.useState(!1),i="undefined"===typeof window?null:window,r=o.useRef(0);return(0,k.OR)("touchstart",o.useCallback((()=>{window.clearTimeout(r.current),n(!0)}),[]),i,!0,!1),(0,k.OR)("touchend",o.useCallback((t=>{0===t.touches.length&&(r.current=window.setTimeout((()=>n(!1)),e))}),[e]),i,!0,!1),t}(200),[D,H]=o.useState(!0),z=o.useRef(0);o.useLayoutEffect((()=>{if(!D||P||void 0===T.current.lockDirection)return;const e=E.current;if(null===e)return;const[t,n]=T.current.lockDirection;void 0!==t?e.scrollLeft=t:void 0!==n&&(e.scrollTop=n),T.current.lockDirection=void 0}),[P,D]);const L=o.useCallback(((e,t)=>{var n,o,i,r,l,a;const c=E.current;if(null===c)return;t=null!==(n=t)&&void 0!==n?n:c.scrollTop,e=null!==(o=e)&&void 0!==o?o:c.scrollLeft;const d=T.current.scrollTop,h=T.current.scrollLeft,g=e-h,m=t-d;P&&0!==g&&0!==m&&(Math.abs(g)>3||Math.abs(m)>3)&&f&&void 0===T.current.lockDirection&&(T.current.lockDirection=Math.abs(g)<Math.abs(m)?[h,void 0]:[void 0,d]);const w=T.current.lockDirection;e=null!==(i=null===w||void 0===w?void 0:w[0])&&void 0!==i?i:e,t=null!==(r=null===w||void 0===w?void 0:w[1])&&void 0!==r?r:t,T.current.scrollLeft=e,T.current.scrollTop=t;const b=c.clientWidth,y=c.clientHeight,x=t,k=R.current-x,C=c.scrollHeight-y;if(R.current=x,C>0&&(Math.abs(k)>2e3||0===x||x===C)&&s>c.scrollHeight+5){const e=(s-y)*(x/C);M.current=e-x}void 0!==w&&(window.clearTimeout(z.current),H(!1),z.current=window.setTimeout((()=>H(!0)),200)),u({x:e,y:x+M.current,width:b-v,height:y-p,paddingRight:null!==(l=null===(a=O.current)||void 0===a?void 0:a.clientWidth)&&void 0!==l?l:0})}),[p,v,s,u,f,P]);be(w&&se.value,L,E);const F=o.useRef(L);F.current=L;const A=o.useRef(),V=o.useRef(!1);o.useLayoutEffect((()=>{V.current?L():V.current=!0}),[L,p,v]);const _=o.useCallback((e=>{E.current=e,void 0!==b&&(b.current=e)}),[b]);let N=0,B=0;for(x.push(o.createElement("div",{key:N++,style:{width:c,height:0}}));B<s;){const e=Math.min(5e6,s-B);x.push(o.createElement("div",{key:N++,style:{width:0,height:e}})),B+=e}const{ref:Z,width:W,height:j}=function(e){const t=(0,o.useRef)(null),[n,i]=(0,o.useState)({width:null===e||void 0===e?void 0:e[0],height:null===e||void 0===e?void 0:e[1]});return(0,o.useLayoutEffect)((()=>{const e=new window.ResizeObserver((e=>{for(const t of e){const{width:e,height:n}=t&&t.contentRect||{};i((t=>t.width===e&&t.height===n?t:{width:e,height:n}))}}));return t.current&&e.observe(t.current,void 0),()=>{e.disconnect()}}),[t.current]),{ref:t,...n}}(y);return"undefined"===typeof window||(null===(i=A.current)||void 0===i?void 0:i.height)===j&&(null===(r=A.current)||void 0===r?void 0:r.width)===W||(window.setTimeout((()=>F.current()),0),A.current={width:W,height:j}),0===(null!==W&&void 0!==W?W:0)||0===(null!==j&&void 0!==j?j:0)?o.createElement("div",{ref:Z}):o.createElement("div",{ref:Z},o.createElement(ye,{isSafari:se.value},o.createElement("div",{className:"dvn-underlay"},l),o.createElement("div",{ref:_,style:A.current,draggable:d,onDragStart:e=>{d||(e.stopPropagation(),e.preventDefault())},className:"dvn-scroller "+(null!==h&&void 0!==h?h:""),onScroll:()=>L()},o.createElement("div",{className:"dvn-scroll-inner"+(void 0===g?" dvn-hidden":"")},o.createElement("div",{className:"dvn-stack"},x),void 0!==g&&o.createElement(o.Fragment,null,!S&&o.createElement("div",{className:"dvn-spacer"}),o.createElement("div",{ref:O,style:{height:j,maxHeight:a-Math.ceil(I%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:v,flexGrow:S?1:void 0,right:C?null!==v&&void 0!==v?v:0:void 0,pointerEvents:"auto"}},g))))))},ke=e=>{const{columns:t,rows:n,rowHeight:i,headerHeight:r,groupHeaderHeight:l,enableGroups:a,freezeColumns:s,experimental:c,nonGrowWidth:u,clientSize:d,className:h,onVisibleRegionChanged:f,scrollRef:p,preventDiagonalScrolling:v,rightElement:g,rightElementProps:m,overscrollX:w,overscrollY:b,initialSize:y,smoothScrollX:x=!1,smoothScrollY:k=!1,isDraggable:C}=e,{paddingRight:S,paddingBottom:M}=null!==c&&void 0!==c?c:{},[R,E]=d,I=o.useRef(),T=o.useRef(),O=o.useRef(),P=o.useRef(),D=u+Math.max(0,null!==w&&void 0!==w?w:0);let H=a?r+l:r;if("number"===typeof i)H+=n*i;else for(let o=0;o<n;o++)H+=i(o);void 0!==b&&(H+=b);const z=o.useRef(),L=o.useCallback((()=>{var e,o;if(void 0===z.current)return;const r={...z.current};let l=0,a=r.x<0?-r.x:0,c=0,u=0;r.x=r.x<0?0:r.x;let d=0;for(let n=0;n<s;n++)d+=t[n].width;for(const n of t){const e=l-d;if(r.x>=e+n.width)l+=n.width,u++,c++;else if(r.x>e)l+=n.width,x?a+=e-r.x:u++,c++;else{if(!(r.x+r.width>e))break;l+=n.width,c++}}let h=0,p=0,v=0;if("number"===typeof i)k?(p=Math.floor(r.y/i),h=p*i-r.y):p=Math.ceil(r.y/i),v=Math.ceil(r.height/i)+p,h<0&&v++;else{let e=0;for(let t=0;t<n;t++){const n=i(t),o=e+(k?0:n/2);if(r.y>=e+n)e+=n,p++,v++;else if(r.y>o)e+=n,k?h+=o-r.y:p++,v++;else{if(!(r.y+r.height>n/2+e))break;e+=n,v++}}}const g={x:u,y:p,width:c-u,height:v-p},m=I.current;var w;void 0!==m&&m.y===g.y&&m.x===g.x&&m.height===g.height&&m.width===g.width&&T.current===a&&O.current===h&&r.width===(null===(e=P.current)||void 0===e?void 0:e[0])&&r.height===(null===(o=P.current)||void 0===o?void 0:o[1])||(null===f||void 0===f||f({x:u,y:p,width:c-u,height:v-p},r.width,r.height,null!==(w=r.paddingRight)&&void 0!==w?w:0,a,h),I.current=g,T.current=a,O.current=h,P.current=[r.width,r.height])}),[t,i,n,f,s,x,k]),F=o.useCallback((e=>{z.current=e,L()}),[L]);return o.useEffect((()=>{L()}),[L]),o.createElement(xe,{scrollRef:p,className:h,kineticScrollPerfHack:null===c||void 0===c?void 0:c.kineticScrollPerfHack,preventDiagonalScrolling:v,draggable:!0===C||"string"===typeof C,scrollWidth:D+(null!==S&&void 0!==S?S:0),scrollHeight:H+(null!==M&&void 0!==M?M:0),clientHeight:E,rightElement:g,paddingBottom:M,paddingRight:S,rightElementProps:m,update:F,initialSize:y},o.createElement(me,{eventTargetRef:p,width:R,height:E,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},Ce=(0,we.z)("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),Se=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),Me=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),Re=o.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},o.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),Ee=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:l,searchInputRef:a,searchValue:s,searchResults:c,onSearchValueChange:d,getCellsForSelection:h,onSearchResultsChanged:f,showSearch:p=!1,onSearchClose:v}=e,[g]=o.useState((()=>"search-box-"+Math.round(1e3*Math.random()))),[m,w]=o.useState(""),b=null!==s&&void 0!==s?s:m,y=o.useCallback((e=>{w(e),null===d||void 0===d||d(e)}),[d]),[x,k]=o.useState(),C=o.useRef(x);C.current=x,o.useEffect((()=>{void 0!==c&&(c.length>0?k((e=>{var t;return{rowsSearched:r,results:c.length,selectedIndex:null!==(t=null===e||void 0===e?void 0:e.selectedIndex)&&void 0!==t?t:-1}})):k(void 0))}),[r,c]);const S=o.useRef();void 0===S.current&&(S.current=new AbortController);const M=o.useRef(),[R,E]=o.useState([]),I=null!==c&&void 0!==c?c:R,T=o.useCallback((()=>{void 0!==M.current&&(window.cancelAnimationFrame(M.current),M.current=void 0,S.current.abort())}),[]),O=o.useRef(n);O.current=n;const P=o.useCallback((e=>{const t=new RegExp(e.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let n=O.current,o=Math.min(10,r),a=0;k(void 0),E([]);const s=[],c=async()=>{var e,d;if(void 0===h)return;const p=performance.now(),v=r-a;let g=h({x:0,y:n,width:l.length,height:Math.min(o,v,r-n)},S.current.signal);"function"===typeof g&&(g=await g());let m=!1;for(const[o,i]of g.entries())for(const[e,r]of i.entries()){let i;switch(r.kind){case u.p6.Text:case u.p6.Number:i=r.displayData;break;case u.p6.Uri:case u.p6.Markdown:i=r.data;break;case u.p6.Boolean:i="boolean"===typeof r.data?r.data.toString():void 0;break;case u.p6.Image:case u.p6.Bubble:i=r.data.join("\ud83d\udc33");break;case u.p6.Custom:i=r.copyData}void 0!==i&&t.test(i)&&(s.push([e,o+n]),m=!0)}const w=performance.now();m&&E([...s]),a+=g.length,(0,i.hu)(a<=r);const b=null!==(e=null===(d=C.current)||void 0===d?void 0:d.selectedIndex)&&void 0!==e?e:-1;k({results:s.length,rowsSearched:a,selectedIndex:b}),null===f||void 0===f||f(s,b),n+o>=r?n=0:n+=o;const y=w-p,x=10/Math.max(y,1);o=Math.ceil(o*x),a<r&&s.length<1e3&&(M.current=window.requestAnimationFrame(c))};T(),M.current=window.requestAnimationFrame(c)}),[T,l.length,h,f,r]),D=o.useCallback((()=>{var e;null===v||void 0===v||v(),k(void 0),E([]),null===f||void 0===f||f([],-1),T(),null===t||void 0===t||null===(e=t.current)||void 0===e||e.focus()}),[T,t,v,f]),H=o.useCallback((e=>{y(e.target.value),void 0===c&&(""===e.target.value?(k(void 0),E([]),T()):P(e.target.value))}),[P,T,y,c]);o.useEffect((()=>{p&&null!==a.current&&(y(""),a.current.focus({preventScroll:!0}))}),[p,a,y]);const z=o.useCallback((e=>{var t;if(null===e||void 0===e||null===(t=e.stopPropagation)||void 0===t||t.call(e),void 0===x)return;const n=(x.selectedIndex+1)%x.results;k({...x,selectedIndex:n}),null===f||void 0===f||f(I,n)}),[x,f,I]),L=o.useCallback((e=>{var t;if(null===e||void 0===e||null===(t=e.stopPropagation)||void 0===t||t.call(e),void 0===x)return;let n=(x.selectedIndex-1)%x.results;n<0&&(n+=x.results),k({...x,selectedIndex:n}),null===f||void 0===f||f(I,n)}),[f,I,x]),F=o.useCallback((e=>{(e.ctrlKey||e.metaKey)&&"KeyF"===e.nativeEvent.code||"Escape"===e.key?(D(),e.stopPropagation(),e.preventDefault()):"Enter"===e.key&&(e.shiftKey?L():z())}),[D,z,L]);o.useEffect((()=>()=>{T()}),[T]);const[A,V]=o.useState(!1);o.useEffect((()=>{if(!p){const e=setTimeout((()=>V(!1)),150);return()=>clearTimeout(e)}V(!0)}),[p]);const _=o.useMemo((()=>{var e,t,n;if(!p&&!A)return null;let i;void 0!==x&&(i=x.results>=1e3?"over 1000":"".concat(x.results," result").concat(1!==x.results?"s":""),x.selectedIndex>=0&&(i="".concat(x.selectedIndex+1," of ").concat(i)));const l=e=>{e.stopPropagation()},s=Math.floor((null!==(e=null===x||void 0===x?void 0:x.rowsSearched)&&void 0!==e?e:0)/r*100),c={width:"".concat(s,"%")};return o.createElement(Ce,{className:p?"":"out",onMouseDown:l,onMouseMove:l,onMouseUp:l,onClick:l},o.createElement("div",{className:"gdg-search-bar-inner"},o.createElement("input",{id:g,"aria-hidden":!p,"data-testid":"search-input",ref:a,onChange:H,value:b,tabIndex:p?void 0:-1,onKeyDownCapture:F}),o.createElement("button",{"aria-label":"Previous Result","aria-hidden":!p,tabIndex:p?void 0:-1,onClick:L,disabled:0===(null!==(t=null===x||void 0===x?void 0:x.results)&&void 0!==t?t:0)},Se),o.createElement("button",{"aria-label":"Next Result","aria-hidden":!p,tabIndex:p?void 0:-1,onClick:z,disabled:0===(null!==(n=null===x||void 0===x?void 0:x.results)&&void 0!==n?n:0)},Me),void 0!==v&&o.createElement("button",{"aria-label":"Close Search","aria-hidden":!p,"data-testid":"search-close-button",tabIndex:p?void 0:-1,onClick:D},Re)),void 0!==x?o.createElement(o.Fragment,null,o.createElement("div",{className:"gdg-search-status"},o.createElement("div",{"data-testid":"search-result-area"},i)),o.createElement("div",{className:"gdg-search-progress",style:c})):o.createElement("div",{className:"gdg-search-status"},o.createElement("label",{htmlFor:g},"Type to search")))}),[p,A,x,r,g,a,H,b,F,L,z,v,D]);return o.createElement(o.Fragment,null,o.createElement(ke,{prelightCells:I,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),_)};var Ie=n(84256);const Te=(0,we.z)("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[e=>Math.max(16,e.targetHeight-10),"px"]}}),Oe=e=>{const{bounds:t,group:n,onClose:i,canvasBounds:r,onFinish:l}=e,[a,s]=o.useState(n);return o.createElement(Ie.Z,{style:{position:"absolute",left:t.x-r.left+1,top:t.y-r.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:i},o.createElement(Te,{targetHeight:t.height,"data-testid":"group-rename-input",value:a,onBlur:i,onFocus:e=>e.target.setSelectionRange(0,a.length),onChange:e=>s(e.target.value),onKeyDown:e=>{"Enter"===e.key?l(a):"Escape"===e.key&&i()},autoFocus:!0}))},Pe=150;function De(e,t,n,o,i,r,l,a,s){let c=0;const u=void 0===i?[]:i.map((n=>{const i=function(e,t,n,o){var i,r;const l=o(t);return null!==(i=null===l||void 0===l||null===(r=l.measure)||void 0===r?void 0:r.call(l,e,t,n))&&void 0!==i?i:Pe}(e,n[o],t,s);return c=Math.max(c,i),i}));if(u.length>5&&a){c=0;let e=0;for(const n of u)e+=n;const t=e/u.length;for(let n=0;n<u.length;n++)u[n]>=2*t?u[n]=0:c=Math.max(c,u[n])}c=Math.max(c,e.measureText(n.title).width+2*t.cellHorizontalPadding+(void 0===n.icon?0:28));const d=Math.max(Math.ceil(r),Math.min(Math.floor(l),Math.ceil(c)));return{...n,width:d}}function He(e,t,n){const o=ze(e,t);return o&&(n.didMatch=!0),o}function ze(e,t){if(0===e.length)return!1;if(e.includes("|")){const n=e.split("|");for(const e of n)if(ze(e,t))return!0;return!1}let n=!1,o=!1,i=!1,r=!1;const l=e.split("+");if(!function(e,t){if(void 0===e)return!1;if(e.length>1&&e.startsWith("_"))return Number.parseInt(e.slice(1))===t.keyCode;return 1===e.length&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}(l.pop(),t))return!1;if("any"===l[0])return!0;for(const a of l)switch(a){case"ctrl":n=!0;break;case"shift":o=!0;break;case"alt":i=!0;break;case"meta":r=!0;break;case"primary":ce.value?r=!0:n=!0}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===o&&t.metaKey===r}function Le(e,t){return e.map(((e,n)=>{const o=t[n];return e.map((e=>void 0!==e.span&&e.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:function(e){var t,n;if(void 0!==e.copyData)return{formatted:e.copyData,rawValue:e.copyData,format:"string"};switch(e.kind){case u.p6.Boolean:return{formatted:!0===e.data?"TRUE":!1===e.data?"FALSE":e.data===u.sd?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case u.p6.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case u.p6.Image:case u.p6.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case u.p6.Drilldown:return{formatted:e.data.map((e=>e.text)),rawValue:e.data.map((e=>e.text)),format:"string-array"};case u.p6.Text:return{formatted:null!==(t=e.displayData)&&void 0!==t?t:e.data,rawValue:e.data,format:"string"};case u.p6.Uri:return{formatted:null!==(n=e.displayData)&&void 0!==n?n:e.data,rawValue:e.data,format:"url"};case u.p6.Markdown:case u.p6.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case u.p6.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case u.p6.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case u.p6.Protected:return{formatted:"************",rawValue:"",format:"string"};default:(0,i.vE)(e)}}(e)))}))}function Fe(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e='"'.concat(e.replace(/"/g,'""'),'"')),e}function Ae(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,(e=>"<span> </span>".repeat(e.length)))}function Ve(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function _e(e,t){const n=Le(e,t),o=function(e){const t=[];for(const i of e){const e=[];for(const t of i){var n,o;"url"===t.format?e.push(null!==(n=null===(o=t.rawValue)||void 0===o?void 0:o.toString())&&void 0!==n?n:""):"string-array"===t.format?e.push(t.formatted.map((e=>Fe(e,!0))).join(",")):e.push(Fe(t.formatted,!1))}t.push(e.join("\t"))}return t.join("\n")}(n),i=function(e){const t=[];t.push('<style type="text/css">\x3c!--br {mso-data-placement:same-cell;}--\x3e</style>',"<table><tbody>");for(const i of e){t.push("<tr>");for(const e of i){const i='gdg-format="'.concat(e.format,'"');var n,o;"url"===e.format?t.push("<td ".concat(i,'><a href="').concat(e.rawValue,'">').concat(Ae(e.formatted),"</a></td>")):"string-array"===e.format?t.push("<td ".concat(i,"><ol>").concat(e.formatted.map(((t,n)=>"<li gdg-raw-value=".concat(Ve(e.rawValue[n]),">")+Ae(t)+"</li>")).join(""),"</ol></td>")):t.push("<td gdg-raw-value=".concat(Ve(null!==(n=null===(o=e.rawValue)||void 0===o?void 0:o.toString())&&void 0!==n?n:"")," ").concat(i,">").concat(Ae(e.formatted),"</td>"))}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}(n);return{textPlain:o,textHtml:i}}function Ne(e){const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(null===n)return;const o=[n],i=[];let r;for(;o.length>0;){const e=o.pop();if(void 0===e)break;if(e instanceof HTMLTableElement||"TBODY"===e.nodeName)o.push(...[...e.children].reverse());else if(e instanceof HTMLTableRowElement)void 0!==r&&i.push(r),r=[],o.push(...[...e.children].reverse());else if(e instanceof HTMLTableCellElement){var l;const t=e.cloneNode(!0),n=1===t.children.length&&"P"===t.children[0].nodeName?t.children[0]:null,o=1===(null===n||void 0===n?void 0:n.children.length)&&"FONT"===n.children[0].nodeName,i=t.querySelectorAll("br");for(const e of i)e.replaceWith("\n");const b=t.getAttribute("gdg-raw-value"),y=null!==(l=t.getAttribute("gdg-format"))&&void 0!==l?l:"string";var a,s,c,u;if(null!==t.querySelector("a"))null===(a=r)||void 0===a||a.push({rawValue:null!==(s=null===(c=t.querySelector("a"))||void 0===c?void 0:c.getAttribute("href"))&&void 0!==s?s:"",formatted:null!==(u=t.textContent)&&void 0!==u?u:"",format:y});else if(null!==t.querySelector("ol")){var d;const e=t.querySelectorAll("li");null===(d=r)||void 0===d||d.push({rawValue:[...e].map((e=>{var t;return null!==(t=e.getAttribute("gdg-raw-value"))&&void 0!==t?t:""})),formatted:[...e].map((e=>{var t;return null!==(t=e.textContent)&&void 0!==t?t:""})),format:"string-array"})}else if(null!==b){var h,f;null===(h=r)||void 0===h||h.push({rawValue:(w=b,w.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")),formatted:null!==(f=t.textContent)&&void 0!==f?f:"",format:y})}else{var p,v,g,m;let e=null!==(p=t.textContent)&&void 0!==p?p:"";o&&(e=e.replace(/\n(?!\n)/g,"")),null===(v=r)||void 0===v||v.push({rawValue:null!==(g=e)&&void 0!==g?g:"",formatted:null!==(m=e)&&void 0!==m?m:"",format:y})}}}var w;return void 0!==r&&i.push(r),i}function Be(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function Ze(e){let t;!function(e){e[e.None=0]="None",e[e.inString=1]="inString",e[e.inStringPostQuote=2]="inStringPostQuote"}(t||(t={}));const n=[];let o=[],i=0,r=t.None;e=e.replace(/\r\n/g,"\n");let l=0;for(const a of e){switch(r){case t.None:"\t"===a||"\n"===a?(o.push(e.slice(i,l)),i=l+1,"\n"===a&&(n.push(o),o=[])):'"'===a&&(r=t.inString);break;case t.inString:'"'===a&&(r=t.inStringPostQuote);break;case t.inStringPostQuote:'"'===a?r=t.inString:"\t"===a||"\n"===a?(o.push(Be(e.slice(i,l))),i=l+1,"\n"===a&&(n.push(o),o=[]),r=t.None):r=t.None}l++}return i<e.length&&o.push(Be(e.slice(i,e.length))),n.push(o),n.map((e=>e.map((e=>({rawValue:e,formatted:e,format:"string"})))))}function We(e,t,n){var o;const i=_e(e,t),r=e=>{var t;null===(t=window.navigator.clipboard)||void 0===t||t.writeText(e)},l=(e,t)=>{try{var o,i;if(void 0===n||null===n.clipboardData)throw new Error("No clipboard data");null===n||void 0===n||null===(o=n.clipboardData)||void 0===o||o.setData("text/plain",e),null===n||void 0===n||null===(i=n.clipboardData)||void 0===i||i.setData("text/html",t)}catch{((e,t)=>{var n;return void 0!==(null===(n=window.navigator.clipboard)||void 0===n?void 0:n.write)&&(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([e],{type:"text/plain"}),"text/html":new Blob([t],{type:"text/html"})})]),!0)})(e,t)||r(e)}};void 0!==(null===(o=window.navigator.clipboard)||void 0===o?void 0:o.write)||void 0!==(null===n||void 0===n?void 0:n.clipboardData)?l(i.textPlain,i.textHtml):r(i.textPlain),null===n||void 0===n||n.preventDefault()}function je(e){return!0!==e}function Ue(e){return"string"===typeof e?e:"".concat(e,"px")}const Xe=(0,we.z)("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[e=>e.innerWidth],"wmyidgi-1":[e=>e.innerHeight]}}),Ge=e=>{const{inWidth:t,inHeight:n,children:i,...r}=e;return o.createElement(Xe,{innerHeight:Ue(n),innerWidth:Ue(t),...r},i)};const Ye={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function Ke(e,t){return!0===e?t:!1===e?"":e}function $e(e){const t=ce.value;return{activateCell:Ke(e.activateCell," |Enter|shift+Enter"),clear:Ke(e.clear,"any+Escape"),closeOverlay:Ke(e.closeOverlay,"any+Escape"),acceptOverlayDown:Ke(e.acceptOverlayDown,"Enter"),acceptOverlayUp:Ke(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:Ke(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:Ke(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:Ke(e.delete,t?"Backspace|Delete":"Delete"),downFill:Ke(e.downFill,"primary+_68"),scrollToSelectedCell:Ke(e.scrollToSelectedCell,"primary+Enter"),goDownCell:Ke(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:Ke(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:Ke(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:Ke(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:Ke(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:Ke(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:Ke(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:Ke(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:Ke(e.goToFirstCell,"primary+Home"),goToFirstColumn:Ke(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:Ke(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:Ke(e.goToLastCell,"primary+End"),goToLastColumn:Ke(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:Ke(e.goToLastRow,"primary+ArrowDown"),goToNextPage:Ke(e.goToNextPage,"PageDown"),goToPreviousPage:Ke(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:Ke(e.rightFill,"primary+_82"),search:Ke(e.search,"primary+f"),selectAll:Ke(e.selectAll,"primary+a"),selectColumn:Ke(e.selectColumn,"ctrl+ "),selectGrowDown:Ke(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:Ke(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:Ke(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:Ke(e.selectGrowUp,"shift+ArrowUp"),selectRow:Ke(e.selectRow,"shift+ "),selectToFirstCell:Ke(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:Ke(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:Ke(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:Ke(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:Ke(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:Ke(e.selectToLastRow,"primary+shift+ArrowDown")}}function qe(e){function t(e,n,o){if("number"===typeof e)return{headerIndex:e,isCollapsed:!1,depth:n,path:o};const i={headerIndex:e.headerIndex,isCollapsed:e.isCollapsed,depth:n,path:o};return void 0!==e.subGroups&&(i.subGroups=e.subGroups.map(((e,i)=>t(e,n+1,[...o,i]))).sort(((e,t)=>e.headerIndex-t.headerIndex))),i}return e.map(((e,n)=>t(e,0,[n]))).sort(((e,t)=>e.headerIndex-t.headerIndex))}function Qe(e,t){const n=[];function o(e,i){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=null!==i?i-e.headerIndex:t-e.headerIndex;if(void 0!==e.subGroups&&(l=e.subGroups[0].headerIndex-e.headerIndex),l--,n.push({headerIndex:e.headerIndex,contentIndex:-1,skip:r,isCollapsed:e.isCollapsed,depth:e.depth,path:e.path,rows:l}),e.subGroups)for(let t=0;t<e.subGroups.length;t++){const n=t<e.subGroups.length-1?e.subGroups[t+1].headerIndex:i;o(e.subGroups[t],n,r||e.isCollapsed)}}const i=qe(e.groups);for(let l=0;l<i.length;l++){const e=l<i.length-1?i[l+1].headerIndex:null;o(i[l],e)}let r=0;for(const l of n)l.contentIndex=r,r+=l.rows;return n.filter((e=>!1===e.skip)).map((e=>{const{skip:t,...n}=e;return n}))}function Je(e,t){if(void 0===t||0===Qe.length)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const o of t){if(0===n)return{path:[...o.path,-1],originalIndex:o.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:o.rows};if(n--,!o.isCollapsed){if(n<o.rows)return{path:[...o.path,n],originalIndex:o.headerIndex+n,isGroupHeader:!1,groupIndex:n,contentIndex:o.contentIndex+n,groupRows:o.rows};n-=o.rows}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function et(e,t,n){const[o,...i]=t;return-1===i[0]?e.map(((e,t)=>t===o?{...e,...n}:e)):e.map(((e,t)=>{var r;return t===o?{...e,subGroups:et(null!==(r=e.subGroups)&&void 0!==r?r:[],i,n)}:e}))}function tt(e,t){var n;const[o,...i]=t;return-1===i[0]?e[o]:tt(null!==(n=e[o].subGroups)&&void 0!==n?n:[],i)}function nt(e,t,n,i,r){var l,a;const[s,c]=o.useMemo((()=>[void 0!==t&&"number"===typeof n?Math.floor(t/n):0,void 0!==t&&"number"===typeof n?-t%n:0]),[t,n]),u=o.useMemo((()=>{var e,t;return{x:i.current.x,y:s,width:null!==(e=i.current.width)&&void 0!==e?e:1,height:null!==(t=i.current.height)&&void 0!==t?t:1,ty:c}}),[i,c,s]),[d,h,f]=(0,k.ig)(u),p=o.useRef(r);p.current=r;const v=function(e,t){const[n]=o.useState((()=>({value:e,callback:t,facade:{get current(){return n.value},set current(e){const t=n.value;t!==e&&(n.value=e,n.callback(e,t))}}})));return n.callback=t,n.facade}(null,(n=>{null!==n&&void 0!==t?n.scrollTop=t:null!==n&&void 0!==e&&(n.scrollLeft=e)})),g=(null!==(l=d.height)&&void 0!==l?l:1)>1;o.useLayoutEffect((()=>{if(void 0!==t&&null!==v.current&&g){if(v.current.scrollTop===t)return;v.current.scrollTop=t,v.current.scrollTop!==t&&f(),p.current()}}),[t,g,f,v]);const m=(null!==(a=d.width)&&void 0!==a?a:1)>1;return o.useLayoutEffect((()=>{if(void 0!==e&&null!==v.current&&m){if(v.current.scrollLeft===e)return;v.current.scrollLeft=e,v.current.scrollLeft!==e&&f(),p.current()}}),[e,m,f,v]),{visibleRegion:d,setVisibleRegion:h,scrollRef:v}}const ot=o.lazy((async()=>await n.e(9865).then(n.bind(n,19865))));let it=0;function rt(e,t){return void 0===e||0===t||0===e.columns.length&&void 0===e.current?e:{current:void 0===e.current?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map((e=>({...e,x:e.x+t})))},rows:e.rows,columns:e.columns.offset(t)}}const lt={kind:u.p6.Loading,allowOverlay:!1},at={columns:u.EV.empty(),rows:u.EV.empty(),current:void 0},st=o.forwardRef(((e,t)=>{var n,h,f,p,v,g,m,w,b,y,x;const[S,M]=o.useState(at),[R,E]=o.useState(),I=o.useRef(null),O=o.useRef(null),[H,z]=o.useState(),L=o.useRef(),F="undefined"===typeof window?null:window,{imageEditorOverride:A,getRowThemeOverride:V,markdownDivCreateNode:_,width:N,height:B,columns:Z,rows:W,getCellContent:j,onCellClicked:U,onCellActivated:X,onFillPattern:G,onFinishedEditing:Y,coercePasteValue:K,drawHeader:$,drawCell:q,editorBloom:Q,onHeaderClicked:J,onColumnProposeMove:ee,rangeSelectionColumnSpanning:te=!0,spanRangeBehavior:ne="default",onGroupHeaderClicked:oe,onCellContextMenu:ie,className:re,onHeaderContextMenu:le,getCellsForSelection:ae,onGroupHeaderContextMenu:se,onGroupHeaderRenamed:fe,onCellEdited:ve,onCellsEdited:ge,onSearchResultsChanged:me,searchResults:we,onSearchValueChange:be,searchValue:ye,onKeyDown:xe,onKeyUp:ke,keybindings:Ce,editOnType:Se=!0,onRowAppended:Me,onColumnMoved:Re,validateCell:Ie,highlightRegions:Te,rangeSelect:ze="rect",columnSelect:Le="multi",rowSelect:Fe="multi",rangeSelectionBlending:Ae="exclusive",columnSelectionBlending:Ve="exclusive",rowSelectionBlending:_e="exclusive",onDelete:Be,onDragStart:Ue,onMouseMove:Xe,onPaste:Ke,copyHeaders:qe=!1,freezeColumns:st=0,cellActivationBehavior:ct="second-click",rowSelectionMode:ut="auto",onHeaderMenuClick:dt,onHeaderIndicatorClick:ht,getGroupDetails:ft,rowGrouping:pt,onSearchClose:vt,onItemHovered:gt,onSelectionCleared:mt,showSearch:wt,onVisibleRegionChanged:bt,gridSelection:yt,onGridSelectionChange:xt,minColumnWidth:kt=50,maxColumnWidth:Ct=500,maxColumnAutoWidth:St,provideEditor:Mt,trailingRowOptions:Rt,freezeTrailingRows:Et=0,allowedFillDirections:It="orthogonal",scrollOffsetX:Tt,scrollOffsetY:Ot,verticalBorder:Pt,onDragOverCell:Dt,onDrop:Ht,onColumnResize:zt,onColumnResizeEnd:Lt,onColumnResizeStart:Ft,customRenderers:At,fillHandle:Vt,experimental:_t,fixedShadowX:Nt,fixedShadowY:Bt,headerIcons:Zt,imageWindowLoader:Wt,initialSize:jt,isDraggable:Ut,onDragLeave:Xt,onRowMoved:Gt,overscrollX:Yt,overscrollY:Kt,preventDiagonalScrolling:$t,rightElement:qt,rightElementProps:Qt,trapFocus:Jt=!1,smoothScrollX:en,smoothScrollY:tn,scaleToRem:nn=!1,rowHeight:on=34,headerHeight:rn=36,groupHeaderHeight:ln=rn,theme:an,isOutsideClick:sn,renderers:cn,resizeIndicator:un,scrollToActiveCell:dn=!0,drawFocusRing:hn=!0}=e,fn="no-editor"===hn?void 0===R:hn,pn="string"===typeof e.rowMarkers?void 0:e.rowMarkers,vn=null!==(n=null!==(h=null===pn||void 0===pn?void 0:pn.kind)&&void 0!==h?h:e.rowMarkers)&&void 0!==n?n:"none",gn=null!==(f=null===pn||void 0===pn?void 0:pn.width)&&void 0!==f?f:e.rowMarkerWidth,mn=null!==(p=null!==(v=null===pn||void 0===pn?void 0:pn.startIndex)&&void 0!==v?v:e.rowMarkerStartIndex)&&void 0!==p?p:1,wn=null!==(g=null===pn||void 0===pn?void 0:pn.theme)&&void 0!==g?g:e.rowMarkerTheme,bn=null===pn||void 0===pn?void 0:pn.headerTheme,yn=null===pn||void 0===pn?void 0:pn.headerAlwaysVisible,xn="multi"!==Fe,kn=null!==(m=null===pn||void 0===pn?void 0:pn.checkboxStyle)&&void 0!==m?m:"square",Cn=Math.max(kt,20),Sn=Math.max(Ct,Cn),Mn=Math.max(null!==St&&void 0!==St?St:Sn,Cn),Rn=o.useMemo((()=>"undefined"===typeof window?{fontSize:"16px"}:window.getComputedStyle(document.documentElement)),[]),{rows:En,rowNumberMapper:In,rowHeight:Tn,getRowThemeOverride:On}=function(e,t,n,i){const r=o.useMemo((()=>void 0===e?void 0:Qe(e,t)),[e,t]),l=o.useMemo((()=>void 0===r?t:r.reduce(((e,t)=>e+(t.isCollapsed?1:t.rows+1)),0)),[r,t]),a=o.useMemo((()=>void 0===e||"number"===typeof n&&e.height===n?n:t=>{const{isGroupHeader:o}=Je(t,r);return o?e.height:"number"===typeof n?n:n(t)}),[r,e,n]),s=o.useCallback((e=>{if(void 0===r)return e;let t=e;for(const n of r){if(0===t)return;if(t--,!n.isCollapsed){if(t<n.rows)return n.contentIndex+t;t-=n.rows}}return e}),[r]),c=(0,k.qJ)(null!==i&&void 0!==i?i:null===e||void 0===e?void 0:e.themeOverride,o.useCallback((t=>{if(void 0===e)return null===i||void 0===i?void 0:i(t,t,t);if(void 0===i&&void 0===(null===e||void 0===e?void 0:e.themeOverride))return;const{isGroupHeader:n,contentIndex:o,groupIndex:l}=Je(t,r);return n?e.themeOverride:null===i||void 0===i?void 0:i(t,l,o)}),[r,i,e]));return void 0===e?{rowHeight:a,rows:t,rowNumberMapper:s,getRowThemeOverride:c}:{rowHeight:a,rows:l,rowNumberMapper:s,getRowThemeOverride:c}}(pt,W,on,V),Pn=o.useMemo((()=>Number.parseFloat(Rn.fontSize)),[Rn]),{rowHeight:Dn,headerHeight:Hn,groupHeaderHeight:zn,theme:Ln,overscrollX:Fn,overscrollY:An}=function(e){let{rowHeight:t,headerHeight:n,groupHeaderHeight:i,theme:r,overscrollX:l,overscrollY:a,scaleToRem:s,remSize:c}=e;const[u,d,h,f,p,v]=o.useMemo((()=>{var e,o,u;if(!s||16===c)return[t,n,i,r,l,a];const d=c/16,h=t,f=(0,T.Zu)();return["number"===typeof h?h*d:e=>Math.ceil(h(e)*d),Math.ceil(n*d),Math.ceil(i*d),{...r,headerIconSize:(null!==(e=null===r||void 0===r?void 0:r.headerIconSize)&&void 0!==e?e:f.headerIconSize)*d,cellHorizontalPadding:(null!==(o=null===r||void 0===r?void 0:r.cellHorizontalPadding)&&void 0!==o?o:f.cellHorizontalPadding)*d,cellVerticalPadding:(null!==(u=null===r||void 0===r?void 0:r.cellVerticalPadding)&&void 0!==u?u:f.cellVerticalPadding)*d},Math.ceil((null!==l&&void 0!==l?l:0)*d),Math.ceil((null!==a&&void 0!==a?a:0)*d)]}),[i,n,l,a,c,t,s,r]);return{rowHeight:u,headerHeight:d,groupHeaderHeight:h,theme:f,overscrollX:p,overscrollY:v}}({groupHeaderHeight:ln,headerHeight:rn,overscrollX:Yt,overscrollY:Kt,remSize:Pn,rowHeight:Tn,scaleToRem:nn,theme:an}),Vn=function(e){const t=(0,k.vE)(e);return o.useMemo((()=>{var e,n,o,i,r,l,a,s,c,u,d,h;if(void 0===t)return $e(Ye);const f={...t,goToNextPage:null!==(e=null!==(n=null===t||void 0===t?void 0:t.goToNextPage)&&void 0!==n?n:null===t||void 0===t?void 0:t.pageDown)&&void 0!==e?e:Ye.goToNextPage,goToPreviousPage:null!==(o=null!==(i=null===t||void 0===t?void 0:t.goToPreviousPage)&&void 0!==i?i:null===t||void 0===t?void 0:t.pageUp)&&void 0!==o?o:Ye.goToPreviousPage,goToFirstCell:null!==(r=null!==(l=null===t||void 0===t?void 0:t.goToFirstCell)&&void 0!==l?l:null===t||void 0===t?void 0:t.first)&&void 0!==r?r:Ye.goToFirstCell,goToLastCell:null!==(a=null!==(s=null===t||void 0===t?void 0:t.goToLastCell)&&void 0!==s?s:null===t||void 0===t?void 0:t.last)&&void 0!==a?a:Ye.goToLastCell,selectToFirstCell:null!==(c=null!==(u=null===t||void 0===t?void 0:t.selectToFirstCell)&&void 0!==u?u:null===t||void 0===t?void 0:t.first)&&void 0!==c?c:Ye.selectToFirstCell,selectToLastCell:null!==(d=null!==(h=null===t||void 0===t?void 0:t.selectToLastCell)&&void 0!==h?h:null===t||void 0===t?void 0:t.last)&&void 0!==d?d:Ye.selectToLastCell};return $e({...Ye,...f})}),[t])}(Ce),_n=null!==gn&&void 0!==gn?gn:W>1e4?48:W>1e3?44:W>100?36:32,Nn="none"!==vn,Bn=Nn?1:0,Zn=void 0!==Me,Wn=!0===(null===Rt||void 0===Rt?void 0:Rt.sticky),[jn,Un]=o.useState(!1),Xn=null!==wt&&void 0!==wt?wt:jn,Gn=o.useCallback((()=>{void 0!==vt?vt():Un(!1)}),[vt]),Yn=o.useMemo((()=>void 0===yt?void 0:rt(yt,Bn)),[yt,Bn]),Kn=null!==Yn&&void 0!==Yn?Yn:S,$n=o.useRef();void 0===$n.current&&($n.current=new AbortController),o.useEffect((()=>()=>null===$n||void 0===$n?void 0:$n.current.abort()),[]);const[qn,Qn]=function(e,t,n,i,r){const l=o.useCallback((n=>{var o;if(!0===e){const e=[];for(let o=n.y;o<n.y+n.height;o++){const i=[];for(let e=n.x;e<n.x+n.width;e++)e<0||o>=r?i.push({kind:u.p6.Loading,allowOverlay:!1}):i.push(t([e,o]));e.push(i)}return e}return null!==(o=null===e||void 0===e?void 0:e(n,i.signal))&&void 0!==o?o:[]}),[i.signal,t,e,r]),a=void 0!==e?l:void 0,s=o.useCallback((e=>{if(void 0===a)return[];const t={...e,x:e.x-n};if(t.x<0){t.x=0,t.width--;const e=a(t,i.signal);return"function"===typeof e?async()=>(await e()).map((e=>[{kind:u.p6.Loading,allowOverlay:!1},...e])):e.map((e=>[{kind:u.p6.Loading,allowOverlay:!1},...e]))}return a(t,i.signal)}),[i.signal,a,n]);return[void 0!==e?s:void 0,a]}(ae,j,Bn,$n.current,En),Jn=o.useCallback(((e,t,n)=>{if(void 0===Ie)return!0;const o=[e[0]-Bn,e[1]];return null===Ie||void 0===Ie?void 0:Ie(o,t,n)}),[Bn,Ie]),eo=o.useRef(yt),to=o.useCallback(((e,t)=>{t&&(e=function(e,t,n,o,i){const r=e;if("allowPartial"===o||void 0===e.current||void 0===t)return e;let l=!1;do{var a,s;if(void 0===(null===(a=e)||void 0===a?void 0:a.current))break;const o=null===(s=e.current)||void 0===s?void 0:s.range,u=[];if(o.width>2){const e=t({x:o.x,y:o.y,width:1,height:o.height},i.signal);if("function"===typeof e)return r;u.push(...e);const n=t({x:o.x+o.width-1,y:o.y,width:1,height:o.height},i.signal);if("function"===typeof n)return r;u.push(...n)}else{const e=t({x:o.x,y:o.y,width:o.width,height:o.height},i.signal);if("function"===typeof e)return r;u.push(...e)}let d=o.x-n,h=o.x+o.width-1-n;for(const e of u)for(const t of e)void 0!==t.span&&(d=Math.min(t.span[0],d),h=Math.max(t.span[1],h));var c;d===o.x-n&&h===o.x+o.width-1-n?l=!0:e={current:{cell:null!==(c=e.current.cell)&&void 0!==c?c:[0,0],range:{x:d+n,y:o.y,width:h-d+1,height:o.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!l);return e}(e,qn,Bn,ne,$n.current)),void 0!==xt?(eo.current=rt(e,-Bn),xt(eo.current)):M(e)}),[xt,qn,Bn,ne]),no=(0,k.qJ)(zt,o.useCallback(((e,t,n,o)=>{null===zt||void 0===zt||zt(Z[n-Bn],t,n-Bn,o)}),[zt,Bn,Z])),oo=(0,k.qJ)(Lt,o.useCallback(((e,t,n,o)=>{null===Lt||void 0===Lt||Lt(Z[n-Bn],t,n-Bn,o)}),[Lt,Bn,Z])),io=(0,k.qJ)(Ft,o.useCallback(((e,t,n,o)=>{null===Ft||void 0===Ft||Ft(Z[n-Bn],t,n-Bn,o)}),[Ft,Bn,Z])),ro=(0,k.qJ)($,o.useCallback(((e,t)=>{var n;return null!==(n=null===$||void 0===$?void 0:$({...e,columnIndex:e.columnIndex-Bn},t))&&void 0!==n&&n}),[$,Bn])),lo=(0,k.qJ)(q,o.useCallback(((e,t)=>{var n;return null!==(n=null===q||void 0===q?void 0:q({...e,col:e.col-Bn},t))&&void 0!==n&&n}),[q,Bn])),ao=o.useCallback((e=>{if(void 0!==Be){const t=Be(rt(e,-Bn));return"boolean"===typeof t?t:rt(t,Bn)}return!0}),[Be,Bn]),[so,co,uo]=function(e,t,n,i,r,l,a){return[o.useCallback(((o,s,c,d)=>{var h,f;"cell"!==l&&"multi-cell"!==l||void 0===o||(o={...o,range:{x:o.cell[0],y:o.cell[1],width:1,height:1}}),!a&&void 0!==o&&o.range.width>1&&(o={...o,range:{...o.range,width:1,x:o.cell[0]}});const p="mixed"===n&&(c||"drag"===d),v="mixed"===i&&p,g="mixed"===r&&p;let m={current:void 0===o?void 0:{...o,rangeStack:"drag"===d&&null!==(h=null===(f=e.current)||void 0===f?void 0:f.rangeStack)&&void 0!==h?h:[]},columns:v?e.columns:u.EV.empty(),rows:g?e.rows:u.EV.empty()};c&&("multi-rect"===l||"multi-cell"===l)&&void 0!==m.current&&void 0!==e.current&&(m={...m,current:{...m.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(m,s)}),[i,e,n,l,a,r,t]),o.useCallback(((o,l,a)=>{var s;let c;if(o=null!==(s=o)&&void 0!==s?s:e.rows,void 0!==l&&(o=o.add(l)),"exclusive"===r&&o.length>0)c={current:void 0,columns:u.EV.empty(),rows:o};else{const t=a&&"mixed"===i;c={current:a&&"mixed"===n?e.current:void 0,columns:t?e.columns:u.EV.empty(),rows:o}}t(c,!1)}),[i,e,n,r,t]),o.useCallback(((o,l,a)=>{var s;let c;if(o=null!==(s=o)&&void 0!==s?s:e.columns,void 0!==l&&(o=o.add(l)),"exclusive"===i&&o.length>0)c={current:void 0,rows:u.EV.empty(),columns:o};else{const t=a&&"mixed"===r;c={current:a&&"mixed"===n?e.current:void 0,rows:t?e.rows:u.EV.empty(),columns:o}}t(c,!1)}),[i,e,n,r,t])]}(Kn,to,Ae,Ve,_e,ze,te),ho=o.useMemo((()=>(0,T.yR)((0,T.Zu)(),Ln)),[Ln]),[fo,po]=o.useState([0,0,0]),vo=o.useMemo((()=>{if(void 0===cn)return{};const e={};for(const t of cn)e[t.kind]=t;return e}),[cn]),go=o.useCallback((e=>e.kind!==u.p6.Custom?vo[e.kind]:null===At||void 0===At?void 0:At.find((t=>t.isMatch(e)))),[At,vo]);let{sizedColumns:mo,nonGrowWidth:wo}=function(e,t,n,i,r,l,a,s,c){const d=o.useRef(t),h=o.useRef(n),f=o.useRef(a);d.current=t,h.current=n,f.current=a;const[p,v]=o.useMemo((()=>{if("undefined"===typeof window)return[null,null];const e=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",[e,e.getContext("2d",{alpha:!1})]}),[]);o.useLayoutEffect((()=>(p&&document.documentElement.append(p),()=>{null===p||void 0===p||p.remove()})),[p]);const g=o.useRef({}),m=o.useRef(),[w,b]=o.useState();return o.useLayoutEffect((()=>{const t=h.current;if(void 0===t||e.every(u.Sq))return;let n=Math.max(1,10-Math.floor(e.length/1e4)),o=0;n<d.current&&n>1&&(n--,o=1);const i={x:0,y:0,width:e.length,height:Math.min(d.current,n)},r={x:0,y:d.current-1,width:e.length,height:1};(async()=>{const n=t(i,c.signal),l=o>0?t(r,c.signal):void 0;let a;a="object"===typeof n?n:await(0,u.rL)(n),void 0!==l&&(a="object"===typeof l?[...a,...l]:[...a,...await(0,u.rL)(l)]),m.current=e,b(a)})()}),[c.signal,e]),o.useMemo((()=>{let t=e.every(u.Sq)?e:null===v?e.map((e=>(0,u.Sq)(e)?e:{...e,width:Pe})):(v.font=f.current.baseFontFull,e.map(((t,n)=>{if((0,u.Sq)(t))return t;if(void 0!==g.current[t.id])return{...t,width:g.current[t.id]};if(void 0===w||m.current!==e||void 0===t.id)return{...t,width:Pe};const o=De(v,a,t,n,w,r,l,!0,s);return g.current[t.id]=o.width,o}))),n=0,o=0;const c=[];for(const[e,i]of t.entries())n+=i.width,void 0!==i.grow&&i.grow>0&&(o+=i.grow,c.push(e));if(n<i&&c.length>0){const e=[...t],r=i-n;let l=r;for(let n=0;n<c.length;n++){var d;const i=c[n],a=(null!==(d=t[i].grow)&&void 0!==d?d:0)/o,s=n===c.length-1?l:Math.min(l,Math.floor(r*a));e[i]={...t[i],growOffset:s,width:t[i].width+s},l-=s}t=e}return{sizedColumns:t,nonGrowWidth:n}}),[i,e,v,w,a,r,l,s])}(Z,En,Qn,fo[0]-(0===Bn?0:_n)-fo[2],Cn,Mn,ho,go,$n.current);"none"!==vn&&(wo+=_n);const bo=o.useMemo((()=>mo.some((e=>void 0!==e.group))),[mo]),yo=bo?Hn+zn:Hn,xo=Kn.rows.length,ko="none"===vn?void 0:0!==xo&&(xo===En||void 0),Co=o.useMemo((()=>"none"===vn?mo:[{title:"",width:_n,icon:void 0,hasMenu:!1,style:"normal",themeOverride:wn,rowMarker:kn,rowMarkerChecked:ko,headerRowMarkerTheme:bn,headerRowMarkerAlwaysVisible:yn,headerRowMarkerDisabled:xn},...mo]),[vn,mo,_n,wn,kn,ko,bn,yn,xn]),So=o.useRef({height:1,width:1,x:0,y:0}),Mo=o.useRef(!1),{setVisibleRegion:Ro,visibleRegion:Eo,scrollRef:Io}=nt(Tt,Ot,Dn,So,(()=>Mo.current=!0));So.current=Eo;const To=Eo.x+Bn,Oo=Eo.y,Po=o.useRef(null),Do=o.useCallback((e=>{var t;!0===e?null===(t=Po.current)||void 0===t||t.focus():window.requestAnimationFrame((()=>{var e;null===(e=Po.current)||void 0===e||e.focus()}))}),[]),Ho=Zn?En+1:En,zo=o.useCallback((e=>{const t=0===Bn?e:e.map((e=>({...e,location:[e.location[0]-Bn,e.location[1]]}))),n=null===ge||void 0===ge?void 0:ge(t);if(!0!==n)for(const o of t)null===ve||void 0===ve||ve(o.location,o.value);return n}),[ve,ge,Bn]),[Lo,Fo]=o.useState(),Ao=void 0!==Kn.current&&Kn.current.range.width*Kn.current.range.height>1?Kn.current.range:void 0,Vo=fn?null===(w=Kn.current)||void 0===w?void 0:w.cell:void 0,_o=null===Vo||void 0===Vo?void 0:Vo[0],No=null===Vo||void 0===Vo?void 0:Vo[1],Bo=o.useMemo((()=>{var e,t;if((void 0===Te||0===Te.length)&&void 0===(null!==(e=null!==(t=null!==Ao&&void 0!==Ao?Ao:_o)&&void 0!==t?t:No)&&void 0!==e?e:Lo))return;const n=[];if(void 0!==Te)for(const o of Te){const e=Co.length-o.range.x-Bn;e>0&&n.push({color:o.color,range:{...o.range,x:o.range.x+Bn,width:Math.min(e,o.range.width)},style:o.style})}return void 0!==Lo&&n.push({color:(0,C.fG)(ho.accentColor,0),range:Lo,style:"dashed"}),void 0!==Ao&&n.push({color:(0,C.fG)(ho.accentColor,.5),range:Ao,style:"solid-outline"}),void 0!==_o&&void 0!==No&&n.push({color:ho.accentColor,range:{x:_o,y:No,width:1,height:1},style:"solid-outline"}),n.length>0?n:void 0}),[Lo,Ao,_o,No,Te,Co.length,ho.accentColor,Bn]),Zo=o.useRef(Co);Zo.current=Co;const Wo=o.useCallback((function(e){let[t,n]=e,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=Zn&&n===Ho-1;if(0===t&&Nn){if(i)return lt;const e=In(n);return void 0===e?lt:{kind:u.$o.Marker,allowOverlay:!1,checkboxStyle:kn,checked:!0===(null===Kn||void 0===Kn?void 0:Kn.rows.hasIndex(n)),markerKind:"clickable-number"===vn?"number":vn,row:mn+e,drawHandle:void 0!==Gt,cursor:"clickable-number"===vn?"pointer":void 0}}if(i){var r,l;const e=t===Bn&&null!==(r=null===Rt||void 0===Rt?void 0:Rt.hint)&&void 0!==r?r:"",n=Zo.current[t];if(!0===(null===n||void 0===n||null===(l=n.trailingRowOptions)||void 0===l?void 0:l.disabled))return lt;{var a,s,c,d;const t=null!==(a=null===n||void 0===n||null===(s=n.trailingRowOptions)||void 0===s?void 0:s.hint)&&void 0!==a?a:e,o=null!==(c=null===n||void 0===n||null===(d=n.trailingRowOptions)||void 0===d?void 0:d.addIcon)&&void 0!==c?c:null===Rt||void 0===Rt?void 0:Rt.addIcon;return{kind:u.$o.NewRow,hint:t,allowOverlay:!1,icon:o}}}{const e=t-Bn;if(o||!0===(null===_t||void 0===_t?void 0:_t.strict)){var h,f,p,v;const t=So.current,o=t.x>e||e>t.x+t.width||t.y>n||n>t.y+t.height||n>=qo.current,i=e===(null===(h=t.extras)||void 0===h||null===(f=h.selected)||void 0===f?void 0:f[0])&&n===(null===(p=t.extras)||void 0===p?void 0:p.selected[1]);let r=!1;if(void 0!==(null===(v=t.extras)||void 0===v?void 0:v.freezeRegions))for(const l of t.extras.freezeRegions)if(P(l,e,n)){r=!0;break}if(o&&!i&&!r)return lt}let i=j([e,n]);return 0!==Bn&&void 0!==i.span&&(i={...i,span:[i.span[0]+Bn,i.span[1]+Bn]}),i}}),[Zn,Ho,Nn,In,kn,null===Kn||void 0===Kn?void 0:Kn.rows,vn,mn,Gt,Bn,null===Rt||void 0===Rt?void 0:Rt.hint,null===Rt||void 0===Rt?void 0:Rt.addIcon,null===_t||void 0===_t?void 0:_t.strict,j]),jo=o.useCallback((e=>{var t;let n=null!==(t=null===ft||void 0===ft?void 0:ft(e))&&void 0!==t?t:{name:e};var o;void 0!==fe&&""!==e&&(n={icon:n.icon,name:n.name,overrideTheme:n.overrideTheme,actions:[...null!==(o=n.actions)&&void 0!==o?o:[],{title:"Rename",icon:"renameIcon",onClick:e=>hi({group:n.name,bounds:e.bounds})}]});return n}),[ft,fe]),Uo=o.useCallback((e=>{var t;const[n,o]=e.cell,i=Co[n],r=void 0!==(null===i||void 0===i?void 0:i.group)?null===(t=jo(i.group))||void 0===t?void 0:t.overrideTheme:void 0,l=null===i||void 0===i?void 0:i.themeOverride,a=null===On||void 0===On?void 0:On(o);E({...e,theme:(0,T.yR)(ho,r,l,a,e.content.themeOverride)})}),[On,Co,jo,ho]),Xo=o.useCallback(((e,t,n)=>{if(void 0===Kn.current)return;const[o,r]=Kn.current.cell,l=Wo([o,r]);if(l.kind!==u.p6.Boolean&&l.allowOverlay){let t=l;if(void 0!==n)switch(t.kind){case u.p6.Number:{const e=(0,i.wY)((()=>"-"===n?-0:Number.parseFloat(n)),0);t={...t,data:Number.isNaN(e)?0:e};break}case u.p6.Text:case u.p6.Markdown:case u.p6.Uri:t={...t,data:n}}Uo({target:e,content:t,initialValue:n,cell:[o,r],highlight:void 0===n,forceEditMode:void 0!==n})}else if(l.kind===u.p6.Boolean&&t&&!0!==l.readonly){var a;zo([{location:Kn.current.cell,value:{...l,data:je(l.data)}}]),null===(a=Po.current)||void 0===a||a.damage([{cell:Kn.current.cell}])}}),[Wo,Kn,zo,Uo]),Go=o.useCallback(((e,t)=>{var n;const o=null===(n=Po.current)||void 0===n?void 0:n.getBounds(e,t);if(void 0===o||null===Io.current)return;const i=Wo([e,t]);i.allowOverlay&&Uo({target:o,content:i,initialValue:void 0,highlight:!0,cell:[e,t],forceEditMode:!0})}),[Wo,Io,Uo]),Yo=o.useCallback((function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"both",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;if(null!==Io.current){const a=Po.current,s=O.current,c="number"!==typeof e?"cell"===e.unit?e.amount:void 0:e,u="number"!==typeof t?"cell"===t.unit?t.amount:void 0:t,h="number"!==typeof e&&"px"===e.unit?e.amount:void 0,f="number"!==typeof t&&"px"===t.unit?t.amount:void 0;if(null!==a&&null!==s){let p={x:0,y:0,width:0,height:0},v=0,g=0;var l;if(void 0!==c||void 0!==u)if(p=null!==(l=a.getBounds((null!==c&&void 0!==c?c:0)+Bn,null!==u&&void 0!==u?u:0))&&void 0!==l?l:p,0===p.width||0===p.height)return;const m=s.getBoundingClientRect(),w=m.width/s.offsetWidth;if(void 0!==h&&(p={...p,x:h-m.left-Io.current.scrollLeft,width:1}),void 0!==f&&(p={...p,y:f+m.top-Io.current.scrollTop,height:1}),void 0!==p){const l={x:p.x-o,y:p.y-i,width:p.width+2*o,height:p.height+2*i};let a=0;for(let e=0;e<st;e++)a+=mo[e].width;let s=0;const c=Et+(Wn?1:0);c>0&&(s=(0,d.YN)(Ho,c,Dn));let u=a*w+m.left+Bn*_n*w,h=m.right,f=m.top+yo*w,b=m.bottom-s*w;const y=p.width+2*o;switch(null===r||void 0===r?void 0:r.hAlign){case"start":h=u+y;break;case"end":u=h-y;break;case"center":u=Math.floor((u+h)/2)-y/2,h=u+y}const x=p.height+2*i;switch(null===r||void 0===r?void 0:r.vAlign){case"start":b=f+x;break;case"end":f=b-x;break;case"center":f=Math.floor((f+b)/2)-x/2,b=f+x}u>l.x?v=l.x-u:h<l.x+l.width&&(v=l.x+l.width-h),f>l.y?g=l.y-f:b<l.y+l.height&&(g=l.y+l.height-b),"vertical"===n||"number"===typeof e&&e<st?v=0:("horizontal"===n||"number"===typeof t&&t>=Ho-c)&&(g=0),0===v&&0===g||(1!==w&&(v/=w,g/=w),Io.current.scrollTo(v+Io.current.scrollLeft,g+Io.current.scrollTop))}}}}),[Bn,Et,_n,Io,yo,st,mo,Ho,Wn,Dn]),Ko=o.useRef(Go),$o=o.useRef(j),qo=o.useRef(En);Ko.current=Go,$o.current=j,qo.current=En;const Qo=o.useCallback((async function(e){var t;let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const o=Co[e];if(!0===(null===o||void 0===o||null===(t=o.trailingRowOptions)||void 0===t?void 0:t.disabled))return;const i=null===Me||void 0===Me?void 0:Me();let r,l=!0;void 0!==i&&(r=await i,"top"===r&&(l=!1),"number"===typeof r&&(l=!1));let a=0;const s=()=>{if(qo.current<=En)return a<500&&window.setTimeout(s,a),void(a=50+2*a);const t="number"===typeof r?r:l?En:0;Qi.current(e-Bn,t),so({cell:[e,t],range:{x:e,y:t,width:1,height:1}},!1,!1,"edit");const o=$o.current([e-Bn,t]);o.allowOverlay&&(0,u.Qo)(o)&&!0!==o.readonly&&n&&window.setTimeout((()=>{Ko.current(e,t)}),0)};s()}),[Co,Me,Bn,En,so]),Jo=o.useCallback((e=>{var t,n,o;const i=null!==(t=null===(n=mo[e])||void 0===n||null===(o=n.trailingRowOptions)||void 0===o?void 0:o.targetColumn)&&void 0!==t?t:null===Rt||void 0===Rt?void 0:Rt.targetColumn;if("number"===typeof i){return i+(Nn?1:0)}if("object"===typeof i){const e=Z.indexOf(i);if(e>=0){return e+(Nn?1:0)}}}),[mo,Z,Nn,null===Rt||void 0===Rt?void 0:Rt.targetColumn]),ei=o.useRef(),ti=o.useRef(),ni=o.useCallback(((e,t)=>{var n;const[o,i]=t;return(0,T.yR)(ho,null===(n=Co[o])||void 0===n?void 0:n.themeOverride,null===On||void 0===On?void 0:On(i),e.themeOverride)}),[On,Co,ho]),{mapper:oi}=function(e,t){const n=o.useMemo((()=>void 0===e?void 0:Qe(e,t)),[e,t]);return{getRowGroupingForPath:tt,updateRowGroupingByPath:et,mapper:o.useCallback((e=>{if("number"===typeof e)return Je(e,n);const t=Je(e[1],n);return{...t,originalIndex:[e[0],t.originalIndex]}}),[n])}}(pt,W),ii=null===pt||void 0===pt?void 0:pt.navigationBehavior,ri=o.useCallback((e=>{var t,n;const o=ce.value?e.metaKey:e.ctrlKey,r=o&&"multi"===Fe,l=o&&"multi"===Le,[a,s]=e.location,c=Kn.columns,d=Kn.rows,[h,f]=null!==(t=null===(n=Kn.current)||void 0===n?void 0:n.cell)&&void 0!==t?t:[];if("cell"===e.kind){if(ti.current=void 0,ai.current=[a,s],0===a&&Nn){if(!0===Zn&&s===En||"number"===vn||"none"===Fe)return;const t=Wo(e.location);if(t.kind!==u.$o.Marker)return;if(void 0!==Gt){var p;const n=go(t);(0,i.hu)((null===n||void 0===n?void 0:n.kind)===u.$o.Marker);const o=null===n||void 0===n||null===(p=n.onClick)||void 0===p?void 0:p.call(n,{...e,cell:t,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,theme:ni(t,e.location),preventDefault:()=>{}});if(void 0===o||o.checked===t.checked)return}E(void 0),Do();const n=d.hasIndex(s),l=ei.current;if("multi"===Fe&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==l&&d.hasIndex(l)){const e=[Math.min(l,s),Math.max(l,s)+1];r||"multi"===ut?co(void 0,e,!0):co(u.EV.fromSingleSelection(e),void 0,r)}else"multi"===Fe&&(r||e.isTouch||"multi"===ut)?n?co(d.remove(s),void 0,!0):(co(void 0,s,!0),ei.current=s):n&&1===d.length?co(u.EV.empty(),void 0,o):(co(u.EV.fromSingleSelection(s),void 0,o),ei.current=s)}else if(a>=Bn&&Zn&&s===En){const e=Jo(a);Qo(null!==e&&void 0!==e?e:a)}else if(h!==a||f!==s){var v;const t=Wo(e.location),n=go(t);if(void 0!==(null===n||void 0===n?void 0:n.onSelect)){let o=!1;if(n.onSelect({...e,cell:t,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,preventDefault:()=>o=!0,theme:ni(t,e.location)}),o)return}if("block"===ii&&oi(s).isGroupHeader)return;const i=Wn&&s===En,r=Wn&&void 0!==Kn&&(null===(v=Kn.current)||void 0===v?void 0:v.cell[1])===En;if(!e.shiftKey&&!0!==e.isLongTouch||void 0===h||void 0===f||void 0===Kn.current||r)so({cell:[a,s],range:{x:a,y:s,width:1,height:1}},!0,o,"click"),ei.current=void 0,E(void 0),Do();else{if(i)return;const e=Math.min(a,h),t=Math.max(a,h),n=Math.min(s,f),r=Math.max(s,f);so({...Kn.current,range:{x:e,y:n,width:t-e+1,height:r-n+1}},!0,o,"click"),ei.current=void 0,Do()}}}else if("header"===e.kind)if(ai.current=[a,s],E(void 0),Nn&&0===a)ei.current=void 0,ti.current=void 0,"multi"===Fe&&(d.length!==En?co(u.EV.fromSingleSelection([0,En]),void 0,o):co(u.EV.empty(),void 0,o),Do());else{const t=ti.current;if("multi"===Le&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==t&&c.hasIndex(t)){const e=[Math.min(t,a),Math.max(t,a)+1];l?uo(void 0,e,o):uo(u.EV.fromSingleSelection(e),void 0,o)}else l?(c.hasIndex(a)?uo(c.remove(a),void 0,o):uo(void 0,a,o),ti.current=a):"none"!==Le&&(uo(u.EV.fromSingleSelection(a),void 0,o),ti.current=a);ei.current=void 0,Do()}else e.kind===de?ai.current=[a,s]:e.kind!==he||e.isMaybeScrollbar||(to(at,!1),E(void 0),Do(),null===mt||void 0===mt||mt(),ei.current=void 0,ti.current=void 0)}),[Fe,Le,Kn,Nn,Bn,Zn,En,vn,Wo,Gt,Do,ut,go,ni,co,Jo,Qo,ii,oi,Wn,so,uo,to,mt]),li=o.useRef(!1),ai=o.useRef(),si=o.useRef(Eo),ci=o.useRef(),ui=o.useCallback((e=>{if(pi.current=!1,si.current=So.current,0!==e.button&&1!==e.button)return void(ci.current=void 0);const t=performance.now();ci.current={button:e.button,time:t,location:e.location},"header"===(null===e||void 0===e?void 0:e.kind)&&(li.current=!0);const n="cell"===e.kind&&e.isFillHandle;!n&&"cell"!==e.kind&&e.isEdge||(z({previousSelection:Kn,fillHandle:n}),ai.current=void 0,e.isTouch||0!==e.button||n?e.isTouch||1!==e.button||(ai.current=e.location):ri(e))}),[Kn,ri]),[di,hi]=o.useState(),fi=o.useCallback((e=>{if(e.kind!==de||"multi"!==Le)return;const t=ce.value?e.metaKey:e.ctrlKey,[n]=e.location,o=Kn.columns;if(n<Bn)return;const i=Co[n];let r=n,l=n;for(let a=n-1;a>=Bn&&(0,d.PU)(i.group,Co[a].group);a--)r--;for(let a=n+1;a<Co.length&&(0,d.PU)(i.group,Co[a].group);a++)l++;if(Do(),t)if(o.hasAll([r,l+1])){let e=o;for(let t=r;t<=l;t++)e=e.remove(t);uo(e,void 0,t)}else uo(void 0,[r,l+1],t);else uo(u.EV.fromSingleSelection([r,l+1]),void 0,t)}),[Le,Do,Kn.columns,Co,Bn,uo]),pi=o.useRef(!1),vi=o.useCallback((async e=>{if(void 0!==qn&&void 0!==no){const t=So.current.y,n=So.current.height;let o=qn({x:e,y:t,width:1,height:Math.min(n,En-t)},$n.current.signal);"object"!==typeof o&&(o=await o());const i=mo[e-Bn],r=document.createElement("canvas").getContext("2d",{alpha:!1});if(null!==r){r.font=ho.baseFontFull;const t=De(r,ho,i,0,o,Cn,Sn,!1,go);null===no||void 0===no||no(i,t.width,e,t.width)}}}),[mo,qn,Sn,ho,Cn,no,Bn,En,go]),[gi,mi]=o.useState(),wi=o.useCallback((async(e,t)=>{var n,o;const i=null===(n=e.current)||void 0===n?void 0:n.range;if(void 0===i||void 0===qn||void 0===t.current)return;const r=t.current.range;if(void 0!==G){let e=!1;if(G({fillDestination:{...r,x:r.x-Bn},patternSource:{...i,x:i.x-Bn},preventDefault:()=>e=!0}),e)return}let l=qn(i,$n.current.signal);"object"!==typeof l&&(l=await l());const a=l,s=[];for(let c=0;c<r.width;c++)for(let e=0;e<r.height;e++){const t=[r.x+c,r.y+e];if((0,d.X4)(t,i))continue;const n=a[e%i.height][c%i.width];!(0,u.rs)(n)&&(0,u.Qo)(n)&&s.push({location:t,value:{...n}})}zo(s),null===(o=Po.current)||void 0===o||o.damage(s.map((e=>({cell:e.location}))))}),[qn,zo,G,Bn]),bi=o.useCallback((()=>{if(void 0===Kn.current||Kn.current.range.width<=1)return;const e={...Kn,current:{...Kn.current,range:{...Kn.current.range,width:1}}};wi(e,Kn)}),[wi,Kn]),yi=o.useCallback((()=>{if(void 0===Kn.current||Kn.current.range.height<=1)return;const e={...Kn,current:{...Kn.current,range:{...Kn.current.range,height:1}}};wi(e,Kn)}),[wi,Kn]),xi=o.useCallback(((e,t)=>{var n,o;const i=H;if(z(void 0),Fo(void 0),mi(void 0),li.current=!1,t)return;if(!0===(null===i||void 0===i?void 0:i.fillHandle)&&void 0!==Kn.current&&void 0!==(null===(n=i.previousSelection)||void 0===n?void 0:n.current)){if(void 0===Lo)return;const e={...Kn,current:{...Kn.current,range:D(i.previousSelection.current.range,Lo)}};return wi(i.previousSelection,e),void to(e,!0)}const[r,l]=e.location,[a,s]=null!==(o=ai.current)&&void 0!==o?o:[],c=()=>{pi.current=!0},h=t=>{const n=t.isTouch||a===r&&s===l;if(n&&(null===U||void 0===U||U([r-Bn,l],{...t,preventDefault:c})),1===t.button)return!pi.current;if(!pi.current){var o;const a=Wo(e.location),s=go(a);if(void 0!==s&&void 0!==s.onClick&&n){const n=s.onClick({...t,cell:a,posX:t.localEventX,posY:t.localEventY,bounds:t.bounds,theme:ni(a,e.location),preventDefault:c});var d;if(void 0!==n&&!(0,u.rs)(n)&&(0,u.T9)(n))zo([{location:t.location,value:n}]),null===(d=Po.current)||void 0===d||d.damage([{cell:t.location}])}if(pi.current||void 0===Kn.current)return!1;let p=!1;switch(null!==(o=a.activationBehaviorOverride)&&void 0!==o?o:ct){case"double-click":case"second-click":{var h,f;if(void 0===(null===i||void 0===i||null===(h=i.previousSelection)||void 0===h||null===(f=h.current)||void 0===f?void 0:f.cell))break;const[e,n]=Kn.current.cell,[o,a]=i.previousSelection.current.cell;p=r===e&&r===o&&l===n&&l===a&&(!0===t.isDoubleClick||"second-click"===ct);break}case"single-click":p=!0}if(p)return null===X||void 0===X||X([r-Bn,l]),Xo(t.bounds,!1),!0}return!1},f=e.location[0]-Bn;if(e.isTouch){const t=So.current,n=si.current;if(t.x!==n.x||t.y!==n.y)return;if(!0===e.isLongTouch){var p;if("cell"===e.kind&&(0,d.pU)(null===(p=Kn.current)||void 0===p?void 0:p.cell,e.location))return void(null===ie||void 0===ie||ie([f,e.location[1]],{...e,preventDefault:c}));if("header"===e.kind&&Kn.columns.hasIndex(r))return void(null===le||void 0===le||le(f,{...e,preventDefault:c}));if(e.kind===de){if(f<0)return;return void(null===se||void 0===se||se(f,{...e,preventDefault:c}))}}"cell"===e.kind?h(e)||ri(e):e.kind===de?null===oe||void 0===oe||oe(f,{...e,preventDefault:c}):(e.kind===ue&&(null===J||void 0===J||J(f,{...e,preventDefault:c})),ri(e))}else{if("header"===e.kind){if(f<0)return;e.isEdge?!0===e.isDoubleClick&&vi(r):0===e.button&&r===a&&l===s&&(null===J||void 0===J||J(f,{...e,preventDefault:c}))}if(e.kind===de){if(f<0)return;0===e.button&&r===a&&l===s&&(null===oe||void 0===oe||oe(f,{...e,preventDefault:c}),pi.current||fi(e))}"cell"!==e.kind||0!==e.button&&1!==e.button||h(e),ai.current=void 0}}),[H,Kn,Bn,Lo,wi,to,U,Wo,go,ct,ni,zo,X,Xo,ie,le,se,ri,oe,J,vi,fi]),ki=o.useCallback((e=>{const t={...e,location:[e.location[0]-Bn,e.location[1]]};null===Xe||void 0===Xe||Xe(t),void 0!==H&&0===e.buttons&&(z(void 0),Fo(void 0),mi(void 0),li.current=!1),mi((t=>{var n,o;return li.current?[e.scrollEdge[0],0]:e.scrollEdge[0]===(null===t||void 0===t?void 0:t[0])&&e.scrollEdge[1]===t[1]?t:void 0===H||(null!==(n=null===(o=ci.current)||void 0===o?void 0:o.location[0])&&void 0!==n?n:0)<Bn?void 0:e.scrollEdge}))}),[H,Xe,Bn]),Ci=o.useCallback(((e,t)=>{null===dt||void 0===dt||dt(e-Bn,t)}),[dt,Bn]),Si=o.useCallback(((e,t)=>{null===ht||void 0===ht||ht(e-Bn,t)}),[ht,Bn]),Mi=null===Kn||void 0===Kn||null===(b=Kn.current)||void 0===b?void 0:b.cell,Ri=o.useCallback(((e,t,n,o,i,r)=>{Mo.current=!1;let l=Mi;void 0!==l&&(l=[l[0]-Bn,l[1]]);const a=0===st?void 0:{x:0,y:e.y,width:st,height:e.height},s=[];void 0!==a&&s.push(a),Et>0&&(s.push({x:e.x-Bn,y:En-Et,width:e.width,height:Et}),st>0&&s.push({x:0,y:En-Et,width:st,height:Et}));const c={x:e.x-Bn,y:e.y,width:e.width,height:Zn&&e.y+e.height>=En?e.height-1:e.height,tx:i,ty:r,extras:{selected:l,freezeRegion:a,freezeRegions:s}};So.current=c,Ro(c),po([t,n,o]),null===bt||void 0===bt||bt(c,c.tx,c.ty,c.extras)}),[Mi,Bn,Zn,En,st,Et,Ro,bt]),Ei=(0,k.qJ)(Re,o.useCallback(((e,t)=>{null===Re||void 0===Re||Re(e-Bn,t-Bn),"none"!==Le&&uo(u.EV.fromSingleSelection(t),void 0,!0)}),[Le,Re,Bn,uo])),Ii=o.useRef(!1),Ti=o.useCallback((e=>{0===e.location[0]&&Bn>0?e.preventDefault():(null===Ue||void 0===Ue||Ue({...e,location:[e.location[0]-Bn,e.location[1]]}),e.defaultPrevented()||(Ii.current=!0),z(void 0))}),[Ue,Bn]),Oi=o.useCallback((()=>{Ii.current=!1}),[]),Pi=null===pt||void 0===pt?void 0:pt.selectionBehavior,Di=o.useCallback((e=>{if("block-spanning"!==Pi)return;const{isGroupHeader:t,path:n,groupRows:o}=oi(e);if(t)return[e,e];const i=n[n.length-1];return[e-i,e+o-i-1]}),[oi,Pi]),Hi=o.useRef(),zi=o.useCallback((e=>{var t,n;if(!pe(e,Hi.current)&&(Hi.current=e,!(void 0!==(null===ci||void 0===ci||null===(t=ci.current)||void 0===t?void 0:t.button)&&ci.current.button>=1))){if(0!==e.buttons&&void 0!==H&&0===(null===(n=ci.current)||void 0===n?void 0:n.location[0])&&0===e.location[0]&&1===Bn&&"multi"===Fe&&H.previousSelection&&!H.previousSelection.rows.hasIndex(ci.current.location[1])&&Kn.rows.hasIndex(ci.current.location[1])){const t=Math.min(ci.current.location[1],e.location[1]),n=Math.max(ci.current.location[1],e.location[1])+1;co(u.EV.fromSingleSelection([t,n]),void 0,!1)}if(0!==e.buttons&&void 0!==H&&void 0!==Kn.current&&!Ii.current&&!li.current&&("rect"===ze||"multi-rect"===ze)){var o;const[t,n]=Kn.current.cell;let[i,l]=e.location;if(l<0&&(l=So.current.y),!0===H.fillHandle&&void 0!==(null===(o=H.previousSelection)||void 0===o?void 0:o.current)){const e=H.previousSelection.current.range;l=Math.min(l,Zn?En-1:En);const t=function(e,t,n,o){if("any"===o)return D(e,{x:t,y:n,width:1,height:1});if("vertical"===o&&(t=e.x),"horizontal"===o&&(n=e.y),(0,d.X4)([t,n],e))return;const i=t-e.x,r=e.x+e.width-t,l=n-e.y+1,a=e.y+e.height-n,s=Math.min("vertical"===o?Number.MAX_SAFE_INTEGER:i,"vertical"===o?Number.MAX_SAFE_INTEGER:r,"horizontal"===o?Number.MAX_SAFE_INTEGER:l,"horizontal"===o?Number.MAX_SAFE_INTEGER:a);return s===a?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:s===l?{x:e.x,y:n,width:e.width,height:e.y-n}:s===r?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}(e,i,l,It);Fo(t)}else{if(Zn&&n===En)return;if(Zn&&l===En){if(e.kind!==he)return;l--}i=Math.max(i,Bn);const o=Di(n);l=void 0===o?l:r(l,o[0],o[1]);const a=i-t,s=l-n,c={x:a>=0?t:i,y:s>=0?n:l,width:Math.abs(a)+1,height:Math.abs(s)+1};so({...Kn.current,range:c},!0,!1,"drag")}}null===gt||void 0===gt||gt({...e,location:[e.location[0]-Bn,e.location[1]]})}}),[H,Bn,Fe,Kn,ze,gt,co,Zn,En,It,Di,so]),Li=o.useCallback((()=>{const e=Hi.current;if(void 0===e)return;const[t,n]=e.scrollEdge;let[o,i]=e.location;const l=So.current;var a,s,c;-1===t?o=null!==(a=null===(s=l.extras)||void 0===s||null===(c=s.freezeRegion)||void 0===c?void 0:c.x)&&void 0!==a?a:l.x:1===t&&(o=l.x+l.width);-1===n?i=Math.max(0,l.y):1===n&&(i=Math.min(En-1,l.y+l.height)),o=r(o,0,Co.length-1),i=r(i,0,En-1),zi({...e,location:[o,i]})}),[Co.length,zi,En]);!function(e,t,n){const i=o.useRef(0),[r,l]=null!==e&&void 0!==e?e:[0,0];o.useEffect((()=>{if(0===r&&0===l)return void(i.current=0);let e=!1,o=0;const a=s=>{if(!e){if(0===o)o=s;else{var c;const e=s-o;i.current=Math.min(1,i.current+e/1300);const a=i.current**1.618*e*2;null===(c=t.current)||void 0===c||c.scrollBy(r*a,l*a),o=s,null===n||void 0===n||n()}window.requestAnimationFrame(a)}};return window.requestAnimationFrame(a),()=>{e=!0}}),[t,r,l,n])}(gi,Io,Li);const Fi=o.useCallback((e=>{var t;if(void 0===Kn.current)return;const[n,o]=e,[r,c]=Kn.current.cell,u=Kn.current.range;let d=u.x,h=u.x+u.width,f=u.y,p=u.y+u.height;const[v,g]=null!==(t=Di(c))&&void 0!==t?t:[0,En-1],m=g+1;if(0!==o)switch(o){case 2:p=m,f=c,Yo(0,p,"vertical");break;case-2:f=v,p=c+1,Yo(0,f,"vertical");break;case 1:f<c?(f++,Yo(0,f,"vertical")):(p=Math.min(m,p+1),Yo(0,p,"vertical"));break;case-1:p>c+1?(p--,Yo(0,p,"vertical")):(f=Math.max(v,f-1),Yo(0,f,"vertical"));break;default:(0,i.vE)(o)}if(0!==n)if(2===n)h=Co.length,d=r,Yo(h-1-Bn,0,"horizontal");else if(-2===n)d=Bn,h=r+1,Yo(d-Bn,0,"horizontal");else{let e=[];if(void 0!==qn){const t=qn({x:d,y:f,width:h-d-Bn,height:p-f},$n.current.signal);"object"===typeof t&&(e=function(e){return l(a(a(e).filter((e=>void 0!==e.span)).map((e=>{var t,n,o,i;return s((null!==(t=null===(n=e.span)||void 0===n?void 0:n[0])&&void 0!==t?t:0)+1,(null!==(o=null===(i=e.span)||void 0===i?void 0:i[1])&&void 0!==o?o:0)+1)}))))}(t))}if(1===n){let t=!1;if(d<r){if(e.length>0){const n=s(d+1,r+1).find((t=>!e.includes(t-Bn)));void 0!==n&&(d=n,t=!0)}else d++,t=!0;t&&Yo(d,0,"horizontal")}t||(h=Math.min(Co.length,h+1),Yo(h-1-Bn,0,"horizontal"))}else if(-1===n){let t=!1;if(h>r+1){if(e.length>0){const n=s(h-1,r,-1).find((t=>!e.includes(t-Bn)));void 0!==n&&(h=n,t=!0)}else h--,t=!0;t&&Yo(h-Bn,0,"horizontal")}t||(d=Math.max(Bn,d-1),Yo(d-Bn,0,"horizontal"))}else(0,i.vE)(n)}so({cell:Kn.current.cell,range:{x:d,y:f,width:h-d,height:p-f}},!0,!1,"keyboard-select")}),[qn,Di,Kn,Co.length,Bn,En,Yo,so]),Ai=o.useRef(dn);Ai.current=dn;const Vi=o.useCallback(((e,t,n,o)=>{const i=Ho-(n?0:1);e=r(e,Bn,mo.length-1+Bn),t=r(t,0,i);const l=null===Mi||void 0===Mi?void 0:Mi[0],a=null===Mi||void 0===Mi?void 0:Mi[1];if(e===l&&t===a)return!1;if(o&&void 0!==Kn.current){const n=[...Kn.current.rangeStack];(Kn.current.range.width>1||Kn.current.range.height>1)&&n.push(Kn.current.range),to({...Kn,current:{cell:[e,t],range:{x:e,y:t,width:1,height:1},rangeStack:n}},!0)}else so({cell:[e,t],range:{x:e,y:t,width:1,height:1}},!0,!1,"keyboard-nav");return void 0!==L.current&&L.current[0]===e&&L.current[1]===t&&(L.current=void 0),Ai.current&&Yo(e-Bn,t),!0}),[Ho,Bn,mo.length,Mi,Kn,Yo,to,so]),_i=o.useCallback(((e,t)=>{void 0!==(null===R||void 0===R?void 0:R.cell)&&void 0!==e&&(0,u.T9)(e)&&(zo([{location:R.cell,value:e}]),window.requestAnimationFrame((()=>{var e;null===(e=Po.current)||void 0===e||e.damage([{cell:R.cell}])}))),Do(!0),E(void 0);const[n,o]=t;if(void 0!==Kn.current&&(0!==n||0!==o)){const t=Kn.current.cell[1]===Ho-1&&void 0!==e;Vi(r(Kn.current.cell[0]+n,0,Co.length-1),r(Kn.current.cell[1]+o,0,Ho-1),t,!1)}null===Y||void 0===Y||Y(e,t)}),[null===R||void 0===R?void 0:R.cell,Do,Kn,Y,zo,Ho,Vi,Co.length]),Ni=o.useMemo((()=>"gdg-overlay-".concat(it++)),[]),Bi=o.useCallback((e=>{var t;Do();const n=[];for(let l=e.x;l<e.x+e.width;l++)for(let t=e.y;t<e.y+e.height;t++){const e=j([l-Bn,t]);if(!e.allowOverlay&&e.kind!==u.p6.Boolean)continue;let a;if(e.kind===u.p6.Custom){var o;const t=go(e),n=null===t||void 0===t||null===(o=t.provideEditor)||void 0===o?void 0:o.call(t,e);if(void 0!==(null===t||void 0===t?void 0:t.onDelete))a=t.onDelete(e);else if((0,u.DP)(n)){var i;a=null===n||void 0===n||null===(i=n.deletedValue)||void 0===i?void 0:i.call(n,e)}}else if((0,u.T9)(e)&&e.allowOverlay||e.kind===u.p6.Boolean){var r;const t=go(e);a=null===t||void 0===t||null===(r=t.onDelete)||void 0===r?void 0:r.call(t,e)}void 0!==a&&!(0,u.rs)(a)&&(0,u.T9)(a)&&n.push({location:[l,t],value:a})}zo(n),null===(t=Po.current)||void 0===t||t.damage(n.map((e=>({cell:e.location}))))}),[Do,j,go,zo,Bn]),Zi=void 0!==R,Wi=o.useCallback((e=>{const t=()=>{e.stopPropagation(),e.preventDefault()},n={didMatch:!1},{bounds:o}=e,i=Kn.columns,r=Kn.rows,l=Vn;if(!Zi&&He(l.clear,e,n))to(at,!1),null===mt||void 0===mt||mt();else if(!Zi&&He(l.selectAll,e,n)){var a,s;to({columns:u.EV.empty(),rows:u.EV.empty(),current:{cell:null!==(a=null===(s=Kn.current)||void 0===s?void 0:s.cell)&&void 0!==a?a:[Bn,0],range:{x:Bn,y:0,width:Z.length,height:En},rangeStack:[]}},!1)}else if(He(l.search,e,n)){var c;null===I||void 0===I||null===(c=I.current)||void 0===c||c.focus({preventScroll:!0}),Un(!0)}else if(He(l.delete,e,n)){var d;const e=null===(d=null===ao||void 0===ao?void 0:ao(Kn))||void 0===d||d;if(!1!==e){const t=!0===e?Kn:e;if(void 0!==t.current){Bi(t.current.range);for(const e of t.current.rangeStack)Bi(e)}for(const e of t.rows)Bi({x:Bn,y:e,width:Z.length,height:1});for(const e of t.columns)Bi({x:e,y:0,width:1,height:En})}}if(n.didMatch)return t(),!0;if(void 0===Kn.current)return!1;let[h,f]=Kn.current.cell;const[,p]=Kn.current.cell;let v=!1,g=!1;He(l.scrollToSelectedCell,e,n)?Qi.current(h-Bn,f):"none"!==Le&&He(l.selectColumn,e,n)?i.hasIndex(h)?uo(i.remove(h),void 0,!0):"single"===Le?uo(u.EV.fromSingleSelection(h),void 0,!0):uo(void 0,h,!0):"none"!==Fe&&He(l.selectRow,e,n)?r.hasIndex(f)?co(r.remove(f),void 0,!0):"single"===Fe?co(u.EV.fromSingleSelection(f),void 0,!0):co(void 0,f,!0):!Zi&&void 0!==o&&He(l.activateCell,e,n)?f===En&&Zn?window.setTimeout((()=>{const e=Jo(h);Qo(null!==e&&void 0!==e?e:h)}),0):(null===X||void 0===X||X([h-Bn,f]),Xo(o,!0)):Kn.current.range.height>1&&He(l.downFill,e,n)?yi():Kn.current.range.width>1&&He(l.rightFill,e,n)?bi():He(l.goToNextPage,e,n)?f+=Math.max(1,So.current.height-4):He(l.goToPreviousPage,e,n)?f-=Math.max(1,So.current.height-4):He(l.goToFirstCell,e,n)?(E(void 0),f=0,h=0):He(l.goToLastCell,e,n)?(E(void 0),f=Number.MAX_SAFE_INTEGER,h=Number.MAX_SAFE_INTEGER):He(l.selectToFirstCell,e,n)?(E(void 0),Fi([-2,-2])):He(l.selectToLastCell,e,n)?(E(void 0),Fi([2,2])):Zi?(He(l.closeOverlay,e,n)&&E(void 0),He(l.acceptOverlayDown,e,n)&&(E(void 0),f++),He(l.acceptOverlayUp,e,n)&&(E(void 0),f--),He(l.acceptOverlayLeft,e,n)&&(E(void 0),h--),He(l.acceptOverlayRight,e,n)&&(E(void 0),h++)):(He(l.goDownCell,e,n)?f+=1:He(l.goUpCell,e,n)?f-=1:He(l.goRightCell,e,n)?h+=1:He(l.goLeftCell,e,n)?h-=1:He(l.goDownCellRetainSelection,e,n)?(f+=1,v=!0):He(l.goUpCellRetainSelection,e,n)?(f-=1,v=!0):He(l.goRightCellRetainSelection,e,n)?(h+=1,v=!0):He(l.goLeftCellRetainSelection,e,n)?(h-=1,v=!0):He(l.goToLastRow,e,n)?f=En-1:He(l.goToFirstRow,e,n)?f=Number.MIN_SAFE_INTEGER:He(l.goToLastColumn,e,n)?h=Number.MAX_SAFE_INTEGER:He(l.goToFirstColumn,e,n)?h=Number.MIN_SAFE_INTEGER:"rect"!==ze&&"multi-rect"!==ze||(He(l.selectGrowDown,e,n)?Fi([0,1]):He(l.selectGrowUp,e,n)?Fi([0,-1]):He(l.selectGrowRight,e,n)?Fi([1,0]):He(l.selectGrowLeft,e,n)?Fi([-1,0]):He(l.selectToLastRow,e,n)?Fi([0,2]):He(l.selectToFirstRow,e,n)?Fi([0,-2]):He(l.selectToLastColumn,e,n)?Fi([2,0]):He(l.selectToFirstColumn,e,n)&&Fi([-2,0])),g=n.didMatch);if(void 0!==ii&&"normal"!==ii&&f!==p){const e="skip-down"===ii||"skip"===ii||"block"===ii,t=f<p;if(t&&("skip-up"===ii||"skip"===ii||"block"===ii)){for(;f>=0&&oi(f).isGroupHeader;)f--;f<0&&(f=p)}else if(!t&&e){for(;f<En&&oi(f).isGroupHeader;)f++;f>=En&&(f=p)}}const m=Vi(h,f,!1,v),w=n.didMatch;return w&&(m||!g||Jt)&&t(),w}),[ii,Zi,Kn,Vn,Le,Fe,ze,Bn,oi,En,Vi,to,mt,Z.length,ao,Jt,Bi,uo,co,Zn,Jo,Qo,X,Xo,yi,bi,Fi]),ji=o.useCallback((e=>{let t=!1;if(void 0!==xe&&xe({...e,cancel:()=>{t=!0}}),t)return;if(Wi(e))return;if(void 0===Kn.current)return;const[n,o]=Kn.current.cell,i=So.current;if(Se&&!e.metaKey&&!e.ctrlKey&&void 0!==Kn.current&&1===e.key.length&&/[ -~]/g.test(e.key)&&void 0!==e.bounds&&(0,u.Qo)(j([n-Bn,Math.max(0,Math.min(o,En-1))]))){if((!Zn||o!==En)&&(i.y>o||o>i.y+i.height||i.x>n||n>i.x+i.width))return;Xo(e.bounds,!0,e.key),e.stopPropagation(),e.preventDefault()}}),[Se,xe,Wi,Kn,j,Bn,En,Zn,Xo]),Ui=o.useCallback(((e,t)=>{const n=e.location[0]-Bn;if("header"===e.kind&&(null===le||void 0===le||le(n,{...e,preventDefault:t})),e.kind===de){if(n<0)return;null===se||void 0===se||se(n,{...e,preventDefault:t})}if("cell"===e.kind){const[o,i]=e.location;null===ie||void 0===ie||ie([n,i],{...e,preventDefault:t}),(0,d.pZ)(Kn,e.location)||Vi(o,i,!1,!1)}}),[Kn,ie,se,le,Bn,Vi]),Xi=o.useCallback((async e=>{var t,n;if(!Vn.paste)return;function o(e,t,n,o){var r,l;const a="object"===typeof n?null!==(r=null===n||void 0===n?void 0:n.join("\n"))&&void 0!==r?r:"":null!==(l=null===n||void 0===n?void 0:n.toString())&&void 0!==l?l:"";if(!(0,u.rs)(e)&&(0,u.Qo)(e)&&!0!==e.readonly){const r=null===K||void 0===K?void 0:K(a,e);if(void 0!==r&&(0,u.T9)(r))return{location:t,value:r};const l=go(e);if(void 0===l)return;if(l.kind===u.p6.Custom){var s;(0,i.hu)(e.kind===u.p6.Custom);const n=null===(s=l.onPaste)||void 0===s?void 0:s.call(l,a,e.data);if(void 0===n)return;return{location:t,value:{...e,data:n}}}{var c;const r=null===(c=l.onPaste)||void 0===c?void 0:c.call(l,a,e,{formatted:o,formattedString:"string"===typeof o?o:null===o||void 0===o?void 0:o.join("\n"),rawValue:n});if(void 0===r)return;return(0,i.hu)(r.kind===e.kind),{location:t,value:r}}}}const r=Kn.columns,l=Kn.rows,a=!0===(null===(t=Io.current)||void 0===t?void 0:t.contains(document.activeElement))||!0===(null===(n=O.current)||void 0===n?void 0:n.contains(document.activeElement));let s;if(void 0!==Kn.current)s=[Kn.current.range.x,Kn.current.range.y];else if(1===r.length){var c;s=[null!==(c=r.first())&&void 0!==c?c:0,0]}else if(1===l.length){var d;s=[Bn,null!==(d=l.first())&&void 0!==d?d:0]}if(a&&void 0!==s){var h;let t,n;const i="text/plain",r="text/html";if(void 0!==navigator.clipboard.read){const e=await navigator.clipboard.read();for(const o of e){if(o.types.includes(r)){const e=await o.getType(r),n=Ne(await e.text());if(void 0!==n){t=n;break}}o.types.includes(i)&&(n=await(await o.getType(i)).text())}}else if(void 0!==navigator.clipboard.readText)n=await navigator.clipboard.readText();else{if(void 0===e||null===(null===e||void 0===e?void 0:e.clipboardData))return;if(e.clipboardData.types.includes(r)){t=Ne(e.clipboardData.getData(r))}void 0===t&&e.clipboardData.types.includes(i)&&(n=e.clipboardData.getData(i))}const[l,a]=s,c=[];do{if(void 0===Ke){var f,p,v;const e=o(Wo(s),s,null!==(f=null!==(p=n)&&void 0!==p?p:null===(v=t)||void 0===v?void 0:v.map((e=>e.map((e=>e.rawValue)).join("\t"))).join("\t"))&&void 0!==f?f:"",void 0);void 0!==e&&c.push(e);break}if(void 0===t){if(void 0===n)return;t=Ze(n)}if(!1===Ke||"function"===typeof Ke&&!0!==(null===Ke||void 0===Ke?void 0:Ke([s[0]-Bn,s[1]],t.map((e=>e.map((e=>{var t,n;return null!==(t=null===(n=e.rawValue)||void 0===n?void 0:n.toString())&&void 0!==t?t:""})))))))return;for(const[e,n]of t.entries()){if(e+a>=En)break;for(const[t,i]of n.entries()){const n=[t+l,e+a],[r,s]=n;if(r>=Co.length)continue;if(s>=Ho)continue;const u=o(Wo(n),n,i.rawValue,i.formatted);void 0!==u&&c.push(u)}}}while(0);zo(c),null===(h=Po.current)||void 0===h||h.damage(c.map((e=>({cell:e.location}))))}}),[K,go,Wo,Kn,Vn.paste,Io,Co.length,zo,Ho,Ke,Bn,En]);(0,k.OR)("paste",Xi,F,!1,!0);const Gi=o.useCallback((async(e,t)=>{var n,o;if(!Vn.copy)return;const i=!0===t||!0===(null===(n=Io.current)||void 0===n?void 0:n.contains(document.activeElement))||!0===(null===(o=O.current)||void 0===o?void 0:o.contains(document.activeElement)),r=Kn.columns,l=Kn.rows,a=(t,n)=>{if(qe){We([n.map((e=>({kind:u.p6.Text,data:Z[e].title,displayData:Z[e].title,allowOverlay:!1}))),...t],n,e)}else We(t,n,e)};if(i&&void 0!==qn)if(void 0!==Kn.current){let e=qn(Kn.current.range,$n.current.signal);"object"!==typeof e&&(e=await e()),a(e,s(Kn.current.range.x-Bn,Kn.current.range.x+Kn.current.range.width-Bn))}else if(void 0!==l&&l.length>0){const e=[...l].map((e=>{const t=qn({x:Bn,y:e,width:Z.length,height:1},$n.current.signal);return"object"===typeof t?t[0]:t().then((e=>e[0]))}));if(e.some((e=>e instanceof Promise))){a(await Promise.all(e),s(Z.length))}else a(e,s(Z.length))}else if(r.length>0){const e=[],t=[];for(const n of r){let o=qn({x:n,y:0,width:1,height:En},$n.current.signal);"object"!==typeof o&&(o=await o()),e.push(o),t.push(n-Bn)}if(1===e.length)a(e[0],t);else{a(e.reduce(((e,t)=>e.map(((e,n)=>[...e,...t[n]])))),t)}}}),[Z,qn,Kn,Vn.copy,Bn,Io,En,qe]);(0,k.OR)("copy",Gi,F,!1,!1);const Yi=o.useCallback((async e=>{var t,n;if(!Vn.cut)return;if((!0===(null===(t=Io.current)||void 0===t?void 0:t.contains(document.activeElement))||!0===(null===(n=O.current)||void 0===n?void 0:n.contains(document.activeElement)))&&(await Gi(e),void 0!==Kn.current)){let e={current:{cell:Kn.current.cell,range:Kn.current.range,rangeStack:[]},rows:u.EV.empty(),columns:u.EV.empty()};const t=null===ao||void 0===ao?void 0:ao(e);if(!1===t)return;if(e=!0===t?e:t,void 0===e.current)return;Bi(e.current.range)}}),[Bi,Kn,Vn.cut,Gi,Io,ao]);(0,k.OR)("cut",Yi,F,!1,!1);const Ki=o.useCallback(((e,t)=>{if(void 0!==me)return 0!==Bn&&(e=e.map((e=>[e[0]-Bn,e[1]]))),void me(e,t);if(0===e.length||-1===t)return;const[n,o]=e[t];void 0!==L.current&&L.current[0]===n&&L.current[1]===o||(L.current=[n,o],Vi(n,o,!1,!1))}),[me,Bn,Vi]),[$i,qi]=null!==(y=null===yt||void 0===yt||null===(x=yt.current)||void 0===x?void 0:x.cell)&&void 0!==y?y:[],Qi=o.useRef(Yo);Qi.current=Yo,o.useLayoutEffect((()=>{var e,t,n,o;!Ai.current||Mo.current||void 0===$i||void 0===qi||$i===(null===(e=eo.current)||void 0===e||null===(t=e.current)||void 0===t?void 0:t.cell[0])&&qi===(null===(n=eo.current)||void 0===n||null===(o=n.current)||void 0===o?void 0:o.cell[1])||Qi.current($i,qi),Mo.current=!1}),[$i,qi]);const Ji=void 0!==Kn.current&&(Kn.current.cell[0]>=Co.length||Kn.current.cell[1]>=Ho);o.useLayoutEffect((()=>{Ji&&to(at,!1)}),[Ji,to]);const er=o.useMemo((()=>!0===Zn&&!0===(null===Rt||void 0===Rt?void 0:Rt.tint)?u.EV.fromSingleSelection(Ho-1):u.EV.empty()),[Ho,Zn,null===Rt||void 0===Rt?void 0:Rt.tint]),tr=o.useCallback((e=>{var t;return"boolean"===typeof Pt?Pt:null===(t=null===Pt||void 0===Pt?void 0:Pt(e-Bn))||void 0===t||t}),[Bn,Pt]),nr=o.useMemo((()=>{if(void 0===di||null===O.current)return null;const{bounds:e,group:t}=di,n=O.current.getBoundingClientRect();return o.createElement(Oe,{bounds:e,group:t,canvasBounds:n,onClose:()=>hi(void 0),onFinish:e=>{hi(void 0),null===fe||void 0===fe||fe(t,e)}})}),[fe,di]),or=Math.min(Co.length,st+(Nn?1:0));o.useImperativeHandle(t,(()=>({appendRow:(e,t)=>Qo(e+Bn,t),updateCells:e=>{var t;return 0!==Bn&&(e=e.map((e=>({cell:[e.cell[0]+Bn,e.cell[1]]})))),null===(t=Po.current)||void 0===t?void 0:t.damage(e)},getBounds:(e,t)=>{var n;if(null!==(null===O||void 0===O?void 0:O.current)&&null!==(null===Io||void 0===Io?void 0:Io.current)){if(void 0===e&&void 0===t){const e=O.current.getBoundingClientRect(),t=e.width/Io.current.clientWidth;return{x:e.x-Io.current.scrollLeft*t,y:e.y-Io.current.scrollTop*t,width:Io.current.scrollWidth*t,height:Io.current.scrollHeight*t}}return null===(n=Po.current)||void 0===n?void 0:n.getBounds((null!==e&&void 0!==e?e:0)+Bn,t)}},focus:()=>{var e;return null===(e=Po.current)||void 0===e?void 0:e.focus()},emit:async e=>{switch(e){case"delete":ji({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":ji({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":ji({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await Gi(void 0,!0);break;case"paste":await Xi()}},scrollTo:Yo,remeasureColumns:e=>{for(const t of e)vi(t+Bn)}})),[Qo,vi,Io,Gi,ji,Xi,Bn,Yo]);const[ir,rr]=null!==Mi&&void 0!==Mi?Mi:[],lr=o.useCallback((e=>{const[t,n]=e;-1!==n?ir===t&&rr===n||(so({cell:e,range:{x:t,y:n,width:1,height:1}},!0,!1,"keyboard-nav"),Yo(t,n)):"none"!==Le&&(uo(u.EV.fromSingleSelection(t),void 0,!1),Do())}),[Le,Do,Yo,ir,rr,so,uo]),[ar,sr]=o.useState(!1),cr=o.useRef(c((e=>{sr(e)}),5)),ur=o.useCallback((()=>{cr.current(!0),void 0===Kn.current&&0===Kn.columns.length&&0===Kn.rows.length&&void 0===H&&so({cell:[Bn,Oo],range:{x:Bn,y:Oo,width:1,height:1}},!0,!1,"keyboard-select")}),[Oo,Kn,H,Bn,so]),dr=o.useCallback((()=>{cr.current(!1)}),[]),[hr,fr]=o.useMemo((()=>{var e;let t;const n=null!==(e=null===_t||void 0===_t?void 0:_t.scrollbarWidthOverride)&&void 0!==e?e:(0,k.Iz)(),o=En+(Zn?1:0);if("number"===typeof Dn)t=yo+o*Dn;else{let e=0;const n=Math.min(o,10);for(let t=0;t<n;t++)e+=Dn(t);e=Math.floor(e/n),t=yo+o*e}t+=n;const i=Co.reduce(((e,t)=>t.width+e),0)+n;return["".concat(Math.min(1e5,i),"px"),"".concat(Math.min(1e5,t),"px")]}),[Co,null===_t||void 0===_t?void 0:_t.scrollbarWidthOverride,Dn,En,Zn,yo]),pr=o.useMemo((()=>(0,T.be)(ho)),[ho]);return o.createElement(T.Ni.Provider,{value:ho},o.createElement(Ge,{style:pr,className:re,inWidth:null!==N&&void 0!==N?N:hr,inHeight:null!==B&&void 0!==B?B:fr},o.createElement(Ee,{fillHandle:Vt,drawFocusRing:fn,experimental:_t,fixedShadowX:Nt,fixedShadowY:Bt,getRowThemeOverride:On,headerIcons:Zt,imageWindowLoader:Wt,initialSize:jt,isDraggable:Ut,onDragLeave:Xt,onRowMoved:Gt,overscrollX:Fn,overscrollY:An,preventDiagonalScrolling:$t,rightElement:qt,rightElementProps:Qt,smoothScrollX:en,smoothScrollY:tn,className:re,enableGroups:bo,onCanvasFocused:ur,onCanvasBlur:dr,canvasRef:O,onContextMenu:Ui,theme:ho,cellXOffset:To,cellYOffset:Oo,accessibilityHeight:Eo.height,onDragEnd:Oi,columns:Co,nonGrowWidth:wo,drawHeader:ro,onColumnProposeMove:ee,drawCell:lo,disabledRows:er,freezeColumns:or,lockColumns:Bn,firstColAccessible:0===Bn,getCellContent:Wo,minColumnWidth:Cn,maxColumnWidth:Sn,searchInputRef:I,showSearch:Xn,onSearchClose:Gn,highlightRegions:Bo,getCellsForSelection:qn,getGroupDetails:jo,headerHeight:Hn,isFocused:ar,groupHeaderHeight:bo?zn:0,freezeTrailingRows:Et+(Zn&&!0===(null===Rt||void 0===Rt?void 0:Rt.sticky)?1:0),hasAppendRow:Zn,onColumnResize:no,onColumnResizeEnd:oo,onColumnResizeStart:io,onCellFocused:lr,onColumnMoved:Ei,onDragStart:Ti,onHeaderMenuClick:Ci,onHeaderIndicatorClick:Si,onItemHovered:zi,isFilling:!0===(null===H||void 0===H?void 0:H.fillHandle),onMouseMove:ki,onKeyDown:ji,onKeyUp:ke,onMouseDown:ui,onMouseUp:xi,onDragOverCell:Dt,onDrop:Ht,onSearchResultsChanged:Ki,onVisibleRegionChanged:Ri,clientSize:fo,rowHeight:Dn,searchResults:we,searchValue:ye,onSearchValueChange:be,rows:Ho,scrollRef:Io,selection:Kn,translateX:Eo.tx,translateY:Eo.ty,verticalBorder:tr,gridRef:Po,getCellRenderer:go,resizeIndicator:un}),nr,void 0!==R&&o.createElement(o.Suspense,{fallback:null},o.createElement(ot,{...R,validateCell:Jn,bloom:Q,id:Ni,getCellRenderer:go,className:!0===(null===_t||void 0===_t?void 0:_t.isSubGrid)?"click-outside-ignore":void 0,provideEditor:Mt,imageEditorOverride:A,onFinishEditing:_i,markdownDivCreateNode:_,isOutsideClick:sn}))))}));function ct(e){var t,n;const{cell:o,posX:i,posY:r,bounds:l,theme:a}=e,{width:s,height:c,x:d,y:h}=l,f=null!==(t=o.maxSize)&&void 0!==t?t:20,p=Math.floor(l.y+c/2),v=(0,k.Qo)(f,c,a.cellVerticalPadding),g=(0,k.XC)(null!==(n=o.contentAlign)&&void 0!==n?n:"center",d,s,a.cellHorizontalPadding,v),m=(0,k.kq)(g,p,v),w=(0,k.qq)(d+i,h+r,m);return(0,u.kf)(o)&&w}const ut={getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:"false"},kind:u.p6.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>{var t,n;return function(e,t,n,o,i){if(!n&&t===u.qF)return;const{ctx:r,hoverAmount:l,theme:a,rect:s,highlighted:c,hoverX:d,hoverY:h,cell:{contentAlign:f}}=e,{x:p,y:v,width:g,height:m}=s;let w=!1;if(i>0){let e=n?1-i+i*l:.4;if(t===u.qF&&(e*=l),0===e)return;e<1&&(w=!0,r.globalAlpha=e)}Z(r,a,t,p,v,g,m,c,d,h,o,f),w&&(r.globalAlpha=1)}(e,e.cell.data,(0,u.kf)(e.cell),null!==(t=e.cell.maxSize)&&void 0!==t?t:20,null!==(n=e.cell.hoverEffectIntensity)&&void 0!==n?n:.35)},onDelete:e=>({...e,data:!1}),onSelect:e=>{ct(e)&&e.preventDefault()},onClick:e=>{if(ct(e))return{...e.cell,data:je(e.cell.data)}},onPaste:(e,t)=>{let n=u.qF;return"true"===e.toLowerCase()?n=!0:"false"===e.toLowerCase()?n=!1:"indeterminate"===e.toLowerCase()&&(n=u.sd),n===t.data?void 0:{...t,data:n}}};const dt=(0,we.z)("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),ht=e=>{const{bubbles:t}=e;return o.createElement(dt,null,t.map(((e,t)=>o.createElement("div",{key:t,className:"boe-bubble"},e))),o.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},ft={getAccessibilityString:e=>(0,k.jM)(e.data),kind:u.p6.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce(((t,n)=>e.measureText(n).width+t+20),0)+2*n.cellHorizontalPadding-4,draw:e=>function(e,t){const{rect:n,theme:o,ctx:i,highlighted:r}=e,{x:l,y:a,width:s,height:c}=n,u=20,h=8,f=pt;let p=l+o.cellHorizontalPadding;const v=[];for(const m of t){if(p>l+s)break;const e=(0,d.P7)(m,i,o.baseFontFull).width;v.push({x:p,width:e}),p+=e+2*h+f}i.beginPath();for(const m of v){var g;(0,d.NK)(i,m.x,a+(c-u)/2,m.width+2*h,u,null!==(g=o.roundingRadius)&&void 0!==g?g:u/2)}i.fillStyle=r?o.bgBubbleSelected:o.bgBubble,i.fill();for(const[m,w]of v.entries())i.beginPath(),i.fillStyle=o.textBubble,i.fillText(t[m],w.x+h,a+c/2+(0,d.aX)(i,o))}(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return o.createElement(ht,{bubbles:t.data})},onPaste:()=>{}},pt=4;const vt=(0,we.z)("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),gt=e=>{const{drilldowns:t}=e;return o.createElement(vt,null,t.map(((e,t)=>o.createElement("div",{key:t,className:"doe-bubble"},void 0!==e.img&&o.createElement("img",{src:e.img}),o.createElement("div",null,e.text)))))},mt={getAccessibilityString:e=>(0,k.jM)(e.data.map((e=>e.text))),kind:u.p6.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce(((t,n)=>e.measureText(n.text).width+t+20+(void 0!==n.img?18:0)),0)+2*n.cellHorizontalPadding-4,draw:e=>function(e,t){var n;const{rect:o,theme:i,ctx:r,imageLoader:l,col:a,row:s}=e,{x:c,width:u}=o,h=i.baseFontFull,f=(0,d.WA)(r,h),p=Math.min(o.height,Math.max(16,2*Math.ceil(f*i.lineHeight))),v=Math.floor(o.y+(o.height-p)/2),g=p-10,m=8,w=wt;let b=c+i.cellHorizontalPadding;const y=null!==(n=i.roundingRadius)&&void 0!==n?n:6,x=function(e,t,n,o){const i=Math.ceil(window.devicePixelRatio),r=5,l=n-2*r,a=4,s=n*i,c=o+r,u=3*o,h=(u+2*r)*i,f="".concat(e,",").concat(t,",").concat(i,",").concat(n);if(void 0!==bt[f])return{el:bt[f],height:s,width:h,middleWidth:a*i,sideWidth:c*i,padding:r*i,dpr:i};const p=document.createElement("canvas"),v=p.getContext("2d");return null===v?null:(p.width=h,p.height=s,v.scale(i,i),bt[f]=p,v.beginPath(),(0,d.NK)(v,r,r,u,l,o),v.shadowColor="rgba(24, 25, 34, 0.4)",v.shadowBlur=1,v.fillStyle=e,v.fill(),v.shadowColor="rgba(24, 25, 34, 0.3)",v.shadowOffsetY=1,v.shadowBlur=5,v.fillStyle=e,v.fill(),v.shadowOffsetY=0,v.shadowBlur=0,v.shadowBlur=0,v.beginPath(),(0,d.NK)(v,r+.5,r+.5,u,l,o),v.strokeStyle=t,v.lineWidth=1,v.stroke(),{el:p,height:s,width:h,sideWidth:c*i,middleWidth:o*i,padding:r*i,dpr:i})}(i.bgCell,i.drilldownBorder,p,y),k=[];for(const S of t){if(b>c+u)break;const e=(0,d.P7)(S.text,r,h).width;let t=0;if(void 0!==S.img){void 0!==l.loadOrGetImage(S.img,a,s)&&(t=g-8+4)}const n=e+t+2*m;k.push({x:b,width:n}),b+=n+w}if(null!==x){const{el:e,height:t,middleWidth:n,sideWidth:o,width:i,dpr:l,padding:a}=x,s=o/l,c=a/l;for(const u of k){const l=Math.floor(u.x),a=Math.floor(u.width),d=a-2*(s-c);r.imageSmoothingEnabled=!1,r.drawImage(e,0,0,o,t,l-c,v,s,p),d>0&&r.drawImage(e,o,0,n,t,l+(s-c),v,d,p),r.drawImage(e,i-o,0,o,t,l+a-(s-c),v,s,p),r.imageSmoothingEnabled=!0}}r.beginPath();for(const[S,M]of k.entries()){const e=t[S];let n=M.x+m;if(void 0!==e.img){const t=l.loadOrGetImage(e.img,a,s);if(void 0!==t){var C;const e=g-8;let o=0,l=0,a=t.width,s=t.height;a>s?(o+=(a-s)/2,a=s):s>a&&(l+=(s-a)/2,s=a),r.beginPath(),(0,d.NK)(r,n,v+p/2-e/2,e,e,null!==(C=i.roundingRadius)&&void 0!==C?C:3),r.save(),r.clip(),r.drawImage(t,o,l,a,s,n,v+p/2-e/2,e,e),r.restore(),n+=e+4}}r.beginPath(),r.fillStyle=i.textBubble,r.fillText(e.text,n,v+p/2+(0,d.aX)(r,i))}}(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return o.createElement(gt,{drilldowns:t.data})},onPaste:()=>{}},wt=4,bt={};const yt=(0,we.z)("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var xt=n(44303);const kt=e=>{const{urls:t,canWrite:n,onEditClick:i,renderImage:r}=e,l=t.filter((e=>""!==e));if(0===l.length)return null;const a=l.length>1;return o.createElement(yt,{"data-testid":"GDG-default-image-overlay-editor"},o.createElement(xt.lr,{showArrows:a,showThumbs:!1,swipeable:a,emulateTouch:a,infiniteLoop:a},l.map((e=>{var t;const n=null!==(t=null===r||void 0===r?void 0:r(e))&&void 0!==t?t:o.createElement("img",{draggable:!1,src:e});return o.createElement("div",{className:"gdg-centering-container",key:e},n)}))),n&&i&&o.createElement("button",{className:"gdg-edit-icon",onClick:i},o.createElement(k.Wy,null)))},Ct={getAccessibilityString:e=>e.data.join(", "),kind:u.p6.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>{var t,n,o;return function(e,t,n,o){const{rect:i,col:r,row:l,theme:a,ctx:s,imageLoader:c}=e,{x:u,y:h,height:f,width:p}=i,v=f-2*a.cellVerticalPadding,g=[];let m=0;for(let d=0;d<t.length;d++){const e=t[d];if(0===e.length)continue;const n=c.loadOrGetImage(e,r,l);if(void 0!==n){g[d]=n;m+=n.width*(v/n.height)+St}}if(0===m)return;m-=St;let w=u+a.cellHorizontalPadding;"right"===o?w=Math.floor(u+p-a.cellHorizontalPadding-m):"center"===o&&(w=Math.floor(u+p/2-m/2));for(const b of g){if(void 0===b)continue;const e=b.width*(v/b.height);n>0&&(s.beginPath(),(0,d.NK)(s,w,h+a.cellVerticalPadding,e,v,n),s.save(),s.clip()),s.drawImage(b,w,h+a.cellVerticalPadding,e,v),n>0&&s.restore(),w+=e+St}}(e,null!==(t=e.cell.displayData)&&void 0!==t?t:e.cell.data,null!==(n=null!==(o=e.cell.rounding)&&void 0!==o?o:e.theme.roundingRadius)&&void 0!==n?n:4,e.cell.contentAlign)},measure:(e,t)=>50*t.data.length,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:i}=e,r=null!==i&&void 0!==i?i:kt;return o.createElement(r,{urls:t.data,canWrite:!0!==t.readonly,onCancel:n,onChange:e=>{n({...t,data:[e]})}})},onPaste:(e,t)=>{const n=(e=e.trim()).split(",").map((e=>{try{return new URL(e),e}catch{return}})).filter((e=>void 0!==e));if(n.length!==t.data.length||!n.every(((e,n)=>e===t.data[n])))return{...t,data:n}}},St=4;const Mt={getAccessibilityString:()=>"",kind:u.p6.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{var t,n;const{cell:o,col:i,row:r,ctx:l,rect:a,theme:s}=e;if(void 0===o.skeletonWidth||0===o.skeletonWidth)return;let c=o.skeletonWidth;void 0!==o.skeletonWidthVariability&&o.skeletonWidthVariability>0&&(c+=Math.round(function(e,t){let n=49632*e+325176*t;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}(i,r)*o.skeletonWidthVariability));const u=s.cellHorizontalPadding;c+2*u>=a.width&&(c=a.width-2*u-1);const h=null!==(t=o.skeletonHeight)&&void 0!==t?t:Math.min(18,a.height-2*s.cellVerticalPadding);(0,d.NK)(l,a.x+u,a.y+(a.height-h)/2,c,h,null!==(n=s.roundingRadius)&&void 0!==n?n:3),l.fillStyle=(0,C.fG)(s.textDark,.1),l.fill()},onPaste:()=>{}};function Rt(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Et={async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};const It=/[&<>"']/,Tt=new RegExp(It.source,"g"),Ot=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Pt=new RegExp(Ot.source,"g"),Dt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ht=e=>Dt[e];function zt(e,t){if(t){if(It.test(e))return e.replace(Tt,Ht)}else if(Ot.test(e))return e.replace(Pt,Ht);return e}const Lt=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Ft(e){return e.replace(Lt,((e,t)=>"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const At=/(^|[^\[])\^/g;function Vt(e,t){e="string"===typeof e?e:e.source,t=t||"";const n={replace:(t,o)=>(o=(o=o.source||o).replace(At,"$1"),e=e.replace(t,o),n),getRegex:()=>new RegExp(e,t)};return n}const _t=/[^\w:]/g,Nt=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function Bt(e,t,n){if(e){let e;try{e=decodeURIComponent(Ft(n)).replace(_t,"").toLowerCase()}catch(o){return null}if(0===e.indexOf("javascript:")||0===e.indexOf("vbscript:")||0===e.indexOf("data:"))return null}t&&!Nt.test(n)&&(n=function(e,t){Zt[" "+e]||(Wt.test(e)?Zt[" "+e]=e+"/":Zt[" "+e]=Yt(e,"/",!0));e=Zt[" "+e];const n=-1===e.indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(jt,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(Ut,"$1")+t:e+t}(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(o){return null}return n}const Zt={},Wt=/^[^:]+:\/*[^/]*$/,jt=/^([^:]+:)[\s\S]*$/,Ut=/^([^:]+:\/*[^/]*)[\s\S]*$/;const Xt={exec:function(){}};function Gt(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let o=!1,i=t;for(;--i>=0&&"\\"===n[i];)o=!o;return o?"|":" |"})).split(/ \|/);let o=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;o<n.length;o++)n[o]=n[o].trim().replace(/\\\|/g,"|");return n}function Yt(e,t,n){const o=e.length;if(0===o)return"";let i=0;for(;i<o;){const r=e.charAt(o-i-1);if(r!==t||n){if(r===t||!n)break;i++}else i++}return e.slice(0,o-i)}function Kt(e,t){if(t<1)return"";let n="";for(;t>1;)1&t&&(n+=e),t>>=1,e+=e;return n+e}function $t(e,t,n,o){const i=t.href,r=t.title?zt(t.title):null,l=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){o.state.inLink=!0;const e={type:"link",raw:n,href:i,title:r,text:l,tokens:o.inlineTokens(l)};return o.state.inLink=!1,e}return{type:"image",raw:n,href:i,title:r,text:zt(l)}}class qt{constructor(e){this.options=e||Et}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:Yt(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const o=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=o.length?e.slice(o.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=Yt(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const e=t[0].replace(/^ *>[ \t]?/gm,""),n=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(e);return this.lexer.state.top=n,{type:"blockquote",raw:t[0],tokens:o,text:e}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n,o,i,r,l,a,s,c,u,d,h,f,p=t[1].trim();const v=p.length>1,g={type:"list",raw:"",ordered:v,start:v?+p.slice(0,-1):"",loose:!1,items:[]};p=v?"\\d{1,9}\\".concat(p.slice(-1)):"\\".concat(p),this.options.pedantic&&(p=v?p:"[*+-]");const m=new RegExp("^( {0,3}".concat(p,")((?:[\t ][^\\n]*)?(?:\\n|$))"));for(;e&&(f=!1,t=m.exec(e))&&!this.rules.block.hr.test(e);){if(n=t[0],e=e.substring(n.length),c=t[2].split("\n",1)[0].replace(/^\t+/,(e=>" ".repeat(3*e.length))),u=e.split("\n",1)[0],this.options.pedantic?(r=2,h=c.trimLeft()):(r=t[2].search(/[^ ]/),r=r>4?1:r,h=c.slice(r),r+=t[1].length),a=!1,!c&&/^ *$/.test(u)&&(n+=u+"\n",e=e.substring(u.length+1),f=!0),!f){const t=new RegExp("^ {0,".concat(Math.min(3,r-1),"}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))")),o=new RegExp("^ {0,".concat(Math.min(3,r-1),"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)")),i=new RegExp("^ {0,".concat(Math.min(3,r-1),"}(?:```|~~~)")),l=new RegExp("^ {0,".concat(Math.min(3,r-1),"}#"));for(;e&&(d=e.split("\n",1)[0],u=d,this.options.pedantic&&(u=u.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!i.test(u))&&!l.test(u)&&!t.test(u)&&!o.test(e);){if(u.search(/[^ ]/)>=r||!u.trim())h+="\n"+u.slice(r);else{if(a)break;if(c.search(/[^ ]/)>=4)break;if(i.test(c))break;if(l.test(c))break;if(o.test(c))break;h+="\n"+u}a||u.trim()||(a=!0),n+=d+"\n",e=e.substring(d.length+1),c=u.slice(r)}}g.loose||(s?g.loose=!0:/\n *\n *$/.test(n)&&(s=!0)),this.options.gfm&&(o=/^\[[ xX]\] /.exec(h),o&&(i="[ ] "!==o[0],h=h.replace(/^\[[ xX]\] +/,""))),g.items.push({type:"list_item",raw:n,task:!!o,checked:i,loose:!1,text:h}),g.raw+=n}g.items[g.items.length-1].raw=n.trimRight(),g.items[g.items.length-1].text=h.trimRight(),g.raw=g.raw.trimRight();const w=g.items.length;for(l=0;l<w;l++)if(this.lexer.state.top=!1,g.items[l].tokens=this.lexer.blockTokens(g.items[l].text,[]),!g.loose){const e=g.items[l].tokens.filter((e=>"space"===e.type)),t=e.length>0&&e.some((e=>/\n.*\n/.test(e.raw)));g.loose=t}if(g.loose)for(l=0;l<w;l++)g.items[l].loose=!0;return g}}html(e){const t=this.rules.block.html.exec(e);if(t){const e={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};if(this.options.sanitize){const n=this.options.sanitizer?this.options.sanitizer(t[0]):zt(t[0]);e.type="paragraph",e.text=n,e.tokens=this.lexer.inline(n)}return e}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:o}}}table(e){const t=this.rules.block.table.exec(e);if(t){const e={type:"table",header:Gt(t[1]).map((e=>({text:e}))),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(e.header.length===e.align.length){e.raw=t[0];let n,o,i,r,l=e.align.length;for(n=0;n<l;n++)/^ *-+: *$/.test(e.align[n])?e.align[n]="right":/^ *:-+: *$/.test(e.align[n])?e.align[n]="center":/^ *:-+ *$/.test(e.align[n])?e.align[n]="left":e.align[n]=null;for(l=e.rows.length,n=0;n<l;n++)e.rows[n]=Gt(e.rows[n],e.header.length).map((e=>({text:e})));for(l=e.header.length,o=0;o<l;o++)e.header[o].tokens=this.lexer.inline(e.header[o].text);for(l=e.rows.length,o=0;o<l;o++)for(r=e.rows[o],i=0;i<r.length;i++)r[i].tokens=this.lexer.inline(r[i].text);return e}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:zt(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):zt(t[0]):t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=Yt(e.slice(0,-1),"\\");if((e.length-t.length)%2===0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;const n=e.length;let o=0,i=0;for(;i<n;i++)if("\\"===e[i])i++;else if(e[i]===t[0])o++;else if(e[i]===t[1]&&(o--,o<0))return i;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],o="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],o=e[3])}else o=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),$t(t,{href:n?n.replace(this.rules.inline._escapes,"$1"):n,title:o?o.replace(this.rules.inline._escapes,"$1"):o},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=(n[2]||n[1]).replace(/\s+/g," ");if(e=t[e.toLowerCase()],!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return $t(n,e,n[0],this.lexer)}}emStrong(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=this.rules.inline.emStrong.lDelim.exec(e);if(!o)return;if(o[3]&&n.match(/[\p{L}\p{N}]/u))return;const i=o[1]||o[2]||"";if(!i||i&&(""===n||this.rules.inline.punctuation.exec(n))){const n=o[0].length-1;let i,r,l=n,a=0;const s="*"===o[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+n);null!=(o=s.exec(t));){if(i=o[1]||o[2]||o[3]||o[4]||o[5]||o[6],!i)continue;if(r=i.length,o[3]||o[4]){l+=r;continue}if((o[5]||o[6])&&n%3&&!((n+r)%3)){a+=r;continue}if(l-=r,l>0)continue;r=Math.min(r,r+l+a);const t=e.slice(0,n+o.index+(o[0].length-i.length)+r);if(Math.min(n,r)%2){const e=t.slice(1,-1);return{type:"em",raw:t,text:e,tokens:this.lexer.inlineTokens(e)}}const s=t.slice(2,-2);return{type:"strong",raw:t,text:s,tokens:this.lexer.inlineTokens(s)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),o=/^ /.test(e)&&/ $/.test(e);return n&&o&&(e=e.substring(1,e.length-1)),e=zt(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e,t){const n=this.rules.inline.autolink.exec(e);if(n){let e,o;return"@"===n[2]?(e=zt(this.options.mangle?t(n[1]):n[1]),o="mailto:"+e):(e=zt(n[1]),o=e),{type:"link",raw:n[0],text:e,href:o,tokens:[{type:"text",raw:e,text:e}]}}}url(e,t){let n;if(n=this.rules.inline.url.exec(e)){let e,o;if("@"===n[2])e=zt(this.options.mangle?t(n[0]):n[0]),o="mailto:"+e;else{let t;do{t=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0]}while(t!==n[0]);e=zt(n[0]),o="www."===n[1]?"http://"+n[0]:n[0]}return{type:"link",raw:n[0],text:e,href:o,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e,t){const n=this.rules.inline.text.exec(e);if(n){let e;return e=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):zt(n[0]):n[0]:zt(this.options.smartypants?t(n[0]):n[0]),{type:"text",raw:n[0],text:e}}}}const Qt={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:Xt,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};Qt.def=Vt(Qt.def).replace("label",Qt._label).replace("title",Qt._title).getRegex(),Qt.bullet=/(?:[*+-]|\d{1,9}[.)])/,Qt.listItemStart=Vt(/^( *)(bull) */).replace("bull",Qt.bullet).getRegex(),Qt.list=Vt(Qt.list).replace(/bull/g,Qt.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+Qt.def.source+")").getRegex(),Qt._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Qt._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Qt.html=Vt(Qt.html,"i").replace("comment",Qt._comment).replace("tag",Qt._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Qt.paragraph=Vt(Qt._paragraph).replace("hr",Qt.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Qt._tag).getRegex(),Qt.blockquote=Vt(Qt.blockquote).replace("paragraph",Qt.paragraph).getRegex(),Qt.normal={...Qt},Qt.gfm={...Qt.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"},Qt.gfm.table=Vt(Qt.gfm.table).replace("hr",Qt.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Qt._tag).getRegex(),Qt.gfm.paragraph=Vt(Qt._paragraph).replace("hr",Qt.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",Qt.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Qt._tag).getRegex(),Qt.pedantic={...Qt.normal,html:Vt("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",Qt._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Xt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Vt(Qt.normal._paragraph).replace("hr",Qt.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",Qt.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const Jt={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:Xt,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:Xt,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function en(e){return e.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201c").replace(/"/g,"\u201d").replace(/\.{3}/g,"\u2026")}function tn(e){let t,n,o="";const i=e.length;for(t=0;t<i;t++)n=e.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),o+="&#"+n+";";return o}Jt._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",Jt.punctuation=Vt(Jt.punctuation).replace(/punctuation/g,Jt._punctuation).getRegex(),Jt.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,Jt.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g,Jt._comment=Vt(Qt._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),Jt.emStrong.lDelim=Vt(Jt.emStrong.lDelim).replace(/punct/g,Jt._punctuation).getRegex(),Jt.emStrong.rDelimAst=Vt(Jt.emStrong.rDelimAst,"g").replace(/punct/g,Jt._punctuation).getRegex(),Jt.emStrong.rDelimUnd=Vt(Jt.emStrong.rDelimUnd,"g").replace(/punct/g,Jt._punctuation).getRegex(),Jt._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,Jt._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,Jt._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,Jt.autolink=Vt(Jt.autolink).replace("scheme",Jt._scheme).replace("email",Jt._email).getRegex(),Jt._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,Jt.tag=Vt(Jt.tag).replace("comment",Jt._comment).replace("attribute",Jt._attribute).getRegex(),Jt._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Jt._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,Jt._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,Jt.link=Vt(Jt.link).replace("label",Jt._label).replace("href",Jt._href).replace("title",Jt._title).getRegex(),Jt.reflink=Vt(Jt.reflink).replace("label",Jt._label).replace("ref",Qt._label).getRegex(),Jt.nolink=Vt(Jt.nolink).replace("ref",Qt._label).getRegex(),Jt.reflinkSearch=Vt(Jt.reflinkSearch,"g").replace("reflink",Jt.reflink).replace("nolink",Jt.nolink).getRegex(),Jt.normal={...Jt},Jt.pedantic={...Jt.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Vt(/^!?\[(label)\]\((.*?)\)/).replace("label",Jt._label).getRegex(),reflink:Vt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Jt._label).getRegex()},Jt.gfm={...Jt.normal,escape:Vt(Jt.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Jt.gfm.url=Vt(Jt.gfm.url,"i").replace("email",Jt.gfm._extended_email).getRegex(),Jt.breaks={...Jt.gfm,br:Vt(Jt.br).replace("{2,}","*").getRegex(),text:Vt(Jt.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};class nn{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Et,this.options.tokenizer=this.options.tokenizer||new qt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:Qt.normal,inline:Jt.normal};this.options.pedantic?(t.block=Qt.pedantic,t.inline=Jt.pedantic):this.options.gfm&&(t.block=Qt.gfm,this.options.breaks?t.inline=Jt.breaks:t.inline=Jt.gfm),this.tokenizer.rules=t}static get rules(){return{block:Qt,inline:Jt}}static lex(e,t){return new nn(t).lex(e)}static lexInline(e,t){return new nn(t).inlineTokens(e)}lex(e){let t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e){let t,n,o,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,((e,t,n)=>t+"    ".repeat(n.length)));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((n=>!!(t=n.call({lexer:this},e,r))&&(e=e.substring(t.raw.length),r.push(t),!0)))))if(t=this.tokenizer.space(e))e=e.substring(t.raw.length),1===t.raw.length&&r.length>0?r[r.length-1].raw+="\n":r.push(t);else if(t=this.tokenizer.code(e))e=e.substring(t.raw.length),n=r[r.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?r.push(t):(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.fences(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.heading(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.hr(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.blockquote(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.list(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.html(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.def(e))e=e.substring(t.raw.length),n=r[r.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?this.tokens.links[t.tag]||(this.tokens.links[t.tag]={href:t.href,title:t.title}):(n.raw+="\n"+t.raw,n.text+="\n"+t.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.table(e))e=e.substring(t.raw.length),r.push(t);else if(t=this.tokenizer.lheading(e))e=e.substring(t.raw.length),r.push(t);else{if(o=e,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const n=e.slice(1);let i;this.options.extensions.startBlock.forEach((function(e){i=e.call({lexer:this},n),"number"===typeof i&&i>=0&&(t=Math.min(t,i))})),t<1/0&&t>=0&&(o=e.substring(0,t+1))}if(this.state.top&&(t=this.tokenizer.paragraph(o)))n=r[r.length-1],i&&"paragraph"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):r.push(t),i=o.length!==e.length,e=e.substring(t.raw.length);else if(t=this.tokenizer.text(e))e=e.substring(t.raw.length),n=r[r.length-1],n&&"text"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):r.push(t);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,r}inline(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e){let t,n,o,i,r,l,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(s));)e.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,i.index)+"["+Kt("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(s));)s=s.slice(0,i.index)+"["+Kt("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(i=this.tokenizer.rules.inline.escapedEmSt.exec(s));)s=s.slice(0,i.index+i[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;e;)if(r||(l=""),r=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((n=>!!(t=n.call({lexer:this},e,a))&&(e=e.substring(t.raw.length),a.push(t),!0)))))if(t=this.tokenizer.escape(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.tag(e))e=e.substring(t.raw.length),n=a[a.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(t=this.tokenizer.link(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(t.raw.length),n=a[a.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(t=this.tokenizer.emStrong(e,s,l))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.codespan(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.br(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.del(e))e=e.substring(t.raw.length),a.push(t);else if(t=this.tokenizer.autolink(e,tn))e=e.substring(t.raw.length),a.push(t);else if(this.state.inLink||!(t=this.tokenizer.url(e,tn))){if(o=e,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const n=e.slice(1);let i;this.options.extensions.startInline.forEach((function(e){i=e.call({lexer:this},n),"number"===typeof i&&i>=0&&(t=Math.min(t,i))})),t<1/0&&t>=0&&(o=e.substring(0,t+1))}if(t=this.tokenizer.inlineText(o,en))e=e.substring(t.raw.length),"_"!==t.raw.slice(-1)&&(l=t.raw.slice(-1)),r=!0,n=a[a.length-1],n&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):a.push(t);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}else e=e.substring(t.raw.length),a.push(t);return a}}class on{constructor(e){this.options=e||Et}code(e,t,n){const o=(t||"").match(/\S*/)[0];if(this.options.highlight){const t=this.options.highlight(e,o);null!=t&&t!==e&&(n=!0,e=t)}return e=e.replace(/\n$/,"")+"\n",o?'<pre><code class="'+this.options.langPrefix+zt(o)+'">'+(n?e:zt(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:zt(e,!0))+"</code></pre>\n"}blockquote(e){return"<blockquote>\n".concat(e,"</blockquote>\n")}html(e){return e}heading(e,t,n,o){if(this.options.headerIds){const i=this.options.headerPrefix+o.slug(n);return"<h".concat(t,' id="').concat(i,'">').concat(e,"</h").concat(t,">\n")}return"<h".concat(t,">").concat(e,"</h").concat(t,">\n")}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,t,n){const o=t?"ol":"ul";return"<"+o+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+o+">\n"}listitem(e){return"<li>".concat(e,"</li>\n")}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return"<p>".concat(e,"</p>\n")}table(e,t){return t&&(t="<tbody>".concat(t,"</tbody>")),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"}tablerow(e){return"<tr>\n".concat(e,"</tr>\n")}tablecell(e,t){const n=t.header?"th":"td";return(t.align?"<".concat(n,' align="').concat(t.align,'">'):"<".concat(n,">"))+e+"</".concat(n,">\n")}strong(e){return"<strong>".concat(e,"</strong>")}em(e){return"<em>".concat(e,"</em>")}codespan(e){return"<code>".concat(e,"</code>")}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return"<del>".concat(e,"</del>")}link(e,t,n){if(null===(e=Bt(this.options.sanitize,this.options.baseUrl,e)))return n;let o='<a href="'+e+'"';return t&&(o+=' title="'+t+'"'),o+=">"+n+"</a>",o}image(e,t,n){if(null===(e=Bt(this.options.sanitize,this.options.baseUrl,e)))return n;let o='<img src="'.concat(e,'" alt="').concat(n,'"');return t&&(o+=' title="'.concat(t,'"')),o+=this.options.xhtml?"/>":">",o}text(e){return e}}class rn{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class ln{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let n=e,o=0;if(this.seen.hasOwnProperty(n)){o=this.seen[e];do{o++,n=e+"-"+o}while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=o,this.seen[n]=0),n}slug(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)}}class an{constructor(e){this.options=e||Et,this.options.renderer=this.options.renderer||new on,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new rn,this.slugger=new ln}static parse(e,t){return new an(t).parse(e)}static parseInline(e,t){return new an(t).parseInline(e)}parse(e){let t,n,o,i,r,l,a,s,c,u,d,h,f,p,v,g,m,w,b,y=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],x="";const k=e.length;for(t=0;t<k;t++)if(u=e[t],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[u.type]&&(b=this.options.extensions.renderers[u.type].call({parser:this},u),!1!==b||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)))x+=b||"";else switch(u.type){case"space":continue;case"hr":x+=this.renderer.hr();continue;case"heading":x+=this.renderer.heading(this.parseInline(u.tokens),u.depth,Ft(this.parseInline(u.tokens,this.textRenderer)),this.slugger);continue;case"code":x+=this.renderer.code(u.text,u.lang,u.escaped);continue;case"table":for(s="",a="",i=u.header.length,n=0;n<i;n++)a+=this.renderer.tablecell(this.parseInline(u.header[n].tokens),{header:!0,align:u.align[n]});for(s+=this.renderer.tablerow(a),c="",i=u.rows.length,n=0;n<i;n++){for(l=u.rows[n],a="",r=l.length,o=0;o<r;o++)a+=this.renderer.tablecell(this.parseInline(l[o].tokens),{header:!1,align:u.align[o]});c+=this.renderer.tablerow(a)}x+=this.renderer.table(s,c);continue;case"blockquote":c=this.parse(u.tokens),x+=this.renderer.blockquote(c);continue;case"list":for(d=u.ordered,h=u.start,f=u.loose,i=u.items.length,c="",n=0;n<i;n++)v=u.items[n],g=v.checked,m=v.task,p="",v.task&&(w=this.renderer.checkbox(g),f?v.tokens.length>0&&"paragraph"===v.tokens[0].type?(v.tokens[0].text=w+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&"text"===v.tokens[0].tokens[0].type&&(v.tokens[0].tokens[0].text=w+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:w}):p+=w),p+=this.parse(v.tokens,f),c+=this.renderer.listitem(p,m,g);x+=this.renderer.list(c,d,h);continue;case"html":x+=this.renderer.html(u.text);continue;case"paragraph":x+=this.renderer.paragraph(this.parseInline(u.tokens));continue;case"text":for(c=u.tokens?this.parseInline(u.tokens):u.text;t+1<k&&"text"===e[t+1].type;)u=e[++t],c+="\n"+(u.tokens?this.parseInline(u.tokens):u.text);x+=y?this.renderer.paragraph(c):c;continue;default:{const e='Token with "'+u.type+'" type was not found.';if(this.options.silent)return void console.error(e);throw new Error(e)}}return x}parseInline(e,t){t=t||this.renderer;let n,o,i,r="";const l=e.length;for(n=0;n<l;n++)if(o=e[n],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(i=this.options.extensions.renderers[o.type].call({parser:this},o),!1!==i||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type)))r+=i||"";else switch(o.type){case"escape":case"text":r+=t.text(o.text);break;case"html":r+=t.html(o.text);break;case"link":r+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break;case"image":r+=t.image(o.href,o.title,o.text);break;case"strong":r+=t.strong(this.parseInline(o.tokens,t));break;case"em":r+=t.em(this.parseInline(o.tokens,t));break;case"codespan":r+=t.codespan(o.text);break;case"br":r+=t.br();break;case"del":r+=t.del(this.parseInline(o.tokens,t));break;default:{const e='Token with "'+o.type+'" type was not found.';if(this.options.silent)return void console.error(e);throw new Error(e)}}return r}}class sn{constructor(e){this.options=e||Et}preprocess(e){return e}postprocess(e){return e}}function cn(e,t){return(n,o,i)=>{"function"===typeof o&&(i=o,o=null);const r={...o},l=function(e,t,n){return o=>{if(o.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+zt(o.message+"",!0)+"</pre>";return t?Promise.resolve(e):n?void n(null,e):e}if(t)return Promise.reject(o);if(!n)throw o;n(o)}}((o={...un.defaults,...r}).silent,o.async,i);if("undefined"===typeof n||null===n)return l(new Error("marked(): input parameter is undefined or null"));if("string"!==typeof n)return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(function(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}(o),o.hooks&&(o.hooks.options=o),i){const r=o.highlight;let s;try{o.hooks&&(n=o.hooks.preprocess(n)),s=e(n,o)}catch(a){return l(a)}const c=function(e){let n;if(!e)try{o.walkTokens&&un.walkTokens(s,o.walkTokens),n=t(s,o),o.hooks&&(n=o.hooks.postprocess(n))}catch(a){e=a}return o.highlight=r,e?l(e):i(null,n)};if(!r||r.length<3)return c();if(delete o.highlight,!s.length)return c();let u=0;return un.walkTokens(s,(function(e){"code"===e.type&&(u++,setTimeout((()=>{r(e.text,e.lang,(function(t,n){if(t)return c(t);null!=n&&n!==e.text&&(e.text=n,e.escaped=!0),u--,0===u&&c()}))}),0))})),void(0===u&&c())}if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(n):n).then((t=>e(t,o))).then((e=>o.walkTokens?Promise.all(un.walkTokens(e,o.walkTokens)).then((()=>e)):e)).then((e=>t(e,o))).then((e=>o.hooks?o.hooks.postprocess(e):e)).catch(l);try{o.hooks&&(n=o.hooks.preprocess(n));const i=e(n,o);o.walkTokens&&un.walkTokens(i,o.walkTokens);let r=t(i,o);return o.hooks&&(r=o.hooks.postprocess(r)),r}catch(a){return l(a)}}}function un(e,t,n){return cn(nn.lex,an.parse)(e,t,n)}(0,h.Z)(sn,"passThroughHooks",new Set(["preprocess","postprocess"])),un.options=un.setOptions=function(e){var t;return un.defaults={...un.defaults,...e},t=un.defaults,Et=t,un},un.getDefaults=Rt,un.defaults=Et,un.use=function(){const e=un.defaults.extensions||{renderers:{},childTokens:{}};for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];n.forEach((t=>{const n={...t};if(n.async=un.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach((t=>{if(!t.name)throw new Error("extension name required");if(t.renderer){const n=e.renderers[t.name];e.renderers[t.name]=n?function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];let r=t.renderer.apply(this,o);return!1===r&&(r=n.apply(this,o)),r}:t.renderer}if(t.tokenizer){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");e[t.level]?e[t.level].unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}t.childTokens&&(e.childTokens[t.name]=t.childTokens)})),n.extensions=e),t.renderer){const e=un.defaults.renderer||new on;for(const n in t.renderer){const o=e[n];e[n]=function(){for(var i=arguments.length,r=new Array(i),l=0;l<i;l++)r[l]=arguments[l];let a=t.renderer[n].apply(e,r);return!1===a&&(a=o.apply(e,r)),a}}n.renderer=e}if(t.tokenizer){const e=un.defaults.tokenizer||new qt;for(const n in t.tokenizer){const o=e[n];e[n]=function(){for(var i=arguments.length,r=new Array(i),l=0;l<i;l++)r[l]=arguments[l];let a=t.tokenizer[n].apply(e,r);return!1===a&&(a=o.apply(e,r)),a}}n.tokenizer=e}if(t.hooks){const e=un.defaults.hooks||new sn;for(const n in t.hooks){const o=e[n];sn.passThroughHooks.has(n)?e[n]=i=>{if(un.defaults.async)return Promise.resolve(t.hooks[n].call(e,i)).then((t=>o.call(e,t)));const r=t.hooks[n].call(e,i);return o.call(e,r)}:e[n]=function(){for(var i=arguments.length,r=new Array(i),l=0;l<i;l++)r[l]=arguments[l];let a=t.hooks[n].apply(e,r);return!1===a&&(a=o.apply(e,r)),a}}n.hooks=e}if(t.walkTokens){const e=un.defaults.walkTokens;n.walkTokens=function(n){let o=[];return o.push(t.walkTokens.call(this,n)),e&&(o=o.concat(e.call(this,n))),o}}un.setOptions(n)}))},un.walkTokens=function(e,t){let n=[];for(const o of e)switch(n=n.concat(t.call(un,o)),o.type){case"table":for(const e of o.header)n=n.concat(un.walkTokens(e.tokens,t));for(const e of o.rows)for(const o of e)n=n.concat(un.walkTokens(o.tokens,t));break;case"list":n=n.concat(un.walkTokens(o.items,t));break;default:un.defaults.extensions&&un.defaults.extensions.childTokens&&un.defaults.extensions.childTokens[o.type]?un.defaults.extensions.childTokens[o.type].forEach((function(e){n=n.concat(un.walkTokens(o[e],t))})):o.tokens&&(n=n.concat(un.walkTokens(o.tokens,t)))}return n},un.parseInline=cn(nn.lexInline,an.parseInline),un.Parser=an,un.parser=an.parse,un.Renderer=on,un.TextRenderer=rn,un.Lexer=nn,un.lexer=nn.lex,un.Tokenizer=qt,un.Slugger=ln,un.Hooks=sn,un.parse=un;un.options,un.setOptions,un.use,un.walkTokens,un.parseInline,an.parse,nn.lex;const dn=(0,we.z)("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class hn extends o.PureComponent{constructor(){super(...arguments),(0,h.Z)(this,"targetElement",null),(0,h.Z)(this,"containerRefHook",(e=>{this.targetElement=e,this.renderMarkdownIntoDiv()}))}renderMarkdownIntoDiv(){const{targetElement:e,props:t}=this;if(null===e)return;const{contents:n,createNode:o}=t,i=un(n),r=document.createRange();r.selectNodeContents(e),r.deleteContents();let l=null===o||void 0===o?void 0:o(i);if(void 0===l){const e=document.createElement("template");e.innerHTML=i,l=e.content}e.append(l);const a=e.getElementsByTagName("a");for(const s of a)s.target="_blank",s.rel="noreferrer noopener"}render(){return this.renderMarkdownIntoDiv(),o.createElement(dn,{ref:this.containerRefHook})}}var fn=n(31208);const pn=(0,we.z)("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[e=>e.targetWidth,"px"]}}),vn=e=>{const{value:t,onChange:n,forceEditMode:i,createNode:r,targetRect:l,onFinish:a,validatedSelection:s}=e,c=t.data,u=!0===t.readonly,[d,h]=o.useState(""===c||i),f=o.useCallback((()=>{h((e=>!e))}),[]),p=c?"gdg-ml-6":"";return d?o.createElement(pn,{targetWidth:l.width-20},o.createElement(fn.K,{autoFocus:!0,highlight:!1,validatedSelection:s,value:c,onKeyDown:e=>{"Enter"===e.key&&e.stopPropagation()},onChange:n}),o.createElement("div",{className:"gdg-edit-icon gdg-checkmark-hover ".concat(p),onClick:()=>a(t)},o.createElement(k.MC,null))):o.createElement(pn,{targetWidth:l.width},o.createElement(hn,{contents:c,createNode:r}),!u&&o.createElement(o.Fragment,null,o.createElement("div",{className:"spacer"}),o.createElement("div",{className:"gdg-edit-icon gdg-edit-hover ".concat(p),onClick:f},o.createElement(k.Wy,null))),o.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},gn={getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:""},kind:u.p6.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:d.k0,measure:(e,t,n)=>{const o=t.data.split("\n")[0];return e.measureText(o).width+2*n.cellHorizontalPadding},draw:e=>(0,d.uN)(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:i,onFinishedEditing:r,markdownDivCreateNode:l,forceEditMode:a,validatedSelection:s}=e;return o.createElement(vn,{onFinish:r,targetRect:i,value:n,validatedSelection:s,onChange:e=>t({...n,data:e.target.value}),forceEditMode:a,createNode:l})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},mn={getAccessibilityString:e=>e.row.toString(),kind:u.$o.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:function(e,t){const{ctx:n,theme:o}=e,i=o.markerFontFull,r=null!==t&&void 0!==t?t:{};(null===r||void 0===r?void 0:r.font)!==i&&(n.font=i,r.font=i);return r.deprep=wn,n.textAlign="center",r},measure:()=>44,draw:e=>function(e,t,n,o,i,r){const{ctx:l,rect:a,hoverAmount:s,theme:c}=e,{x:u,y:h,width:f,height:p}=a,v=n?1:"checkbox-visible"===o?.6+.4*s:s;if("number"!==o&&v>0){l.globalAlpha=v;const e=7*(n?s:1);if(Z(l,c,n,i?u+e:u,h,i?f-e:f,p,!0,void 0,void 0,18,"center",r),i){l.globalAlpha=s,l.beginPath();for(const e of[3,6])for(const t of[-5,-1,3])l.rect(u+e,h+p/2+t,2,2);l.fillStyle=c.textLight,l.fill(),l.beginPath()}l.globalAlpha=1}if("number"===o||"both"===o&&!n){const e=t.toString(),n=c.markerFontFull,i=u+f/2;"both"===o&&0!==s&&(l.globalAlpha=1-s),l.fillStyle=c.textLight,l.font=n,l.fillText(e,i,h+p/2+(0,d.aX)(l,n)),0!==s&&(l.globalAlpha=1)}}(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:o,posY:i}=e,{width:r,height:l}=t,a=n.drawHandle?7+(r-7)/2:r/2,s=l/2;if(Math.abs(o-a)<=10&&Math.abs(i-s)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function wn(e){const{ctx:t}=e;t.textAlign="start"}const bn={getAccessibilityString:()=>"",kind:u.$o.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>function(e,t,n){const{ctx:o,rect:i,hoverAmount:r,theme:l,spriteManager:a}=e,{x:s,y:c,width:u,height:h}=i;o.beginPath(),o.globalAlpha=r,o.rect(s+1,c+1,u,h-2),o.fillStyle=l.bgHeaderHovered,o.fill(),o.globalAlpha=1,o.beginPath();const f=""!==t;let p=0;if(void 0!==n){const e=8,t=h-e,i=s+e/2,u=c+e/2;a.drawSprite(n,"normal",o,i,u,t,l,f?1:r),p=t}else{p=24;const e=12,t=f?e:r*e,n=f?0:(1-r)*e*.5,i=l.cellHorizontalPadding+4;t>0&&(o.moveTo(s+i+n,c+h/2),o.lineTo(s+i+n+t,c+h/2),o.moveTo(s+i+n+.5*t,c+h/2-.5*t),o.lineTo(s+i+n+.5*t,c+h/2+.5*t),o.lineWidth=2,o.strokeStyle=l.bgIconHeader,o.lineCap="round",o.stroke())}o.fillStyle=l.textMedium,o.fillText(t,p+s+l.cellHorizontalPadding+.5,c+h/2+(0,d.aX)(o,l)),o.beginPath()}(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function yn(e,t,n,o,i,r,l){var a,s,c;e.textBaseline="alphabetic";const u=function(e,t,n,o,i){const r=o.cellHorizontalPadding,l=o.cellVerticalPadding;if(i)return{x:t.x+r/2,y:t.y+l/2+1,width:t.width-r,height:t.height-l-1};const a=(0,d.P7)(n,e,o.baseFontFull,"alphabetic"),s=t.height-l,c=Math.min(s,2.5*a.actualBoundingBoxAscent);return{x:t.x+r/2,y:t.y+(t.height-c)/2+1,width:a.width+3*r,height:c-1}}(e,i,o,t,null!==(a=null===n||void 0===n?void 0:n.fullSize)&&void 0!==a&&a);e.beginPath(),(0,d.NK)(e,u.x,u.y,u.width,u.height,null!==(s=t.roundingRadius)&&void 0!==s?s:4),e.globalAlpha=r,e.fillStyle=null!==(c=null===n||void 0===n?void 0:n.bgColor)&&void 0!==c?c:(0,C.fG)(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",null===l||void 0===l||l("text")}const xn=o.lazy((async()=>await n.e(6405).then(n.bind(n,16405)))),kn={getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:""},kind:u.p6.Number,needsHover:e=>!0===e.hoverEffect,needsHoverPosition:!1,useLabel:!0,drawPrep:d.k0,draw:e=>{const{hoverAmount:t,cell:n,ctx:o,theme:i,rect:r,overrideCursor:l}=e,{hoverEffect:a,displayData:s,hoverEffectTheme:c}=n;!0===a&&t>0&&yn(o,i,c,s,r,t,l),(0,d.uN)(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+2*n.cellHorizontalPadding,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:i,validatedSelection:r}=e;return o.createElement(o.Suspense,{fallback:null},o.createElement(xn,{highlight:t,disabled:!0===i.readonly,value:i.data,fixedDecimals:i.fixedDecimals,allowNegative:i.allowNegative,thousandSeparator:i.thousandSeparator,decimalSeparator:i.decimalSeparator,validatedSelection:r,onChange:e=>{var t;return n({...i,data:Number.isNaN(null!==(t=e.floatValue)&&void 0!==t?t:0)?0:e.floatValue})}}))},onPaste:(e,t,n)=>{var o;const i="number"===typeof n.rawValue?n.rawValue:Number.parseFloat("string"===typeof n.rawValue?n.rawValue:e);if(!Number.isNaN(i)&&t.data!==i)return{...t,data:i,displayData:null!==(o=n.formattedString)&&void 0!==o?o:t.displayData}}},Cn={getAccessibilityString:()=>"",measure:()=>108,kind:u.p6.Protected,needsHover:!1,needsHoverPosition:!1,draw:function(e){const{ctx:t,theme:n,rect:o}=e,{x:i,y:r,height:l}=o;t.beginPath();const a=2.5;let s=i+n.cellHorizontalPadding+a;const c=r+l/2,u=Math.cos((0,k.Ht)(30))*a,d=Math.sin((0,k.Ht)(30))*a;for(let h=0;h<12;h++)t.moveTo(s,c-a),t.lineTo(s,c+a),t.moveTo(s+u,c-d),t.lineTo(s-u,c+d),t.moveTo(s-u,c-d),t.lineTo(s+u,c+d),s+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()},onPaste:()=>{}};const Sn={getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:""},kind:u.p6.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>(0,d.k0)(e,t,e.theme.textLight),draw:e=>(0,d.uN)(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+2*n.cellHorizontalPadding,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:i,validatedSelection:r}=e;return o.createElement(fn.K,{highlight:t,autoFocus:!0!==i.readonly,disabled:!1!==i.readonly,value:i.data,validatedSelection:r,onChange:e=>n({...i,data:e.target.value})})},onPaste:()=>{}},Mn={getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:""},kind:u.p6.Text,needsHover:e=>!0===e.hoverEffect,needsHoverPosition:!1,drawPrep:d.k0,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:o,ctx:i,rect:r,theme:l,overrideCursor:a}=e,{displayData:s,contentAlign:c,hoverEffect:u,allowWrapping:h,hoverEffectTheme:f}=t;!0===u&&n>0&&yn(i,l,f,s,r,n,a),(0,d.uN)(e,s,c,h,o)},measure:(e,t,n)=>{const o=t.displayData.split("\n",!0===t.allowWrapping?void 0:1);let i=0;for(const r of o)i=Math.max(i,e.measureText(r).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:!0===e.allowWrapping,editor:t=>{const{isHighlighted:n,onChange:i,value:r,validatedSelection:l}=t;return o.createElement(fn.K,{style:!0===e.allowWrapping?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:!0!==r.readonly,disabled:!0===r.readonly,altNewline:!0,value:r.data,validatedSelection:l,onChange:e=>i({...r,data:e.target.value})})}}),onPaste:(e,t,n)=>{var o;return e===t.data?void 0:{...t,data:e,displayData:null!==(o=n.formattedString)&&void 0!==o?o:t.displayData}}},Rn=(0,we.z)("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),En=e=>{const{uri:t,onChange:n,forceEditMode:i,readonly:r,validatedSelection:l,preview:a}=e,[s,c]=o.useState(!r&&(""===t||i)),u=o.useCallback((()=>{c(!0)}),[]);return s?o.createElement(fn.K,{validatedSelection:l,highlight:!0,autoFocus:!0,value:t,onChange:n}):o.createElement(Rn,null,o.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},a),!r&&o.createElement("div",{className:"gdg-edit-icon",onClick:u},o.createElement(k.Wy,null)),o.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function In(e,t,n,o){let i=n.cellHorizontalPadding;const r=t.height/2-e.actualBoundingBoxAscent/2,l=e.width,a=e.actualBoundingBoxAscent;return"right"===o?i=t.width-l-n.cellHorizontalPadding:"center"===o&&(i=t.width/2-l/2),{x:i,y:r,width:l,height:a}}function Tn(e){var t;const{cell:n,bounds:o,posX:i,posY:r,theme:l}=e,a=null!==(t=n.displayData)&&void 0!==t?t:n.data;if(!0!==n.hoverEffect||void 0===n.onClickUri)return!1;const s=(0,d._y)(a,l.baseFontFull);if(void 0===s)return!1;const c=In(s,o,l,n.contentAlign);return P({x:c.x-4,y:c.y-4,width:c.width+8,height:c.height+8},i,r)}const On=[mn,bn,ut,ft,mt,Ct,Mt,gn,kn,Cn,Sn,Mn,{getAccessibilityString:e=>{var t,n;return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:""},kind:u.p6.Uri,needsHover:e=>!0===e.hoverEffect,needsHoverPosition:!0,useLabel:!0,drawPrep:d.k0,draw:e=>{var t;const{cell:n,theme:o,overrideCursor:i,hoverX:r,hoverY:l,rect:a,ctx:s}=e,c=null!==(t=n.displayData)&&void 0!==t?t:n.data,u=!0===n.hoverEffect;if(void 0!==i&&u&&void 0!==r&&void 0!==l){const t=In((0,d.P7)(c,s,o.baseFontFull),a,o,n.contentAlign),{x:u,y:h,width:f,height:p}=t;if(r>=u-4&&r<=u-4+f+8&&l>=h-4&&l<=h-4+p+8){const t=(0,d.aX)(s,o.baseFontFull);i("pointer");const r=5,l=h-t;s.beginPath(),s.moveTo(a.x+u,Math.floor(a.y+l+p+r)+.5),s.lineTo(a.x+u+f,Math.floor(a.y+l+p+r)+.5),s.strokeStyle=o.linkColor,s.stroke(),s.save(),s.fillStyle=e.cellFillColor,(0,d.uN)({...e,rect:{...a,x:a.x-1}},c,n.contentAlign),(0,d.uN)({...e,rect:{...a,x:a.x-2}},c,n.contentAlign),(0,d.uN)({...e,rect:{...a,x:a.x+1}},c,n.contentAlign),(0,d.uN)({...e,rect:{...a,x:a.x+2}},c,n.contentAlign),s.restore()}}s.fillStyle=u?o.linkColor:o.textDark,(0,d.uN)(e,c,n.contentAlign)},onSelect:e=>{Tn(e)&&e.preventDefault()},onClick:e=>{const{cell:t}=e;var n;Tn(e)&&(null===(n=t.onClickUri)||void 0===n||n.call(t,e))},measure:(e,t,n)=>{var o;return e.measureText(null!==(o=t.displayData)&&void 0!==o?o:t.data).width+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{var n;const{onChange:i,value:r,forceEditMode:l,validatedSelection:a}=t;return o.createElement(En,{forceEditMode:!0!==r.readonly&&(l||!0===e.hoverEffect&&void 0!==e.onClickUri),uri:r.data,preview:null!==(n=r.displayData)&&void 0!==n?n:r.data,validatedSelection:a,readonly:!0===r.readonly,onChange:e=>i({...r,data:e.target.value})})},onPaste:(e,t,n)=>{var o;return e===t.data?void 0:{...t,data:e,displayData:null!==(o=n.formattedString)&&void 0!==o?o:t.displayData}}}],Pn='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',Dn=e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="').concat(t,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="').concat(t,'"/>\n</svg>\n  ')},Hn={headerRowID:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'<rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="').concat(t,'"/></svg>')},headerNumber:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="').concat(t,'"/>\n  </svg>')},headerCode:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'<rect x="2" y="2" width="16" height="16" rx="4" fill="').concat(n,'"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="').concat(t,'"/></svg>')},headerString:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="').concat(t,'"/>\n</svg>')},headerBoolean:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n    <path\n        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"\n        fill="').concat(n,'"\n    />\n    <path\n        fill-rule="evenodd"\n        clip-rule="evenodd"\n        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"\n        fill="').concat(t,'"\n    />\n</svg>')},headerAudioUri:Dn,headerVideoUri:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="').concat(t,'"/>\n</svg>')},headerEmoji:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="').concat(t,'"/>\n    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="').concat(t,'"/>\n    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n  </svg>')},headerImage:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="').concat(t,'"/>\n</svg>')},headerUri:Dn,headerPhone:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <path fill="').concat(t,'" d="M3 3h14v14H3z"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="').concat(n,'"/>\n  </svg>')},headerMarkdown:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="').concat(t,'"/>\n  </svg>')},headerDate:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="').concat(t,'"/>\n</svg>')},headerTime:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="').concat(t,'"/>\n    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="').concat(t,'"/>\n  </svg>')},headerEmail:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="').concat(t,'"/>\n</svg>')},headerReference:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <rect x="2" y="8" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <rect x="8" y="4" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="').concat(t,'"/>\n  </svg>')},headerIfThenElse:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path fill="').concat(t,'" d="M4 3h12v14H4z"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="').concat(n,'"/>\n</svg>')},headerSingleValue:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="').concat(t,'"/>\n  </svg>')},headerLookup:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="').concat(t,'"/>\n  </svg>')},headerTextTemplate:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="').concat(t,'"/>\n</svg>')},headerMath:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="').concat(t,'"/>\n  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="').concat(t,'"/>\n</svg>')},headerRollup:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="').concat(t,'"/>\n  </svg>')},headerJoinStrings:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="').concat(t,'"/>\n</svg>')},headerSplitString:e=>{const t=e.fgColor,n=e.bgColor;return"\n    ".concat(Pn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="').concat(t,'"/>\n  </svg>')},headerGeoDistance:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="').concat(t,'"/>\n</svg>')},headerArray:e=>{const t=e.fgColor,n=e.bgColor;return"".concat(Pn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="').concat(t,'"/>\n</svg>')},rowOwnerOverlay:e=>{const t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>')},protectedColumnOverlay:e=>{const t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>\n')},renameIcon:e=>{const t=e.bgColor;return"".concat(Pn,'\n    <path stroke="').concat(t,'" stroke-width="2" d="M12 3v14"/>\n    <path stroke="').concat(t,'" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>\n    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="').concat(t,'"/>\n  </svg>\n')}};var zn=n(19266);const Ln=[];const Fn=class extends w{constructor(){super(...arguments),(0,h.Z)(this,"imageLoaded",(()=>{})),(0,h.Z)(this,"loadedLocations",[]),(0,h.Z)(this,"cache",{}),(0,h.Z)(this,"sendLoaded",zn((()=>{this.imageLoaded(new y(this.loadedLocations)),this.loadedLocations=[]}),20)),(0,h.Z)(this,"clearOutOfWindow",(()=>{const e=Object.keys(this.cache);for(const t of e){const e=this.cache[t];let n=!1;for(let t=0;t<e.cells.length;t++){const o=e.cells[t];if(this.isInWindow(o)){n=!0;break}}n?e.cells=e.cells.filter(this.isInWindow):(e.cancel(),delete this.cache[t])}}))}setCallback(e){this.imageLoaded=e}loadImage(e,t,n,o){var i;let r=!1;const l=null!==(i=Ln.pop())&&void 0!==i?i:new Image;let a=!1;const s={img:void 0,cells:[p(t,n)],url:e,cancel:()=>{a||(a=!0,Ln.length<12?Ln.unshift(l):r||(l.src=""))}},c=new Promise((e=>l.addEventListener("load",(()=>e(null)))));requestAnimationFrame((async()=>{try{l.src=e,await c,await l.decode();const t=this.cache[o];if(void 0!==t&&!a){t.img=l;for(const e of t.cells)this.loadedLocations.push(m(e));r=!0,this.sendLoaded()}}catch{s.cancel()}})),this.cache[o]=s}loadOrGetImage(e,t,n){const o=e,i=this.cache[o];if(void 0!==i){const e=p(t,n);return i.cells.includes(e)||i.cells.push(e),i.img}this.loadImage(e,t,n,o)}},An=o.forwardRef(((e,t)=>{const n=o.useMemo((()=>({...Hn,...e.headerIcons})),[e.headerIcons]),i=o.useMemo((()=>{var t;return null!==(t=e.imageWindowLoader)&&void 0!==t?t:new Fn}),[e.imageWindowLoader]);return o.createElement(st,{...e,renderers:On,headerIcons:n,ref:t,imageWindowLoader:i})}))},84256:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(64649),i=n(66845);class r extends i.PureComponent{constructor(){super(...arguments),(0,o.Z)(this,"wrapperRef",i.createRef()),(0,o.Z)(this,"clickOutside",(e=>{if((!this.props.isOutsideClick||this.props.isOutsideClick(e))&&null!==this.wrapperRef.current&&!this.wrapperRef.current.contains(e.target)){let t=e.target;for(;null!==t;){if(t.classList.contains("click-outside-ignore"))return;t=t.parentElement}this.props.onClickOutside()}}))}componentDidMount(){document.addEventListener("touchend",this.clickOutside,!0),document.addEventListener("mousedown",this.clickOutside,!0),document.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){document.removeEventListener("touchend",this.clickOutside,!0),document.removeEventListener("mousedown",this.clickOutside,!0),document.removeEventListener("contextmenu",this.clickOutside,!0)}render(){const{onClickOutside:e,isOutsideClick:t,...n}=this.props;return i.createElement("div",{...n,ref:this.wrapperRef},this.props.children)}}},85735:(e,t,n)=>{"use strict";n.d(t,{NH:()=>c,dF:()=>r,fG:()=>l,mv:()=>s});const o={};let i=null;function r(e){const t=e.toLowerCase().trim();if(void 0!==o[t])return o[t];i=i||function(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}(),i.style.color="#000",i.style.color=t;const n=getComputedStyle(i).color;i.style.color="#fff",i.style.color=t;const r=getComputedStyle(i).color;if(r!==n)return[0,0,0,1];let l=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return l.length<4&&l.push(1),l=l.map((e=>Number.isNaN(e)?0:e)),o[t]=l,l}function l(e,t){const[n,o,i]=r(e);return"rgba(".concat(n,", ").concat(o,", ").concat(i,", ").concat(t,")")}const a=new Map;function s(e,t){const n="".concat(e,"-").concat(t),o=a.get(n);if(void 0!==o)return o;const i=c(e,t);return a.set(n,i),i}function c(e,t){if(void 0===t)return e;const[n,o,i,l]=r(e);if(1===l)return e;const[a,s,c,u]=r(t),d=l+u*(1-l),h=(l*o+u*s*(1-l))/d,f=(l*i+u*c*(1-l))/d;return"rgba(".concat((l*n+u*a*(1-l))/d,", ").concat(h,", ").concat(f,", ").concat(d,")")}},67930:(e,t,n)=>{"use strict";n.d(t,{$o:()=>h,DP:()=>w,EV:()=>x,Qo:()=>m,Sq:()=>f,T9:()=>v,kf:()=>b,p6:()=>c,pN:()=>d,qF:()=>a,rL:()=>p,rs:()=>g,sd:()=>s});var o=n(64649),i=n(63674),r=n(82781);let l;const a=null,s=void 0;var c,u,d,h;function f(e){return"width"in e&&"number"===typeof e.width}async function p(e){return"object"===typeof e?e:await e()}function v(e){return e.kind!==c.Loading&&e.kind!==c.Bubble&&e.kind!==c.RowID&&e.kind!==c.Protected&&e.kind!==c.Drilldown&&((0,i.NG)(e),!0)}function g(e){return e.kind===h.Marker||e.kind===h.NewRow}function m(e){return!(!v(e)||e.kind===c.Image)&&(e.kind===c.Text||e.kind===c.Number||e.kind===c.Markdown||e.kind===c.Uri||e.kind===c.Custom||e.kind===c.Boolean?!0!==e.readonly:void(0,i.vE)(e,"A cell was passed with an invalid kind"))}function w(e){return r(e,"editor")}function b(e){var t;return!(null!==(t=e.readonly)&&void 0!==t&&t)}let y;!function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"}(c||(c={})),function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"}(u||(u={})),function(e){e.Triangle="triangle",e.Dots="dots"}(d||(d={})),function(e){e.NewRow="new-row",e.Marker="marker"}(h||(h={})),l=Symbol.iterator;class x{constructor(e){(0,o.Z)(this,"items",void 0),this.items=e}offset(e){if(0===e)return this;const t=this.items.map((t=>[t[0]+e,t[1]+e]));return new x(t)}add(e){const t="number"===typeof e?[e,e+1]:e,n=function(e){if(0===e.length)return[];const t=[...e],n=[];t.sort((function(e,t){return e[0]-t[0]})),n.push([...t[0]]);for(const o of t.slice(1)){const e=n[n.length-1];e[1]<o[0]?n.push([...o]):e[1]<o[1]&&(e[1]=o[1])}return n}([...this.items,t]);return new x(n)}remove(e){const t=[...this.items],n="number"===typeof e?e:e[0],o="number"===typeof e?e+1:e[1];for(const[i,r]of t.entries()){const[e,l]=r;if(e<=o&&n<=l){const r=[];e<n&&r.push([e,n]),o<l&&r.push([o,l]),t.splice(i,1,...r)}}return new x(t)}first(){if(0!==this.items.length)return this.items[0][0]}last(){if(0!==this.items.length)return this.items.slice(-1)[0][1]-1}hasIndex(e){for(let t=0;t<this.items.length;t++){const[n,o]=this.items[t];if(e>=n&&e<o)return!0}return!1}hasAll(e){for(let t=e[0];t<e[1];t++)if(!this.hasIndex(t))return!1;return!0}some(e){for(const t of this)if(e(t))return!0;return!1}equals(e){if(e===this)return!0;if(e.items.length!==this.items.length)return!1;for(let t=0;t<this.items.length;t++){const n=e.items[t],o=this.items[t];if(n[0]!==o[0]||n[1]!==o[1])return!1}return!0}toArray(){const e=[];for(const[t,n]of this.items)for(let o=t;o<n;o++)e.push(o);return e}get length(){let e=0;for(const[t,n]of this.items)e+=n-t;return e}*[l](){for(const[e,t]of this.items)for(let n=e;n<t;n++)yield n}}(0,o.Z)(x,"empty",(()=>{var e;return null!==(e=y)&&void 0!==e?e:y=new x([])})),(0,o.Z)(x,"fromSingleSelection",(e=>x.empty().add(e)))},39806:(e,t,n)=>{"use strict";n.d(t,{H1:()=>w,Sb:()=>f,Ve:()=>j,vr:()=>z,Ld:()=>Z,uN:()=>N,L6:()=>F,oK:()=>C,ih:()=>k,WA:()=>V,YN:()=>x,_y:()=>O,aX:()=>P,pV:()=>S,G6:()=>y,pZ:()=>d,PU:()=>h,X4:()=>p,pU:()=>v,P7:()=>T,k0:()=>L,zU:()=>g,zu:()=>W,NK:()=>B,NZ:()=>u});var o=n(965),i=n(66845),r=new Map,l=new Map,a=new Map;function s(e,t,n,o){var i,r;let s=l.get(n);if(o&&void 0!==s&&s.count>2e4){let o=a.get(n);if(void 0===o&&(o=function(e,t){var n;let o=new Map,i=0;for(let s of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let t=e.measureText(s).width;o.set(s,t),i+=t}let r=i/o.size,l=(t/r+3)/4,a=o.keys();for(let s of a)o.set(s,(null!=(n=o.get(s))?n:r)*l);return o}(e,s.size),a.set(n,o)),s.count>5e5){let e=0;for(let n of t)e+=null!=(i=o.get(n))?i:s.size;return 1.01*e}let r=e.measureText(t);return function(e,t,n,o,i){var r,l,a;let s=0,c={};for(let d of e)s+=null!=(r=n.get(d))?r:i,c[d]=(null!=(l=c[d])?l:0)+1;let u=t-s;for(let d of Object.keys(c)){let e=c[d],t=null!=(a=n.get(d))?a:i,r=t+u*(t*e/s)*o/e;n.set(d,r)}}(t,r.width,o,Math.max(.05,1-s.count/2e5),s.size),l.set(n,{count:s.count+t.length,size:s.size}),r.width}let c=e.measureText(t),u=c.width/t.length;if((null!=(r=null==s?void 0:s.count)?r:0)>2e4)return c.width;if(void 0===s)l.set(n,{count:t.length,size:u});else{let e=u-s.size,o=t.length/(s.count+t.length),i=s.size+e*o;l.set(n,{count:s.count+t.length,size:i})}return c.width}function c(e,t,n,o,i,r,l,a){if(t.length<=1)return t.length;if(i<n)return-1;let c=Math.floor(n/i*r),u=s(e,t.slice(0,Math.max(0,c)),o,l),d=null==a?void 0:a(t);if(u!==n)if(u<n){for(;u<n;)c++,u=s(e,t.slice(0,Math.max(0,c)),o,l);c--}else for(;u>n;){let n=void 0!==d?0:t.lastIndexOf(" ",c-1);n>0?c=n:c--,u=s(e,t.slice(0,Math.max(0,c)),o,l)}if(" "!==t[c]){let e=0;if(void 0===d)e=t.lastIndexOf(" ",c);else for(let t of d){if(t>c)break;e=t}e>0&&(c=e)}return c}function u(e,t){return i.useMemo((()=>e.map(((e,n)=>({group:e.group,grow:e.grow,hasMenu:e.hasMenu,icon:e.icon,id:e.id,menuIcon:e.menuIcon,overlayIcon:e.overlayIcon,sourceIndex:n,sticky:n<t,indicatorIcon:e.indicatorIcon,style:e.style,themeOverride:e.themeOverride,title:e.title,trailingRowOptions:e.trailingRowOptions,width:e.width,growOffset:e.growOffset,rowMarker:e.rowMarker,rowMarkerChecked:e.rowMarkerChecked,headerRowMarkerTheme:e.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:e.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:e.headerRowMarkerDisabled})))),[e,t])}function d(e,t){const[n,o]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(o))return!0;if(void 0!==e.current){if(v(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const e of i)if(n>=e.x&&n<e.x+e.width&&o>=e.y&&o<e.y+e.height)return!0}return!1}function h(e,t){return(null!==e&&void 0!==e?e:"")===(null!==t&&void 0!==t?t:"")}function f(e,t,n){return void 0!==n.current&&(e[1]===n.current.cell[1]&&(void 0===t.span?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]))}function p(e,t){const[n,o]=e;return n>=t.x&&n<t.x+t.width&&o>=t.y&&o<t.y+t.height}function v(e,t){return(null===e||void 0===e?void 0:e[0])===(null===t||void 0===t?void 0:t[0])&&(null===e||void 0===e?void 0:e[1])===(null===t||void 0===t?void 0:t[1])}function g(e){return[e.x+e.width-1,e.y+e.height-1]}function m(e,t,n){const o=n.x,i=n.x+n.width-1,r=n.y,l=n.y+n.height-1,[a,s]=e;if(s<r||s>l)return!1;if(void 0===t.span)return a>=o&&a<=i;const[c,u]=t.span;return c>=o&&c<=i||u>=o&&c<=i||c<o&&u>i}function w(e,t,n,o){let i=0;if(void 0===n.current)return i;const r=n.current.range;(o||r.height*r.width>1)&&m(e,t,r)&&i++;for(const l of n.current.rangeStack)m(e,t,l)&&i++;return i}function b(e,t){let n=e;if(void 0!==t){let o=[...e];const i=n[t.src];t.src>t.dest?(o.splice(t.src,1),o.splice(t.dest,0,i)):(o.splice(t.dest+1,0,i),o.splice(t.src,1)),o=o.map(((t,n)=>({...t,sticky:e[n].sticky}))),n=o}return n}function y(e,t){let n=0;const o=b(e,t);for(let i=0;i<o.length;i++){const e=o[i];if(!e.sticky)break;n+=e.width}return n}function x(e,t,n){if("number"===typeof n)return t*n;{let o=0;for(let i=e-t;i<e;i++)o+=n(i);return o}}function k(e,t,n,o,i){const r=b(e,o),l=[];for(const c of r){if(!c.sticky)break;l.push(c)}if(l.length>0)for(const c of l)n-=c.width;let a=t,s=null!==i&&void 0!==i?i:0;for(;s<=n&&a<r.length;)s+=r[a].width,a++;for(let c=t;c<a;c++){const e=r[c];e.sticky||l.push(e)}return l}function C(e,t,n){let o=0;for(const i of t){if(e<=(i.sticky?o:o+(null!==n&&void 0!==n?n:0))+i.width)return i.sourceIndex;o+=i.width}return-1}function S(e,t,n,o,i,r,l,a,s,c){const u=o+i;if(n&&e<=i)return-2;if(e<=u)return-1;let d=t;for(let p=0;p<c;p++){const t=r-1-p;if(d-="number"===typeof l?l:l(t),e>=d)return t}const h=r-c,f=e-(null!==s&&void 0!==s?s:0);if("number"===typeof l){const e=Math.floor((f-u)/l)+a;if(e>=h)return;return e}{let e=u;for(let t=a;t<h;t++){const n=l(t);if(f<=e+n)return t;e+=n}return}}let M=0,R={};const E="undefined"===typeof window;function I(e,t,n,o){return"".concat(e,"_").concat(null!==o&&void 0!==o?o:null===t||void 0===t?void 0:t.font,"_").concat(n)}function T(e,t,n){const o=I(e,t,arguments.length>3&&void 0!==arguments[3]?arguments[3]:"middle",n);let i=R[o];return void 0===i&&(i=t.measureText(e),R[o]=i,M++),M>1e4&&(R={},M=0),i}function O(e,t){const n=I(e,void 0,"middle",t);return R[n]}function P(e,t){return"string"!==typeof t&&(t=t.baseFontFull),function(e,t){for(const r of H)if(r.key===t)return r.val;const n=D(e,"alphabetic"),o=D(e,"middle"),i=-(o.actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return H.push({key:t,val:i}),i}(e,t)}function D(e,t){e.save(),e.textBaseline=t;const n=e.measureText("ABCDEFGHIJKLMNOPQRSTUVWXYZ");return e.restore(),n}!async function(){var e,t;E||void 0===(null===(e=document)||void 0===e||null===(t=e.fonts)||void 0===t?void 0:t.ready)||(await document.fonts.ready,M=0,R={},r.clear(),a.clear(),l.clear())}();const H=[];function z(e,t,n,o,i,r){const{ctx:l,rect:a,theme:s}=e;let c=Number.MAX_SAFE_INTEGER;if(void 0!==t&&(c=n-t,c<500)){const e=1-c/500;l.globalAlpha=e,l.fillStyle=s.bgSearchResult,l.fillRect(a.x+1,a.y+1,a.width-(i?2:1),a.height-(r?2:1)),l.globalAlpha=1,void 0!==o&&(o.fillStyle=s.bgSearchResult)}return c<500}function L(e,t,n){const{ctx:o,theme:i}=e,r=null!==t&&void 0!==t?t:{},l=null!==n&&void 0!==n?n:i.textDark;return l!==r.fillStyle&&(o.fillStyle=l,r.fillStyle=l),r}function F(e,t,n){const{rect:o,ctx:i,theme:r}=e;i.fillStyle=r.textDark,N({ctx:i,rect:o,theme:r},t,n)}function A(e,t,n,o,i,r,l,a,s){"right"===s?e.fillText(t,n+i-(a.cellHorizontalPadding+.5),o+r/2+l):"center"===s?e.fillText(t,n+i/2,o+r/2+l):e.fillText(t,n+a.cellHorizontalPadding+.5,o+r/2+l)}function V(e,t){const n=T("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function _(e,t,n,o,i,a,u,d,h,f){const p=d.baseFontFull,v=function(e,t,n,o,i,a){let u="".concat(t,"_").concat(n,"_").concat(o,"px"),d=r.get(u);if(void 0!==d)return d;if(o<=0)return[];let h=[],f=t.split("\n"),p=l.get(n),v=void 0===p?t.length:o/p.size*1.5,g=i&&void 0!==p&&p.count>2e4;for(let r of f){let t=s(e,r.slice(0,Math.max(0,v)),n,g),i=Math.min(r.length,v);if(t<=o)h.push(r);else{for(;t>o;){let l=c(e,r,o,n,t,i,g,a),u=r.slice(0,Math.max(0,l));r=r.slice(u.length),h.push(u),t=s(e,r.slice(0,Math.max(0,v)),n,g),i=Math.min(r.length,v)}t>0&&h.push(r)}}return h=h.map(((e,t)=>0===t?e.trimEnd():e.trim())),r.set(u,h),r.size>500&&r.delete(r.keys().next().value),h}(e,t,p,i-2*d.cellHorizontalPadding,null!==f&&void 0!==f&&f),g=V(e,p),m=d.lineHeight*g,w=g+m*(v.length-1),b=w+d.cellVerticalPadding>a;b&&(e.save(),e.rect(n,o,i,a),e.clip());const y=o+a/2-w/2;let x=Math.max(o+d.cellVerticalPadding,y);for(const r of v)if(A(e,r,n,x,i,g,u,d,h),x+=m,x>o+a)break;b&&e.restore()}function N(e,t,n,i,r){var l;const{ctx:a,rect:s,theme:c}=e,{x:u,y:d,width:h,height:f}=s;(i=null!==(l=i)&&void 0!==l&&l)||(t=function(e,t){e.includes("\n")&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}(t,h));const p=P(a,c),v="rtl"===(0,o.o7)(t);if(void 0===n&&v&&(n="right"),v&&(a.direction="rtl"),t.length>0){let e=!1;"right"===n?(a.textAlign="right",e=!0):void 0!==n&&"left"!==n&&(a.textAlign=n,e=!0),i?_(a,t,u,d,h,f,p,c,n,r):A(a,t,u,d,h,f,p,c,n),e&&(a.textAlign="start"),v&&(a.direction="inherit")}}function B(e,t,n,o,i,r){"number"===typeof r&&(r={tl:r,tr:r,br:r,bl:r}),r={tl:Math.max(0,Math.min(r.tl,i/2,o/2)),tr:Math.max(0,Math.min(r.tr,i/2,o/2)),bl:Math.max(0,Math.min(r.bl,i/2,o/2)),br:Math.max(0,Math.min(r.br,i/2,o/2))},e.moveTo(t+r.tl,n),e.arcTo(t+o,n,t+o,n+r.tr,r.tr),e.arcTo(t+o,n+i,t+o-r.br,n+i,r.br),e.arcTo(t,n+i,t,n+i-r.bl,r.bl),e.arcTo(t,n,t+r.tl,n,r.tl)}function Z(e,t,n){const o=1.25;e.arc(t,n-4.375,o,0,2*Math.PI,!1),e.arc(t,n,o,0,2*Math.PI,!1),e.arc(t,n+4.375,o,0,2*Math.PI,!1)}function W(e,t,n){const o=function(e,t){const n=t.x-e.x,o=t.y-e.y,i=Math.sqrt(n*n+o*o),r=n/i,l=o/i;return{x:n,y:t.y-e.y,len:i,nx:r,ny:l,ang:Math.atan2(l,r)}};let i;const r=t.length;let l=t[r-1];for(let a=0;a<r;a++){let s=t[a%r];const c=t[(a+1)%r],u=o(s,l),d=o(s,c),h=u.nx*d.ny-u.ny*d.nx,f=u.nx*d.nx-u.ny*-d.ny;let p=Math.asin(h<-1?-1:h>1?1:h),v=1,g=!1;f<0?p<0?p=Math.PI+p:(p=Math.PI-p,v=-1,g=!0):p>0&&(v=-1,g=!0),i=void 0!==s.radius?s.radius:n;const m=p/2;let w,b=Math.abs(Math.cos(m)*i/Math.sin(m));b>Math.min(u.len/2,d.len/2)?(b=Math.min(u.len/2,d.len/2),w=Math.abs(b*Math.sin(m)/Math.cos(m))):w=i;let y=s.x+d.nx*b,x=s.y+d.ny*b;y+=-d.ny*w*v,x+=d.nx*w*v,e.arc(y,x,w,u.ang+Math.PI/2*v,d.ang-Math.PI/2*v,g),l=s,s=c}e.closePath()}function j(e,t,n,o,i,r,l,a,s,c,u,d,f,p,v){const g={x:0,y:r+c,width:0,height:0};if(e>=p.length||t>=u||t<-2||e<0)return g;const m=r-i;if(e>=d){const t=l>e?-1:1,n=y(p);g.x+=n+s;for(let o=l;o!==e;o+=t)g.x+=p[1===t?o:o-1].width*t}else for(let h=0;h<e;h++)g.x+=p[h].width;if(g.width=p[e].width+1,-1===t)g.y=i,g.height=m;else if(-2===t){g.y=0,g.height=i;let t=e;const o=p[e].group,r=p[e].sticky;for(;t>0&&h(p[t-1].group,o)&&p[t-1].sticky===r;){const e=p[t-1];g.x-=e.width,g.width+=e.width,t--}let l=e;for(;l+1<p.length&&h(p[l+1].group,o)&&p[l+1].sticky===r;){const e=p[l+1];g.width+=e.width,l++}if(!r){const e=y(p),t=g.x-e;t<0&&(g.x-=t,g.width+=t),g.x+g.width>n&&(g.width=n-g.x)}}else if(t>=u-f){let e=u-t;for(g.y=o;e>0;){const n=t+e-1;g.height="number"===typeof v?v:v(n),g.y-=g.height,e--}g.height+=1}else{const e=a>t?-1:1;if("number"===typeof v){const e=t-a;g.y+=e*v}else for(let n=a;n!==t;n+=e)g.y+=v(n)*e;g.height=("number"===typeof v?v:v(t))+1}return g}},31208:(e,t,n)=>{"use strict";n.d(t,{K:()=>u});var o=n(66845),i=n(74559);const r=(0,i.z)("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),l=(0,i.z)("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),a=(0,i.z)("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});var s=n(63674);let c=0;const u=e=>{const{placeholder:t,value:n,onKeyDown:i,highlight:u,altNewline:d,validatedSelection:h,...f}=e,{onChange:p,className:v}=f,g=o.useRef(null),m=null!==n&&void 0!==n?n:"";(0,s.hu)(void 0!==p,"GrowingEntry must be a controlled input area");const[w]=o.useState((()=>"input-box-"+(c=(c+1)%1e7)));o.useEffect((()=>{const e=g.current;if(null===e)return;if(e.disabled)return;const t=m.toString().length;e.focus(),e.setSelectionRange(u?0:t,t)}),[]),o.useLayoutEffect((()=>{if(void 0!==h){var e;const t="number"===typeof h?[h,null]:h;null===(e=g.current)||void 0===e||e.setSelectionRange(t[0],t[1])}}),[h]);const b=o.useCallback((e=>{"Enter"===e.key&&e.shiftKey&&!0===d||null===i||void 0===i||i(e)}),[d,i]);return o.createElement(a,{className:"gdg-growing-entry"},o.createElement(l,{className:v},m+"\n"),o.createElement(r,{...f,className:(null!==v&&void 0!==v?v:"")+" gdg-input",id:w,ref:g,onKeyDown:b,value:m,placeholder:t,dir:"auto"}))}}}]);