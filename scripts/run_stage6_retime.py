# scripts/run_stage6_retime.py

from src.subtitle_editor.subtitle_retimer import retime_subtitles
from src.utils.srt_utils import save_srt_file

# 模拟改写后的字幕
mock_subtitles = [
    {"index": 1, "content": "宇宙的起源令人着迷。"},
    {"index": 2, "content": "黑洞是密度极高的天体。"},
    {"index": 3, "content": "科学家正努力揭开宇宙的秘密。"}
]

# 模拟 TTS 音频实际时长（秒）
mock_audio_durations = [2.5, 3.2, 4.0]

# 生成新时间码字幕
retimed_subs = retime_subtitles(mock_subtitles, mock_audio_durations)

# 输出 SRT 文件
output_path = "data/outputs/retimed_subtitles.srt"
save_srt_file(retimed_subs, output_path)

print(f"✅ 已保存新字幕文件至: {output_path}")
