# scripts/run_stage3_rewrite.py

import os
from dotenv import load_dotenv
from src.utils.srt_utils import parse_srt, write_srt as save_srt
from src.subtitle_editor.deepseek_rewriter import rewrite_subtitles_with_mapping
from src.subtitle_editor.mapping_utils import save_mapping as save_mapping_as_json

# 加载环境变量
load_dotenv()

# 参数配置
input_srt = "data/uploads/010.srt"
output_srt = "data/outputs/rewritten_subtitles.srt"
mapping_json = "data/outputs/subtitle_mapping.json"
prompt_path = "prompts/rewrite_prompt.txt"

# 读取字幕
subtitles = parse_srt(input_srt)

# 加载改写提示词模板
with open(prompt_path, "r", encoding="utf-8") as f:
    prompt_template = f.read()

# 调用改写并建立映射
new_subs, mapping = rewrite_subtitles_with_mapping(subtitles, prompt_template)

# 保存新字幕与映射关系
save_srt(new_subs, output_srt)
save_mapping_as_json(mapping, mapping_json)

print(f"✅ 改写字幕已保存至: {output_srt}")
print(f"✅ 映射关系已保存至: {mapping_json}")
