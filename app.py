import streamlit as st
import os
from dotenv import load_dotenv

# ✅ Load environment variables from .env file
load_dotenv()

# ✅ Import custom modules
from src.utils.srt_utils import parse_srt, write_srt
from src.subtitle_editor.deepseek_rewriter import rewrite_subtitles
from src.subtitle_editor.mapping_utils import save_mapping
from src.subtitle_editor.subtitle_retimer import retimer
from src.video_splitter.segment_video import segment_video
from src.video_editor.final_composer import compose_video
from src.tts_providers.tts_aliyun import synthesize_with_cosyvoice

st.title("AI Video Editor")

# ✅ Ensure directories exist
os.makedirs("data/uploads", exist_ok=True)
os.makedirs("data/outputs", exist_ok=True)
os.makedirs("data/outputs/segments", exist_ok=True)
os.makedirs("data/outputs/audio", exist_ok=True)

# Stage 1: Upload
st.header("1. Upload Video and Subtitles")
video_file = st.file_uploader("Upload Video File", type=["mp4", "avi", "mov", "mkv"])
srt_file = st.file_uploader("Upload Subtitle File (SRT)", type=["srt"])
if video_file and srt_file:
    video_path = os.path.join("data/uploads", video_file.name)
    srt_path = os.path.join("data/uploads", srt_file.name)
    with open(video_path, "wb") as f:
        f.write(video_file.getbuffer())
    with open(srt_path, "wb") as f:
        f.write(srt_file.getbuffer())
    st.success("Uploaded successfully!")

    # Stage 2: Subtitle Rewriting
    st.header("2. Rewrite Subtitles")
    if st.button("Rewrite Subtitles"):
        rewritten_srt = os.path.join("data/outputs", "rewritten_subtitles.srt")
        mapping_json = os.path.join("data/outputs", "subtitle_mapping.json")
        try:
            rewrite_subtitles(srt_path, rewritten_srt, mapping_json)
            st.success("Subtitles rewritten.")
            with open(rewritten_srt, "rb") as f:
                st.download_button("Download Rewritten Subtitles", f, file_name="rewritten_subtitles.srt")
            with open(mapping_json, "r", encoding="utf-8") as f:
                st.download_button("Download Subtitle Mapping", f, file_name="subtitle_mapping.json")
        except Exception as e:
            st.error(f"Error: {e}")

    # Stage 3: Mapping 已在上一阶段完成
    st.header("3. Subtitle Mapping")
    st.info("Mapping has been saved during rewriting step.")

    # Stage 4: Segment Video
    st.header("4. Segment Video by Subtitles")
    if st.button("Segment Video"):
        try:
            segment_video(video_path, srt_path, "data/outputs/segments")
            st.success("Video segmented.")
            for seg in sorted(os.listdir("data/outputs/segments")):
                if seg.endswith(".mp4"):
                    st.write(seg)
        except Exception as e:
            st.error(f"Error: {e}")

    # Stage 5: Voiceover with TTS
    st.header("5. Generate Voiceover (Aliyun TTS)")
    if st.button("Generate Voiceover"):
        rewritten_srt = os.path.join("data/outputs", "rewritten_subtitles.srt")
        if not os.path.exists(rewritten_srt):
            st.error("Please rewrite subtitles first.")
        else:
            try:
                subtitles = parse_srt(rewritten_srt)
                for i, entry in enumerate(subtitles, start=1):
                    text = entry["text"]
                    audio_path = os.path.join("data/outputs/audio", f"audio_{i}.mp3")
                    synthesize_with_cosyvoice(text, audio_path)
                st.success("Voiceover generated.")
            except Exception as e:
                st.error(f"Error: {e}")

    # Stage 6: Retiming
    st.header("6. Retiming Subtitles to Voiceover")
    if st.button("Retimer Subtitles"):
        rewritten_srt = os.path.join("data/outputs", "rewritten_subtitles.srt")
        new_srt_output = os.path.join("data/outputs", "new_timed_subtitles.srt")
        if not os.path.exists(rewritten_srt):
            st.error("Rewritten subtitles missing.")
        else:
            try:
                retimer(rewritten_srt, "data/outputs/audio", new_srt_output)
                st.success("Retiming complete.")
                with open(new_srt_output, "rb") as f:
                    st.download_button("Download Retimed Subtitles", f, file_name="new_timed_subtitles.srt")
            except Exception as e:
                st.error(f"Error: {e}")

    # Stage 7: Compose Final Video
    st.header("7. Compose Final Video")
    if st.button("Compose Final Video"):
        final_video_path = os.path.join("data/outputs", "final_video.mp4")
        timed_sub_path = os.path.join("data/outputs", "new_timed_subtitles.srt")
        try:
            compose_video(
                segments_dir="data/outputs/segments",
                audio_dir="data/outputs/audio",
                output_path=final_video_path,
                subtitle_path=timed_sub_path
            )
            st.success("Final video composed.")
            with open(final_video_path, "rb") as f:
                st.download_button("Download Final Video", f, file_name="final_video.mp4")
        except Exception as e:
            st.error(f"Error: {e}")
