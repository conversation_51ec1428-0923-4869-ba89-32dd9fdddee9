import os
import openai
from src.subtitle_editor.deepseek_rewriter import rewrite_subtitles_with_mapping
from src.utils.srt_utils import Subtitle, merge_subtitles_into_blocks

# 测试数据
test_subtitles = [
    Subtitle(index=1, start="00:00:01,000", end="00:00:03,000", text="Hello world"),
    Subtitle(index=2, start="00:00:04,000", end="00:00:06,000", text="This is a test"),
    Subtitle(index=3, start="00:00:07,000", end="00:00:09,000", text="Another test case"),
]
if __name__ == "__main__":
    print("测试DeepSeek重写功能...")
    try:
        print("1. 测试API模型列表...")
        client = openai.OpenAI(
            api_key='sk-afeed833a6fe4e86aaeebf61327801cc',
            base_url='https://api.deepseek.com/'
        )
        print("可用模型:", [m.id for m in client.models.list().data])

        print("\n2. 测试重写功能...")
        new_texts, mapping = rewrite_subtitles_with_mapping(
            subtitles=test_subtitles,
            prompt_template="请将以下文本简化为10字以内：{text}",
            max_block_size=1  # 改为单条测试
        )

        if not new_texts:
            print("警告：未获取到改写结果")
        else:
            print("\n改写结果：")
            for i, text in enumerate(new_texts):
                print(f"原字幕: {test_subtitles[i].text}")
                print(f"改写后: {text}\n")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        raise